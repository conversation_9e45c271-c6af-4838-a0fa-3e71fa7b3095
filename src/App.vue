<script>
import {boot, onBooted} from './lib/bootstrap.js';
import {initReferral} from './lib/context.js';

export default {
    onLaunch() {
        //Bootstrap
        onBooted(() => console.log("App booted!"));
        setTimeout(boot, 0);

        initReferral();
    },
    onShow() {
        console.log('App Show')
    },
    onHide() {
        console.log('App Hide')
    }
}
</script>

<style>
/*每个页面公共css */


/* 空数据页面 */
.no-data-nomal-box {
    position: relative;
    text-align: center;
}

.ndnb-icon image {
    width: 500rpx;
}

.ndnb-tip {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 100rpx;
    font-size: 28rpx;
    text-align: center;
    color: #999;
}

/* button页面 */
button {
    background-color: transparent;
    border: 0;
    padding: 0;
    margin: 0;
    line-height: 1.6;
}

button::after {
    display: none;
}

/* 信息红点 */
.p-rel {
    position: relative;
}

.nomal-badge {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 20rpx;
    height: 20rpx;
    background-color: #f54a45;
    border-radius: 100%;
    margin-left: 70rpx;
    margin-top: -16rpx;
}

/** 去除横向滚动条 */
.navScroll {
    white-space: nowrap;
}
.navScroll ::-webkit-scrollbar {
    display: block;
    width: 4px !important;
    height: 1px !important;
    overflow: auto !important;
    background: transparent !important;
}
</style>
