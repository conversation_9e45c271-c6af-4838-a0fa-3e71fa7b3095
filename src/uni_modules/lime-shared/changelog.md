## 0.1.4（2023-09-05）
- feat: 增加 Hooks `useIntersectionObserver`
- feat: 增加 `floatAdd`
- feat: 因为本人插件兼容 vue2 需要使用 `composition-api`，故增加vue文件代码插件的条件编译
## 0.1.3（2023-08-13）
- feat: 增加 `camelCase`
## 0.1.2（2023-07-17）
- feat: 增加 `getClassStr`
## 0.1.1（2023-07-06）
- feat: 增加 `isNumeric`， 区别于 `isNumber`
## 0.1.0（2023-06-30）
- fix: `clamp`忘记导出了
## 0.0.9（2023-06-27）
- feat: 增加`arrayBufferToFile`
## 0.0.8（2023-06-19）
- feat: 增加`createAnimation`、`clamp`
## 0.0.7（2023-06-08）
- chore: 更新注释
## 0.0.6（2023-06-08）
- chore: 增加`createImage`为`lime-watermark`和`lime-qrcode`提供依赖
## 0.0.5（2023-06-03）
- chore: 更新注释
## 0.0.4（2023-05-22）
- feat: 增加`range`,`exif`,`selectComponent`
## 0.0.3（2023-05-08）
- feat: 增加`fillZero`,`debounce`,`throttle`,`random`
## 0.0.2（2023-05-05）
- chore: 更新文档
## 0.0.1（2023-05-05）
- 无
