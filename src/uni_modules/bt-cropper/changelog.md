## 3.0.2（2023-06-06）
更新了文档

## 3.0.1（2022-11-03）
修复 撤销和重做不生效的问题
## 3.0.0（2022-11-03）
使用wxs重构代码，性能大提升
新增 支持蒙版裁剪，可以裁剪任何形状的图形（详情见demo示例）
新增 支持在弹窗中使用（详情见demo示例）
移除 由于插槽会导致许多问题，实际上开发者自己封装组件反而更简单，所以3.0版本以后移除插槽，2.0迁移教程见 demo:全屏裁剪
## 2.0.3（2022-08-21）
修复 在vue3 程序中报错的问题
新增 新增了图片初始化完成和加载失败的事件
## 2.0.2（2022-08-18）
新增 增加了原像素裁剪功能，即使用用户在裁剪框取景的大小作为输出像素，换句话说，输出的图片分辨率与输入图片分辨率一样
新增 增加了change事件，会在图像和裁剪框位置变化后触发
新增 增加了compress参数 压缩图片，压缩图片是为了提升流畅度，所以只会针对用户拖动的那张图片进行压缩，最终输出的图像品质并不会受到影响
修复 用户在没有传入图像时报错的问题
修复 ios在某些机型上拖动出现残留的问题
## 2.0.1（2022-07-20）
修复：ios打包成app的时候有几率裁剪不成功的问题
## 2.0.0（2022-07-13）
更新了2.0版本，增加了图片放大功能
## bt-cropper 图片裁切
========
### 2022年7月13日 发布2.0版本
* 1.完全重构了代码，并且优化了性能
* 2.支持app
* 3.修复demo在H5的情况下高度错误的问题
