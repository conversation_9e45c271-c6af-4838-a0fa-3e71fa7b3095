<template>
    <view class="related-doc-box" v-if="list.length > 0 && type === 'doc'">
        <view class="rdl-tit">相关资料</view>
        <view class="related-doc-list">
            <view class="rdl-item" v-for="item in list" :key="item.sid" @click="goDetail(item)">
                <doc-icon :format="item.resource?.format" />
                <text>{{ item.title }}</text>
            </view>
        </view>
    </view>
    <view class="related-resources-box" v-if="list.length > 0 && type === 'rich_text'">
        <view class="rrb-tit"><text>相关资源</text></view>
        <view class="docs-list-box">
            <view class="dlb-item" v-for="item in list" :key="item.sid" @click="goDetail(item)">
                <view class="dlbi-img">
                    <doc-icon :format="item.resource?.format" />
                </view>
                <view class="dlbi-cnt">
                    <view class="dlbic-tit">{{ item.title }}</view>
                    <view class="dlbic-foot">
                        <view class="dlbicf-time">时间：<uni-dateformat format="yyyy-MM-dd" :date="item.release_at"></uni-dateformat></view>
                        <view class="dlbicf-pn" v-if="item.resource?.page_count">页数：{{ item.resource?.page_count }}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import api from "@/lib/api";
    import DocIcon from "@/components/doc-icon.vue";
    import {alert, getDetailUrl} from "@/lib/utils";

    export default {
        components: {DocIcon},
        props: {
            sid: String,
            type: String
        },
        data() {
            return {
                list: []
            }
        },
        mounted() {
            this.getRelation()
        },
        methods: {
            getRelation() {
                api.get("cms/related-contents/" + this.sid).then(res => {
                    this.list = res
                }).catch(err => {
                    alert(err.message)
                })
            },
            goDetail(data) {
                let url = getDetailUrl(data.type_label, data.sid)
                if (url === '') {
                    alert("未获取到详情地址")
                    return
                }
                uni.navigateTo({
                    url: url
                })
            }
        }
    }
</script>

<style>
    .related-doc-box {
        background-color: #fff;
        border-radius: 12rpx;
        padding-bottom: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .rdl-tit {
        padding: 30rpx 30rpx 10rpx;
        font-size: 28rpx;
        color: #999;
    }
    .rdl-item {
        display: flex;
        align-items: center;
        height: 88rpx;
        padding: 0 30rpx;
    }
    .rdl-item image {
        width: 38rpx;
        height: 38rpx;
    }
    .rdl-item text {
        flex: 1;
        padding-left: 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
    }


    .rrb-tit {
        position: relative;
        height: 100rpx;
        line-height: 100rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
    }
    .rrb-tit text {
        display: inline-block;
        position: relative;
    }
    .rrb-tit text::after {
        content: '';
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        bottom: 24rpx;
        width: 124rpx;
        height: 10rpx;
        background-color: #390ABC;
    }

    .docs-list-box {
        position: relative;
        background-color: #fff;
        border-radius: 12rpx;
    }
    .dlb-top {
        position: fixed;
        left: 0;
        right: 0;
        top: 0;
        z-index: 2;
        background-color: #f3f3f3;
        padding: 30rpx 30rpx 0;
    }
    .dlbs-empty-block {
        height: 260rpx;
    }
    .dlb-item {
        display: flex;
        align-items: center;
        padding-bottom: 30rpx;
    }
    .dlbic-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        font-weight: bold;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .dlbic-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
    .dlbicf-time {
        padding-right: 30rpx;
    }

    .dlbi-img {
        position: relative;
        width: 140rpx;
        height: 140rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        border: 1px solid #e7e7e7;
    }
    .dlbi-cnt {
        flex: 1;
    }
</style>