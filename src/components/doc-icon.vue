<!--资料列表图标组件，之所以单独搞一个组件是为了保持元素对图片的引用，从而在打包时能动态识别用到的引用，而不是放 static 里一股脑全打包-->
<template>
    <image src="../images/icon/file-word-fill.png" class="doc-type" v-if="format=='word'"></image>
    <image src="../images/icon/file-excel-fill.png" class="doc-type" v-else-if="format=='excel'"></image>
    <image src="../images/icon/file-ppt-fill.png" class="doc-type" v-else-if="format=='ppt'"></image>
    <image src="../images/icon/file-pdf-2-fill.png" class="doc-type" v-else-if="format=='pdf'"></image>
    <image src="../images/icon/file-video-fill-2.png" class="doc-type" v-else-if="format=='video'"></image>
    <image src="../images/icon/file-word-fill.png" class="doc-type" v-else></image>
</template>

<script>
export default {
    props: {
        format: String
    }
}
</script>