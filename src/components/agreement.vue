<!--同意协议组件-->
<template>
    <view class="lcb-foot">
        <checkbox-group @change="changeAgree">
            <checkbox value="agree" color="#FFCC33" style="transform:scale(0.7)" :checked="agreed" />
        </checkbox-group>
        <view class="lcbf-txt">请阅读并同意<navigator url="/pages/about/agreement">用户协议</navigator>和<navigator url="/pages/about/privacy">隐私政策</navigator></view>
    </view>


    <uni-popup ref="popup" type="center">
        <view class="warn-tip-box">
            <view class="wtb-title">温馨提示</view>
            <view class="wtb-cnt">请阅读并同意{{ appName }} <navigator url="/pages/about/agreement">《用户协议》</navigator>和<navigator url="/pages/about/privacy">《隐私政策》</navigator></view>
            <view class="wtb-foot">
                <view class="wtbc-btn cancle" @click="$refs.popup.close()">取消</view>
                <button class="wtbc-btn agree" @click="postAgree">同意</button>
            </view>
        </view>
    </uni-popup>
</template>

<script>
import { getAppName } from '@/lib/context.js';

export default {
    data() {
        return {
            agreed: false,
            appName: ""
        }
    },
    mounted() {
        this.appName = getAppName();
    },
    methods: {
        changeAgree(e) {
            this.agreed = e.detail.value.includes("agree");
        },
        postAgree() {
            this.agreed = true;
            this.$refs.popup.close();
            this.$emit("confirm-agree");
        },
        check() {
            if (!this.agreed) {
                this.$refs.popup.open();
            }
            return this.agreed;
        }
    }
}
</script>
<style>
    /* 弹框 */
    .warn-tip-box {
        background-color: #fff;
        width: 560rpx;
        border-radius: 40rpx;
    }
    .warn-tip-box .wtb-title {
        text-align: center;
        padding: 30rpx 0;
        font-weight: 700;
    }
    .warn-tip-box .wtb-cnt {
        padding: 30rpx;
        font-size: 32rpx;
        line-height: 1.6;
        text-align: center;
    }
    .warn-tip-box .wtb-cnt navigator {
        display: inline-block;
        color: #065cdf;
    }
    .wtb-foot {
        display: flex;
        border-top: 1px solid #f3f3f3;
        margin-top: 30rpx;
    }
    .wtb-foot .wtbc-btn {
        flex: 1;
        text-align: center;
        height: 100rpx;
        line-height: 100rpx;
        border-right: 1px solid #f3f3f3;
        color: #999;
    }
    .wtb-foot .wtbc-btn:last-child {
        border-right: none;
    }
    .wtb-foot .wtbc-btn.agree {
        color: #333333;
        background: transparent;
        border-radius: 0 0 40rpx 0;
        border: none;
        padding: 0;
    }
    .wtb-foot .wtbc-btn.agree::after {
        border: none;
    }
    .lcb-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        margin-top: 200rpx;
        padding-bottom: 80rpx;
    }

    .lcbf-txt navigator {
        display: inline-block;
        color: #390ABC;
        text-decoration: underline;
        padding: 0 8rpx;
    }
</style>