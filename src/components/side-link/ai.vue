<template>
	<view class="more-tools-box helper" :class="{ 'mtb-close': toolsClose }" @click="goTools()">
		<image src="/static/images/public/ai-helper.png"></image>
		<view class="text-part">
			<text>安全</text>
			<text>助手</text>
		</view>
	</view>
</template>

<script>
import { useUserStore } from "@/store/user";
import { mapState } from "pinia";
import { loginRequired } from "@/lib/login";

export default {
	data() {
		return {
			toolsClose: false,
		}
	},
    computed: {
        ...mapState(useUserStore, ['user']),
    },
    created() {
        this.closeTools();
    },
	methods: {
		goTools() {
            if (this.user) {
                uni.navigateTo({
                    url: "/packageA/ai/index"
                });
            } else {
                loginRequired();
            }
		},
		closeTools() {
			setTimeout(() => {
				this.toolsClose = true;
			}, 5000);
		}
	}
}
</script>


<style>
.more-tools-box {
	transition: all cubic-bezier(0.075, 0.82, 0.165, 1) .3s;
	display: flex;
	align-items: center;
	position: fixed;
	right: -10rpx;
	top: 700rpx;
	padding: 10rpx;
	border-radius: 80rpx 0 0 80rpx;
	background-color: #FFFFFF;
	box-shadow: 0 0 50rpx rgba(0,0,0,.1);
}
.more-tools-box.helper {
	top: 700rpx;
}

.more-tools-box image {
	width: 70rpx;
	height: 70rpx;
	vertical-align: top;
}

.text-part {
	padding: 0 20rpx 0 10rpx;
}
.text-part text {
	display: block;
	font-size: 24rpx;
}
.more-tools-box.mtb-close {
	right: -90rpx;
	opacity: .8;
}
</style>
