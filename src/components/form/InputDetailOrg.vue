<template>
    <template>
        <template v-if="type == 'group'">
            <view :class="{'ss-group-box': keyIndex > 0}">
                <view class="ssgb-tit">{{ title }}</view>
            </view>
        </template>
        <template v-else-if="type == 'text'">
            <view class="single-txt">
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                <view class="st-input">
                    <input :value="value" @input="inputValue" :type="inputType()" :maxlength="inputMaxLen()"
                           :placeholder="placeholder"/>
                </view>
            </view>
        </template>
        <template v-else-if="type == 'select'">
            <view class="single-txt">
                <picker   @change="bindPickerChange" :value="selectIndex" :range="options.options">
                    <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>

                    <view class="dropdown-part" >
                        <text>{{ selectText }}</text>
                        <image src="../../images/icon/arrow-down-s-line.png"></image>
                    </view>
                </picker>
            </view>
        </template>
        <template v-else-if="type == 'region'">
            <view class="single-txt">
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                <view class="st-data">
                    {{ value.province }}-{{ value.city }} <template v-if="value.district">-{{ value.district }}</template>
                </view>
            </view>
        </template>
        <template v-else-if="type == 'radio'">
            <picker  @change="bindPickerChange" :value="selectIndex" :range="options.options">
                <view class="single-txt">
                    <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                    <view  class="dropdown-part" >
                        <text>{{ selectText }}</text>
                        <image src="../../images/icon/arrow-down-s-line.png"></image>
                    </view>
                </view>
            </picker>
        </template>
        <template v-else-if="type == 'date'">
            <uni-datetime-picker type="date" :clear-icon="false" @change="changePicker">
                <view class="single-txt">
                    <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                    <view class="st-data">{{ value }}</view>
                </view>
            </uni-datetime-picker>
        </template>
        <template v-else-if="type == 'checkbox'">
            <view class="single-cb">
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                <view class="st-checkbox">
                    <checkbox-group @change="groupChange">
                        <checkbox v-for="(item, index) in options.options" color="#390ABC" :key="index" :value="item"
                                  :checked="checkboxChecked(item)">{{ item }}
                        </checkbox>
                    </checkbox-group>
                </view>
            </view>
        </template>
        <template v-else-if="type == 'textarea'">
            <view class="mtextarea">
                <view class="mt-tit">{{ title }}<text v-if="isRequired">*</text>
                </view>
                <view class="mt-cnt">
                    <textarea :value="value" @input="inputValue" :maxlength="inputMaxLen()"
                              :placeholder="placeholder"/>
                </view>
            </view>
        </template>
        <template v-else-if="type == 'photo'">
            <view class="single-photo" @click="openFile">
                <view>
                    <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                    <view class="sp-tips">{{ descText }}</view>
                </view>
                <view class="st-sign">

                    <image class="sign-image" v-if="value" :src="value.value" mode="widthFix"></image>
                    <text v-else>点击上传</text>
                    <image src="/src/images/icon/arrow-right-wide-line.png"></image>
                </view>
            </view>
        </template>
        <template v-else-if="type == 'sign'">
            <view class="single-photo" @click="openSign()">
                <view class="st-name">{{ title }}<text v-if="isRequired">*</text></view>
                <view class="st-sign">
                    <image class="sign-image" v-if="value" :src="value" mode="widthFix"></image>
                    <text  v-else>点击签名</text>
                    <image src="/src/images/icon/arrow-right-wide-line.png"></image>
                </view>
            </view>
        </template>
        <template v-else-if="type == 'file' || type == 'image' || type == 'pic'">
            <view class="upload-file">
                <view class="uf-tit">{{ title }}<text>*</text></view>
                <view class="uf-tips">{{ descText }}</view>

                <view class="adidci-doc-block" v-for="(item, index) in value" :key="index">
                    <view class="adidcidb-l">
                        <image src="@/images/icon/file-excel-fill.png" v-if="getFormatType(item.filename) == 'excel'">
                        </image>
                        <image src="@/images/icon/file-word-fill.png"
                               v-else-if="getFormatType(item.filename) == 'word'"></image>
                        <image src="@/images/icon/file-pdf-2-fill.png"
                               v-else-if="getFormatType(item.filename) == 'pdf'"></image>
                        <image src="@/images/icon/file-ppt-fill.png" v-else-if="getFormatType(item.filename) == 'ppt'"
                               mode=""></image>
                        <image src="@/images/icon/file-zip-fill.png" v-else-if="getFormatType(item.filename) == 'zip'"
                               mode=""></image>
                        <image src="@/images/icon/file-video-fill-2.png"
                               v-else-if="getFormatType(item.filename) == 'video'" mode=""></image>
                        <image src="@/images/icon/file-image-fill.png"
                               v-else-if="getFormatType(item.filename) == 'image'" mode=""></image>
                        <view class="adidcidbl-txt">
                            <view class="adidcidblt-tit">{{ item.filename }}</view>
                            <view class="adidcidblt-size">{{ getFormatType(item.filename) }}</view>
                        </view>
                    </view>
                    <view class="adidcidb-r" @click="delFile(index)">
                        <image src="@/images/icon/close-line.png"></image>
                    </view>

                </view>
                <view class="uf-upbtn" v-if="fileMax" @click="openFile">
                    <image src="@/images/icon/upload-2-line.png"></image>
                    <text>上传文件</text>
                </view>
            </view>
        </template>
    </template>
    <uni-popup ref="signContent" type="center">
        <view class="sign-edit-box">
            <view class="seb-cnt">
                <l-signature disableScroll penColor="#000000" ref="signatureRef" penSize="5"
                             :openSmooth="true"></l-signature>
            </view>
            <view class="seb-foot">
                <button @click="onClick('clear')">关闭</button>
                <button @click="onClick('undo')">撤消</button>
                <button @click="onClick('save')" class="save">保存</button>
            </view>
        </view>
    </uni-popup>
    <uni-popup ref="cropperCropper" type="center" :mask-click="false">
        <view class="crop-image-box">
            <view class="cib-tit">图片裁剪</view>
            <view class="cib-cnt">
                <bt-cropper v-if="showCropper" :rotate="rotate" :ratio="5/7" ref="cropper" :imageSrc="imageSrc"></bt-cropper>
            </view>
            <view class="cib-foot">
                <view class="cibf-l">
                    <button class="cibf-btn" @click="cropperRotate">旋转</button>
                    <button class="cibf-btn" @click="resetCropperImg">重置</button>
                </view>
                <view class="cibf-r">
                    <button @click="$refs.cropperCropper.close()" class="cibf-btn cibfb-cancle">关闭</button>
                    <button @click="cropperImg" class="cibf-btn cibfb-submit">确认裁剪</button>
                </view>
            </view>
        </view>
    </uni-popup>
</template>

<script>
import {
    alert,
    getFormatType
} from "@/lib/utils";
import api from "@/lib/api";
import File_img from "@/pages/services/form/file_img.vue";

export default {
    components: {
        File_img
    },
    props: {
        keyIndex: Number,
        status: Number,
        id: String,
        title: String,
        placeholder: String,
        orgSid: String,
        reject_reason: String,
        desc: String,
        type: String,
        value: [String, Array, Boolean],
        isRequired: Boolean,
        options: Object
    },
    data() {
        return {
            uploadForm: {},
            binds: {},
            fileData: {},
            dataValue: '',
            singAgree: false,
            imageSrc: false,
            showCropper: true,
            rotate: 0,
        }
    },
    created() {
        if (this.type == 'date' && this.value && !this.dataValue) {
            this.dataValue = this.value;
        }
    },
    computed: {
        fileMax() {
            if (!this.value) {
                return true
            }
            console.log('options?.limit', this.value?.length,  this.options?.limit)
            if (this.value?.length < this.options?.limit) {
                return true
            }
            return false;
        },
        selectIndex() {
            let index = this.options?.options.findIndex(res => res == this.value)
            if (index >= 0) {
                return index
            }
            return ""
        },
        selectText() {
            let index = this.options?.options.findIndex(res => res == this.value)
            if (index >= 0) {
                return this.options?.options[index]
            }
            return "请选择"
        },
        descText() {
            if (this.desc) {
                return this.desc
            }
            let desc = ''
            let mb = ''
            if (this.type == 'file' || this.type == 'image') {
                if (this.options) {
                    mb = this.getSize(this.options)
                }

                if (this.type == 'image') {
                    desc += "支持图片,"
                } else {
                    if (this.options?.ext.find(res => res == '*')) {
                        desc += "文件类型不限,"
                    } else {
                        desc += "文件类型支持"
                        if (this.options?.ext.find(res => res == 'doc')) {
                            desc += "文档、"
                        }
                        if (this.options?.ext.find(res => res == 'archive')) {
                            desc += "压缩包、"
                        }
                        if (this.options?.ext.find(res => res == 'image')) {
                            desc += "图片、"
                        }
                        if (this.options?.ext.find(res => res == 'video')) {
                            desc += "视频、"
                        }
                    }
                }
                desc = desc.slice(0, -1)

                if (mb) {
                    desc += "，文件大小" + mb + "M以内"
                }
            }
            return desc
        }
    },
    mounted() {
        if (this.value) {
            this.$emit('inputUpdate', {
                value: this.value,
                key: this.keyIndex
            })
        }
    },
    emits: ['update:value', 'inputUpdate', 'inputUpdateDelFiles',],
    methods: {
        inputValue(e) {
            this.$emit('inputUpdate', {
                value: e.detail.value,
                key: this.keyIndex
            })
        },
        inputType() {
            if (this.options?.mask) {
                return this.options?.mask
            }
            return "text"
        },
        inputMaxLen() {
            if (this.options?.maxlen) {
                return this.options.maxlen;
            }
            return -1;
        },
        inputDesc() {
            if (this.desc) {
                return this.desc
            }
            return "请输入" + this.title;
        },
        async openFile() {
            //this.uploadConfig()
            let itemList = ["从微信聊天选择", "从手机相册选择", "拍摄"]
            this.uploadConfig()
            uni.showActionSheet({
                itemList: itemList,
                success: res => {
                    if (res.tapIndex == 0) {
                        wx.chooseMessageFile({
                            count: this.options?.limit, //默认100
                            type: 'all',
                            success: async res => {
                                await this.uploadAllFiles(res.tempFiles);
                            }
                        });
                    } else if (res.tapIndex == 1) {
                        let mediaType = ['image', 'video'];

                        uni.chooseMedia({
                            count: this.options?.limit,
                            sourceType: ['album'],
                            mediaType: mediaType,
                            success: async res => {
                                console.log(res)
                                await this.uploadAllFiles(res.tempFiles);
                            }
                        })
                    } else if (res.tapIndex == 2) {
                        let mediaType = ['image', 'video'];

                        uni.chooseMedia({
                            count: this.options?.limit,
                            sourceType: ['camera'],
                            mediaType: mediaType,
                            success: async res => {
                                console.log(res)
                                await this.uploadAllFiles(res.tempFiles);
                            }
                        })
                    }
                }
            })
        },
        getSize(data) {
            let kb = 0
            if (data?.filesize) {
                kb = data.filesize
            }
            if (data?.size) {
                kb = data?.size;
            }
            let mb = Math.floor(kb / 1024)
            console.log(mb)
            return mb
        },
        initData() {
            return new Promise((resolve, reject) => {

                api.get(`orgs/${this.orgSid}/upload-config?field_id=${this.id}`).then(res => {
                    this.uploadForm = res
                    resolve()
                }).catch(err => {
                    alert(err.message)
                    reject()
                })
            })
        },
        openSign() {
            if (!this.value && !this.singAgree) {
                uni.showModal({
                    title: this.title,
                    content: this.placeholder,
                    success: (res) => {
                        if (res.confirm) {
                            this.singAgree = true
                            this.$refs.signContent.open()
                        }
                    }
                })
            } else {
                this.$refs.signContent.open()
            }
        },
        uploadConfig() {
            api.get(`orgs/${this.orgSid}/upload-config?field_id=${this.id}`).then(res => {
                this.uploadForm = res
            }).catch(err => {
                alert(err.message)
            })
        },
        judgeSize(size) {
            size = size / 1024
            if (size >= this.uploadForm.max_size_kb) {
                return "图片大小不能超过" +  this.uploadForm.max_size_kb / 1024 + "M"
            }
            return false
        },
        async uploadAllFiles(res) {

            if (this.type == 'photo') {

                let src = res[0].path ? res[0].path : res[0].tempFilePath
                if (this.judgeSize(res[0].size)) {
                    alert(this.judgeSize(res[0].size))
                    return
                }
                this.imageSrc = src
                this.$refs.cropperCropper.open()
                return
            }

            for (const item of res) {
                if (this.judgeSize(item.size)) {
                    alert(this.judgeSize(item.size))
                    return
                }
                await this.initData().then(() => {
                    if (item?.path) {
                        this.uploadFile(item.path)
                    } else if (item?.tempFilePath) {
                        this.uploadFile(item.tempFilePath)
                    }
                }) // 等待每个文件上传完成
            }
        },
        groupChange(e) {
            this.$emit('inputUpdate', {
                value: e.detail.value,
                key: this.keyIndex
            })
        },
        checkboxChecked(data) {
            if (!this.value) {
                return false
            }
            let index = this.value.findIndex(res => res == data)
            console.log(index)
            if (index >= 0) {
                return true
            }
            return false
        },
        async uploadFile(path) {
            uni.showLoading({
                title: '上传中'
            });
            try {
                const uploadFileRes = await uni.uploadFile({
                    url: this.uploadForm.url,
                    filePath: path,
                    formData: this.uploadForm.form_params,
                    name: this.uploadForm.name
                });

                // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换
                let data = JSON.parse(uploadFileRes.data);
                if (data?.key) {
                    let value = {};
                    if (this.type == 'photo') {
                        uni.showLoading({
                            title: '去背景中'
                        });
                        this.photoBack(data)
                        return
                    } else {
                        value = this.value ? [...this.value] : [];
                        value.push(data);
                    }

                    this.$emit('inputUpdate', {
                        value: value,
                        key: this.keyIndex
                    });
                } else {
                    alert('上传失败');
                }
            } catch (error) {
                uni.showModal({
                    title: '上传失败',
                    content: error.error || '上传失败',
                    duration: 2000
                });
            } finally {
                if (this.type != 'photo') {
                    uni.hideLoading();
                }
            }
        },
        photoBack(data) {
            uni.showLoading({
                title: '去背景中'
            });
            api.post("orgs/enrollments/to-id-photo", {photo: data.key}).then(res => {
                 let value = {
                    key: res.key,
                    value: res.url,
                }
                this.$emit('inputUpdate', {
                    value: value,
                    key: this.keyIndex
                });
                uni.hideLoading();
            }).catch(error => {
                let value = {
                    key: data.key,
                    value: data.url,
                }
                this.$emit('inputUpdate', {
                    value: value,
                    key: this.keyIndex
                });
                uni.hideLoading();
                //alert(error.message);
            })
        },
        bindPickerChange(e) {
            let value = e.detail.value
            this.$emit('inputUpdate', {
                value: this.options.options[value],
                key: this.keyIndex
            })
        },
        radioPickerChange(e) {
            console.log(this.options)
            let value = e.detail.value
            this.$emit('inputUpdate', {
                value: this.options.options[value],
                key: this.keyIndex
            })
        },
        changePicker(e) {
            this.$emit('inputUpdate', {
                value: e,
                key: this.keyIndex
            })
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        delFile(index) {
            if (this.type == 'file' || this.type == 'image' || this.type == 'pic') {
                console.log(this.value)
                if (this.value[index].key) {
                    let value = [...this.value]
                    value.splice(index, 1)
                    this.$emit('inputUpdate', {
                        value: value,
                        key: this.keyIndex
                    })
                } else {
                    this.$emit('inputUpdateDelFiles', {
                        value: this.value[0].path,
                        index: this.keyIndex
                    })
                }
            } else {
                this.$emit('inputUpdate', {
                    value: [],
                    key: this.keyIndex
                })
            }
        },
        onClick(type) {

            if (type == 'clear') {
                this.$refs.signContent.close()
                this.$refs.signatureRef[type]()
                return;
            }
            if (type == 'save') {
                this.$refs.signatureRef.canvasToTempFilePath({
                    success: (res) => {
                        // 是否为空画板 无签名
                        if (res.isEmpty) {
                            uni.showToast({
                                title: '请先签名',
                                icon: 'none'
                            })
                            return
                        }
                        // 生成图片的临时路径
                        // H5 生成的是base64
                        this.$emit('inputUpdate', {
                            value: res.tempFilePath,
                            key: this.keyIndex
                        })
                        this.$refs.signContent.close()

                    }
                })
                return
            }
            if (this.$refs.signatureRef)
                this.$refs.signatureRef[type]()
        },
        cropperImg() {
            this.$refs.cropper.crop().then((res)=>{
                console.log(res)
                this.uploadFile(res)
                this.$refs.cropperCropper.close()
            })
        },
        cropperRotate() {
            if (this.rotate + 90 >= 360) {
                this.rotate = 0
            } else {
                this.rotate = this.rotate + 90
            }
        },
        resetCropperImg() {
            this.showCropper = false
            this.$nextTick( () => {
                this.rotate = 0
                this.showCropper = true
            })
        }
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.ssgb-tit {
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #999;
    padding: 0 30rpx;
}

.ss-group-box {
    margin-top: 30rpx;
}

/* 单选、多选 */
.dropdown-part {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100rpx;
    justify-content: flex-end;
}

.dropdown-part text {
    padding-right: 20rpx;
    color: #999;
    font-size: 28rpx;
}

.dropdown-part image {
    width: 32rpx;
    height: 32rpx;
}

.single-cb {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 0 0 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.st-checkbox {
    flex: 1;
    padding-top: 20rpx;
}

.st-checkbox checkbox {
    padding-right: 40rpx;
    padding-bottom: 20rpx;
}

/* 单行文本 */
.single-txt {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    background-color: #fff;
    padding: 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.st-input {
    flex: 1;
}

.st-input input {
    height: 100rpx;
    line-height: 100rpx;
}

.st-unit {
    font-size: 24rpx;
    color: #999;
}

.st-name {
    width: 200rpx;
}

.st-name text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

.ssgb-cnt .single-txt {
    margin-bottom: 1px;
}

.ssgb-cnt .upload-file {
    margin-bottom: 1px;
}

/* 多行文本 */
.mtextarea {
    position: relative;
    margin-bottom: 30rpx;
}

.mt-tit {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 30rpx;
    color: #999;
    font-size: 28rpx;
}

.mt-tit text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

.mt-cnt {
    display: flex;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.mt-cnt textarea {
    flex: 1;
    padding: 30rpx;
    font-size: 28rpx;
}

/* 上传框 */
.upload-file {
    position: relative;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    padding-bottom: 1px;
    margin-bottom: 1rpx;
}

.uf-tit {
    font-size: 28rpx;
    padding: 30rpx 30rpx 0;
}

.uf-tit text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

.uf-tips {
    font-size: 24rpx;
    color: #999;
    padding: 10rpx 30rpx 20rpx;
}

.uf-upbtn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    color: #390ABC;
}

.uf-upbtn image {
    width: 32rpx;
    height: 32rpx;
}

.uf-upbtn text {
    font-size: 28rpx;
    padding-left: 10rpx;
    font-weight: bold;
}

/* 上传后文件样式 */
.adidci-doc-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e7e7e7;
    border-radius: 12rpx;
    padding: 20rpx;
}

.adidcidbl-txt {
    padding-left: 20rpx;
}

.adidcidb-l {
    display: flex;
    align-items: center;
}

.adidcidb-l image {
    width: 64rpx;
    height: 64rpx;
}

.adidcidblt-tit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300rpx;
    font-size: 28rpx;
}

.adidcidblt-size {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}

.adidcidb-r image {
    width: 32rpx;
    height: 32rpx;
}

.upload-file .adidci-doc-block {
    margin: 0 30rpx 30rpx;
    background-color: #F3F3FF;
    border: none;
}

.uf-review-tip {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 24rpx;
    background-color: red;
    color: #fff;
    padding: 4rpx 8rpx;
}

.uf-review-tip.pass {
    background-color: #07c160;
}


/* 底部按钮 */
.sic-foot {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 15rpx 15rpx env(safe-area-inset-bottom);
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.sicf-btn {
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #390abc;
    color: #fff;
    border-radius: 12rpx;
}

.sicf-btn image {
    width: 36rpx;
    height: 36rpx;
}

.sicf-btn text {
    font-weight: bold;
}

.sic-foot-eb {
    height: 100rpx;
    padding-bottom: env(safe-area-inset-bottom);
}


.st-sign {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 10px 0;
}

.st-sign image {
    width: 32rpx;
    height: 32rpx;
}

.st-sign image.sign-image {
    width: 200rpx;
    margin-right: 10px;
}

/* 签名框 */
.sign-edit-box {
    width: 600rpx;
    background-color: #fff;
    border-radius: 20rpx;
    padding: 20rpx;
}

.seb-cnt {
    border: 1px solid #e7e7e7;
}

.seb-foot {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 20rpx;
}

.seb-foot button {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 20px;
    margin: 0 10rpx;
    background-color: #f3f3f3;
    font-size: 28rpx;
}

.seb-foot button.save {
    background-color: #390ABC;
    color: #fff;
}


/* 图片 */
.single-photo {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.sp-tips {
    font-size: 24rpx;
    color: #999;
    max-width: 300rpx;
    padding-top: 10rpx;
}


/* 裁剪框 */
.crop-image-box {
    width: 100%;
    height: 100%;
    background-color: #000;
    border-radius: 12rpx;
}
.cib-tit {
    color: #fff;
    text-align: center;
    padding: 30rpx 0;
    font-size: 28rpx;
}
.cib-cnt {
    width: 700rpx;
    height: 600rpx;
}
.cib-foot {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cibf-l,
.cibf-r {
    display: flex;
    align-items: center;
}
.cibf-btn {
    background-color: rgba(255, 255, 255, .05);
    color: #fff;
    height: 88rpx;
    line-height: 88rpx;
    padding: 0 40rpx;
    border-radius: 12rpx;
    font-size: 28rpx;
    margin-right: 20rpx;
}
.cibf-btn.cibfb-submit {
    background-color: #067cdf;
    color: #fff;
    margin-right: 0;
}
.cibf-btn.cibfb-cancle {
    background-color: transparent;
}
</style>