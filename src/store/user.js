import { defineStore } from "pinia";
import api, {getToken, setToken} from "../lib/api.js";
import { ref } from "vue";

/**
 * @typedef {{uuid: String, nickname: String, avatar: String, phone: String, status: Number, credit: Number}} User 用户信息
 */

/**
 * 当前登录用户关键状态
 */
export const useUserStore = defineStore('user', () => {
    /**
     * 用户信息
     * @type {import("vue").Ref<User|null>}
     */
    const user = ref(null);

    const orgData = ref(null);

    //用户信息是否已加载，在未加载前，user 都不能用来代表用户登录状态
    const loaded = ref(false);

    /**
     * 重新加载用户信息
     * @return {Promise}
     */
    async function reload() {
        try {
            const res = await api.request("users/me", {waitBoot: false});
            user.value = res.data;
            return res.data;
        } catch (e) {
            console.error(e);
        } finally {
            loaded.value = true;
        }
    }

    /**
     * 存储登录
     *
     * @param {{user: Object, token: String, ttl: Number}} 来自登录接口返回的信息
     */
    function login(info) {
        setToken(info.token);
        user.value = info.user;

        reload();

        //登入事件
        uni.$emit("logged-in");
    }

    function getOrgData() {
        let orgId = uni.getStorageSync('org_id')
        return new Promise((resolve, reject) =>{
            api.get(`orgs/${orgId}`).then(res => {
                if (!res.alias) {
                    res.alias = res.name
                }
                orgData.value = res;
                resolve(res)
            }).catch(err => {
                uni.showModal({
                    content: err.message
                })
            })
        })

    }


    function logoutOrg() {
        orgData.value = null;
        uni.setStorageSync("org_id", null)
    }

    /**
     * 清除登录
     */
    function logout() {
        api.get('logout').then().catch(err => {
            console.log(err.message)
        })

        setToken(null);
        user.value = null

        //登出事件
        uni.$emit("logged-out");
    }

    return {user, orgData, loaded, reload, login, logout, getOrgData};
});
