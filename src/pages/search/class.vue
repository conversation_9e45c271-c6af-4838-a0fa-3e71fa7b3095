<template>
    <view class="content">
        <view class="esb-fixed">
            <view class="exam-search-box">
                <image src="../../images/icon/search-line.png"></image>
                <input class="uni-input" type="text" v-model="keywords" :focus="focus" confirm-type="search" @confirm="search()" placeholder="请输入关键词" @input="clearInput"/>
                <view class="search-clear-btn" v-if="showClearIcon" @click="clearIcon">
                    <view class="scb-icon">
                        <image src="../../images/icon/close-line-white.png"></image>
                    </view>
                </view>
                <button class="esb-btn" :disabled="disabled" @click="search()">搜索</button>
            </view>
        </view>

        <!-- 搜索记录 -->
        <view class="search-history" v-if="showHistory && this.searchHistories.length > 0">
            <view class="sh-tit">
                <text>最近搜索</text>
                <view class="sht-delete" @click="clearHistory">
                    <image src="../../images/icon/delete-bin-line.png"></image>
                </view>
            </view>
            <view class="sh-list">
                <view class="shl-item"  @click="search(history)" v-for="(history, index) in searchHistories" :key='index'>{{ history }}</view>
            </view>
        </view>
        <view class="es-info" v-if="isSearch">与<text>{{ keywords }}</text>相关的课程：</view>
    </view>
    <course :list="list" :list-status="listStatus" :list-loaded="listLoaded" :list-loading="listLoading"></course>
</template>

<script>
import api, {buildQuery} from "../../lib/api";
import {alert, showDelayLoading} from "../../lib/utils";
import Course from "@/components/course.vue";

export default {
    components: {Course},
    onLoad() {
        this.getHistory()
    },
    data() {
        return{
            list: [],
            nextCursor: "",
            selectCategoryId: "course",
            listRows: 20,
            listLoaded: false,
            listLoading: false,
            keywords: '',
            searchHistories: [],
            searchHistoryKey: "search_course",
            showHistory: true,
            isSearch: false,
            focus: true,
            showClearIcon: false,
            disabled: true,
        }
    },
    computed: {
        listStatus() {
            if (this.listLoading) {
                return "loading";
            } else if (this.listEnd) {
                return "noMore";
            } else {
                return "more";
            }
        }
    },
    watch: {
        keywords(e) {
            if (e.length > 0) {
                this.showClearIcon = true
                this.disabled = false
            } else {
                this.showClearIcon = false
                this.disabled = true
            }
        }
    },
    onReachBottom() {
        console.log("上拉加载")
        this.getList()
    },
    methods: {
        search(history) {
            if (history) {
                this.keywords = history
            }
            this.chooseCategory('course')
            this.addHistory()
        },
        chooseCategory(cateId) {
            this.selectCategoryId = cateId
            if (this.keywords === '') {
                return
            }
            this.list = []
            this.listEnd = false
            this.nextCursor = ""
            this.getList(true)
        },
        getList(refresh) {
            if (this.listLoading || this.listEnd) {
                return;
            }

            let query = {list_rows: this.listRows}
            if (this.nextCursor !== '') {
                query.next_cursor = this.nextCursor
            }
            if (this.keywords !== '') {
                query.keywords = this.keywords
            }

            this.listLoading = true;
            const hideLoading = refresh ? showDelayLoading('加载中', 200) : null

            this.showHistory = false
            this.isSearch = true
            let url = "cms/" + this.selectCategoryId + "/contents"
            api.get(url, query).then(res => {
                this.url = url + (url.includes("?") ? "&" : "?") + buildQuery(query)
                this.nextCursor = res.next_cursor
                if (refresh) {
                    this.list = res.data
                } else {
                    this.list.push(...res.data)
                }
                this.listLoaded = true;
                if (res.next_cursor === '') {
                    this.listEnd = true
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                this.listLoading = false
                if (hideLoading) {
                    hideLoading()
                }
            })
        },
        getHistory() {
            uni.getStorage({
                key: this.searchHistoryKey,
                success: (res) => {
                    this.searchHistories = res.data
                },
            })
        },
        addHistory() {
            if (this.keywords === '') {
                return
            }
            const key = this.searchHistoryKey;
            uni.getStorage({
                key,
                success: (res) => {
                    let list = res.data;
                    const i = list.indexOf(this.keywords);

                    if (i != -1) {
                        list.splice(i, 1);
                    }

                    list.unshift(this.keywords);

                    if (list.length > 10) {
                        list.splice(10);
                    }

                    uni.setStorage({
                        key,
                        data: list,
                        success: () => {
                            this.searchHistories = list
                        },
                        fail: () => {}
                    });
                },
                fail: () => {
                    uni.setStorage({
                        key,
                        data: [this.keywords],
                        success: () => {
                            this.searchHistories = [this.keywords]
                        },
                        fail: () => {}
                    });
                }
            });
        },
        clearHistory() {
            uni.removeStorage({
                key: this.searchHistoryKey,

                success: () => {
                    this.searchHistories = []
                },

                fail(err) {}
            });
        },
        clearInput(e) {
            if (e.detail.value.length > 0) {
                this.showClearIcon = true
            } else {
                this.showClearIcon = false
            }
        },
        clearIcon() {
            this.keywords = ''
            this.showClearIcon = false
            this.list = []
            this.showHistory = true
            this.listLoaded = false
            this.isSearch = false
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx 30rpx 0;
    }
    .esb-fixed {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        background-color: #F3F3FF;
        margin-bottom: 30rpx;
    }
    .esb-eb {
        height: 130rpx;
    }
    .exam-search-box {
        display: flex;
        align-items: center;
        background-color: #FFF;
        border-radius: 12rpx;
        padding: 0 0 0 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .exam-search-box image {
        width: 32rpx;
        height: 32rpx;
    }
    .exam-search-box .uni-input {
        flex: 1;
        height: 100rpx;
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .esb-btn {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #390ABC;
    }
    .clear {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
    }

    .es-info {
        font-size: 28rpx;
        color: #999;
    }
    .es-info text {
        font-weight: bold;
        font-style: italic;
        padding: 0 10rpx;
        color: #333;
    }


    .esrl-jiexi-cnt text {
        font-weight: bold;
    }



    /* 内容块 */
    .ask-cnt-list {
        margin: 0 30rpx;
    }
    .acl-item {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .acli-top {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 0;
    }
    .aclit-l image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
    }
    .aclit-r {
        padding-left: 20rpx;
    }
    .aclitr-tit {
        font-size: 28rpx;
        font-weight: bold;
    }
    .aclitr-time {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .aclic-txt {
        padding: 30rpx;
        white-space: pre-wrap;
        font-size: 28rpx;
        line-height: 1.6;
    }
    .aclic-txt navigator {
        color: #390ABC;
        display: inline-block;
    }

    .aclic-img {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 0 0 30rpx 30rpx;
    }
    .aclic-img image {
        width: 209rpx;
        height: 209rpx;
        margin-right: 1px;
        margin-bottom: 1px;
        border-radius: 12rpx;
    }

    .acli-foot {
        display: flex;
        align-items: center;
        border-top: 1px solid #f3f3ff;
    }
    .acli-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
    }
    .acli-item image {
        width: 32rpx;
        height: 32rpx;
    }
    .acli-item text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }

    .ask-foot-box {
        position: sticky;
        bottom: 0;
        padding: 16rpx 30rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .afb-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .afb-btn {
        flex: 1;
        height: 90rpx;
        text-align: center;
        line-height: 90rpx;
        background-color: #390ABC;
        color: #fff;
        font-size: 28rpx;
        border-radius: 80rpx;
    }
    .afb-small-btn {
        width: 200rpx;
        margin-left: 30rpx;
        height: 90rpx;
        line-height: 90rpx;
        text-align: center;
        font-size: 28rpx;
        color: #390ABC;
    }


    /* 搜索历史 */
    .sh-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10rpx;
    }
    .sh-tit text {
        font-size: 28rpx;
        color: #999;
    }
    .sht-delete {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        width: 80rpx;
    }
    .sht-delete image {
        width: 32rpx;
        height: 32rpx;
    }
    .sh-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
    .shl-item {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 30rpx;
        margin: 0 30rpx 30rpx 0;
        background-color: #fff;
        border-radius: 80rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }

    /* 搜索清楚按钮 */
    .search-clear-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        padding: 0 20rpx;
    }
    .scb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        border-radius: 100%;
        background-color: rgba(0, 0, 0, .3);
        margin-right: 10rpx;
    }
    .search-clear-btn image {
        width: 28rpx;
        height: 28rpx;
    }
</style>