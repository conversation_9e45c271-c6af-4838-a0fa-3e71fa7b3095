<template>
    <view>
        <web-view :src="url"  ></web-view>
    </view>
</template>
<script>
    import { getWebViewUrl } from '@/lib/utils';
    export default {
        data() {
            return {
                url: '',
                title: ''
            };
        },
        onLoad(options) {
            if (!options.url) {
                uni.navigateBack();
                return;
            }
            //uni.showNavigationBarLoading();
            this.url = decodeURIComponent(options.url)
            if (options.title) {
                const title = decodeURIComponent(options.title);
                uni.setNavigationBarTitle({
                    title
                });
                this.title = title;
            }
        },
        methods: {
            onWebPageLoad(e) {
                //uni.hideNavigationBarLoading();
            },
            onWebPageError(e) {
                uni.hideNavigationBarLoading();
                uni.showModal({
                    title: '页面加载失败',
                    content: '很抱歉，目标页面未能成功加载，您可以返回或尝试重新加载。',
                    confirmText: '重新加载',
                    cancelText: '返回',
                    success: (e) => {
                        if (e.confirm) {
                            const url = getWebViewUrl(this.url, this.title);
                            uni.redirectTo({
                                url
                            });
                        } else {
                            uni.navigateBack();
                        }
                    }
                });
            }
        }
    };
</script>

<style>
</style>