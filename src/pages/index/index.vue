<template>
    <view class="content">
        <uv-skeletons :loading="pageLoading" animate :skeleton="headSkeleton">
            <!-- 幻灯片 -->
            <view class="uni-margin-wrap">
                <swiper class="swiper" circular :indicator-dots="slides.length > 1" autoplay :interval="5000"
                        :duration="500">
                    <swiper-item v-for="item in slides" :key="item.id">
                        <view class="swiper-item" @click="go(item.url)">
                            <image :src="item.image_url" mode="aspectFill"></image>
                        </view>
                    </swiper-item>
                </swiper>
            </view>

            <!-- 金刚区 -->
            <view class="diamond-area">
                <view class="da-item" v-for="item in kk" :key="item.id" @click="go(item.url)">
                    <image :src="item.image_url" mode="aspectFit"></image>
                    <text>{{ item.name }}</text>
                </view>
            </view>
        </uv-skeletons>

        <!-- 搜索框 -->
        <view class="index-search-box">
            <view class="ssl-box">
                <view class="isb-input">
                    <image src="../../images/icon/search-line.png"></image>
                    <input class="uni-input" placeholder="请输入关键词检索" v-model="q" @input="clearInput"/>
                    <view class="search-clear-btn" v-if="showClearIcon" @click="clearIcon">
                        <view class="scb-icon">
                            <image src="../../images/icon/close-line-white.png"></image>
                        </view>
                    </view>
                    <button class="isbi-btn" @click="search">搜索资料</button>
                </view>
            </view>
        </view>

        <!-- 热门课程包 -->
        <view class="hot-cat-box" v-if="recommendCoursePacksLoading || recommendCoursePacks.length > 0">
            <view class="dpb-tit" style="padding: 0;">
                <view class="dpbt-txt">推荐课程包</view>
                <!--
                <navigator class="dpbt-more" url="/pages/document/topic-list">
                    <text>查看更多</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </navigator>
                -->
            </view>
            <uv-skeletons :loading="recommendCoursePacksLoading" animate
                          :skeleton="[{type:'custom',style:'width:630rpx; height:294rpx;'}]">
                <view class="hcb-swiper">
                    <swiper class="swiper" circular indicator-dots autoplay
                            :interval="7000"
                            :duration="500">
                        <swiper-item v-for="pack in recommendCoursePacks" :key="pack.sid" @click="goCoursePack(pack.sid)">
                            <view class="swiper-item-course-pack">
                                <image :src="pack.cover_src"></image>
                            </view>
                        </swiper-item>
                    </swiper>
                </view>
            </uv-skeletons>
        </view>

        <!-- 推荐课程 -->
        <view class="index-class-box" v-if="recommendCoursesLoading || recommendCourses.length > 0">
            <view class="icb-tit">
                <view class="icbt-txt">课程推荐</view>
                <navigator class="icbt-more" url="/pages/training/video/list">
                    <text>查看更多</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </navigator>
            </view>
            <uv-skeletons :loading="recommendCoursesLoading" animate :skeleton="coursesSkeleton">
                <view class="icb-list">
                    <view class="icbl-item" v-for="item in recommendCourses" :key="item.sid"
                          @click="gotoContent(item.type_label, item.sid)">
                        <view class="icbli-video">
                            <image :src="item.cover_src" mode="aspectFill"></image>
                            <view class="icbliv-tip">
                                <text>立即播放</text>
                                <image src="../../images/icon/play-fill.png"></image>
                            </view>
                        </view>
                        <view class="icbli-tit">{{ item.title }}</view>
                        <view class="icbli-foot" v-if="item.resource && item.type_label == 'course'">
                            <view class="icblif-price" v-if="item.view_limit == limitType.credit">
                                {{ item.charge_credit }}积分
                            </view>
                            <view class="icblif-price" v-else-if="item.view_limit == limitType.amount">
                                ￥{{ item.charge_amount }}
                            </view>
                            <view class="icblif-price" v-else-if="item.view_limit == limitType.free">免费</view>
                            <view class="icblif-num">{{ item.resource.learning_count }}人在学</view>
                        </view>
                    </view>
                </view>
            </uv-skeletons>
        </view>

        <!-- 最新资料 -->
        <view class="index-class-box">
            <view class="icb-tit">
                <view class="icbt-txt">最新资料</view>
                <view class="icbt-more" @click="goIndex">
                    <text>查看更多</text>
                    <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                </view>
            </view>
            <uv-skeletons :loading="newestDocsLoading" animate :skeleton="docsSkeleton">
                <view class="docs-list-box">
                    <view class="dlb-item" v-for="item in newestDocs" :key="item.sid"
                          @click="gotoContent(item.type_label, item.sid)">
                        <view class="dlbi-img">
                            <doc-icon :format="item.resource?.format"></doc-icon>
                        </view>
                        <view class="dlbi-cnt">
                            <view class="dlbic-tit">{{ item.title }}</view>
                            <view class="dlbic-foot">
                                <view class="dlbicf-time">时间：
                                    <uni-dateformat :date="item.release_at" format="yyyy-MM-dd"></uni-dateformat>
                                </view>
                                <view class="dlbicf-pn" v-if="item.resource && item.type_label == 'doc'">
                                    页数：{{ item.resource.page_count }}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </uv-skeletons>
        </view>

        <SideLinkAi />
    </view>
</template>

<script>
import DocIcon from "@/components/doc-icon.vue";
import api from "@/lib/api.js";
import {ContentViewLimit as cvl} from "@/lib/enums";
import {tabBarPageLoad} from "@/lib/tabbar.js";
import {alert, getDetailUrl, openUrlAnywhere} from "@/lib/utils";
import headSkeleton from "@/skeletons/home-head.json";
import coursesSkeleton from "@/skeletons/course-list.json";
import docsSkeleton from "@/skeletons/doc-list.json";
import SideLinkAi from "@/components/side-link/ai.vue";
import {getAppName} from "@/lib/context";

export default {
    components: {
        DocIcon,
        SideLinkAi
    },
    data() {
        return {
            slides: [],
            kk: [],
            // searchCategories: [],
            selectCategoryId: '',
            pageLoading: true,
            limitType: cvl,
            recommendCourses: [],
            recommendCoursesLoading: true,
            newestDocs: [],
            newestDocsLoading: true,
            recommendCoursePacks: [],
            recommendCoursePacksLoading: true,
            q: "",
            showClearIcon: false,
            headSkeleton,
            coursesSkeleton,
            docsSkeleton,
            appName: getAppName()
        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: "/pages/index/index",
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        };
    },
    onLoad() {
        Promise.all(this.loadAll())
            .catch(e => alert(e.message));

        tabBarPageLoad();
    },
    onPullDownRefresh() {
        Promise.all(this.loadAll())
            .catch(e => alert(e.message))
            .finally(() => {
                uni.stopPullDownRefresh()
            });
    },
    methods: {
        go(url) {
            openUrlAnywhere(url);
        },
        /**
         * 加载整个页面数据
         * @return {Promise[]}
         */
        loadAll() {
            return [
                //头部幻灯、金刚区、搜索分类推荐
                api.get("pages/home").then(data => {
                    this.slides = data.slides;
                    this.kk = data.kk;

                    // const categories = data.search_categories;

                    // if (categories.length > 0) {
                    //     categories.unshift({
                    //         "name": "全部",
                    //         "sid": "",
                    //         "logo_src": ""
                    //     });
                    // }

                    // this.searchCategories = categories;

                    this.pageLoading = false;
                }),

                //推荐课程包
                api.get("cms/course_pack/recommend-contents").then(data => {
                    this.recommendCoursePacks = data.length > 10 ? data(0, 10) : data;
                    this.recommendCoursePacksLoading = false;
                }),

                //推荐课程
                api.get("cms/course/recommend-contents").then(data => {
                    this.recommendCourses = data.length > 2 ? data.slice(0, 2) : data;
                    this.recommendCoursesLoading = false;
                }),

                //最新资料
                api.get("cms/material/new-contents").then(data => {
                    this.newestDocs = data;
                    this.newestDocsLoading = false;
                })
            ];
        },
        gotoContent(type, sid) {
            let url = getDetailUrl(type, sid)
            if (url === '') {
                alert("暂不支持打开该类型内容")
                return;
            }
            if (type === 'video') {
                url += "&url=cms/material/new-contents"
            }
            uni.navigateTo({
                url
            });
        },
        goIndex() {
            uni.navigateTo({
                url: '/pages/document/list?cateId=material'
            })
        },
        goCoursePack(sid) {
            uni.navigateTo({
                url: "/pages/training/video/course-package?sid=" + sid
            })
        },
        search() {
            const q = this.q.trim();

            const cateId = this.selectCategoryId || "material"

            uni.navigateTo({
                url: "/pages/search/doc?cateId=" + cateId + "&q=" + q
            });
        },
        clearInput(e) {
            this.q = e.detail.value;
            if (e.detail.value.length > 0) {
                this.showClearIcon = true;
            } else {
                this.showClearIcon = false;
            }
        },
        clearIcon() {
            this.q = '';
            this.showClearIcon = false;
        }
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx 30rpx 1rpx;
}

.uni-margin-wrap {
    width: 690rpx;
    height: 294rpx;
}

.swiper {
}

.swiper-item {
    display: block;
    text-align: center;
    width: 690rpx;
    height: 294rpx;
    padding: 0;
}

.swiper-item image {
    width: 690rpx;
    height: 294rpx;
    vertical-align: top;
    border-radius: 12rpx 12rpx 0 0;
}

.swiper-item-course-pack {
    display: block;
    text-align: center;
    width: 630rpx;
    height: 294rpx;
    padding: 0;
}

.swiper-item-course-pack image {
    width: 630rpx;
    height: 294rpx;
    vertical-align: top;
    border-radius: 12rpx;
}


.swiper-list {
    margin-top: 40rpx;
    margin-bottom: 0;
}

.uni-common-mt {
    margin-top: 60rpx;
    position: relative;
}

.diamond-area {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 0 0 12rpx 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.da-item {
    flex: 1;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
}

.da-item image {
    width: 64rpx;
    height: 64rpx;
}

.da-item text {
    font-size: 28rpx;
    padding-top: 10rpx;
    font-size: 28rpx;
}

/* 搜索框 */
.index-search-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.ssl-box {
    padding: 30rpx;
}

.isb-input {
    display: flex;
    align-items: center;
    position: relative;
    background-color: #F3F3FF;
    border-radius: 12rpx;
    padding: 0 0 0 20rpx;
}

.isb-input image {
    width: 32rpx;
    height: 32rpx;
}

.isb-input .uni-input {
    background-color: #F3F3FF;
    height: 100rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    flex: 1;
}

.isb-input .isbi-btn {
    height: 100rpx;
    line-height: 100rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #390ABC;
    background: none;
    z-index: 2;
}

.index-class-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.icb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 30rpx;
}

.icbt-txt {
    font-size: 28rpx;
    font-weight: bold;
}

.icbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.icbt-more text {
    font-size: 24rpx;
    color: #999;
}

.icbt-more image {
    width: 32rpx;
    height: 32rpx;
}

.icb-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
}

.icbl-item {
    width: 300rpx;
    padding-bottom: 30rpx;
}

.icbl-item image {
    width: 300rpx;
    height: 300rpx;
    vertical-align: top;
    border-radius: 12rpx;
}

.icbli-video {
    position: relative;
}

.icbliv-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 0 12rpx 0 6rpx;
    height: 40rpx;
    background-color: #fff;
    border-radius: 0 20rpx 0 12rpx;
}

.icbliv-tip text {
    font-size: 24rpx;
    color: #de572e;
    padding-right: 4rpx;
}

.icbliv-tip image {
    width: 18rpx;
    height: 18rpx;
}

.icbli-tit {
    height: 70rpx;
    font-size: 28rpx;
    font-weight: bold;
    line-height: 35rpx;
    padding: 20rpx 0;
}

.icbli-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    color: #999;
}

.docs-list-box {
}

/* 资料列表 */
.dlb-item {
    display: flex;
    align-items: center;
    padding: 0 30rpx 30rpx;
}

.dlb-item:first-child {
    padding-top: 0;
}

.dlbic-tit {
    height: 80rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.dlbic-foot {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #999;
    padding-top: 20rpx;
}

.dlbicf-time {
    padding-right: 30rpx;
}

.dlbi-img {
    position: relative;
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    border-radius: 12rpx;
    border: 1px solid #e7e7e7;
}

.doc-type {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 64rpx;
    height: 64rpx;
}

.dlbi-cnt {
    flex: 1;
}

/* 搜索分类滚动 */
.sub-sview-list {
    white-space: nowrap;
    width: 100%;
    padding: 20rpx 0;
}

.sub-sview-item {
    display: inline-block;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    font-size: 24rpx;
    border: 1px solid #E7E7E7;
    border-radius: 70rpx;
    margin-left: 30rpx;
    color: #666;
}

.sub-sview-item.cur {
    background-color: #390ABC;
    color: #fff;
    border: 1px solid #390ABC;
}


/* 搜索清楚按钮 */
.search-clear-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    padding: 0 20rpx;
}

.scb-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
    border-radius: 100%;
    background-color: rgba(0, 0, 0, .3);
    margin-right: 10rpx;
}

.search-clear-btn image {
    width: 28rpx;
    height: 28rpx;
}

/* 热门课程包 */
.hot-cat-box {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 0 30rpx 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.dpb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 20rpx 0 30rpx;
}
.dpbt-txt {
    font-size: 28rpx;
    font-weight: bold;
}
.dpbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}
.dpbt-more text {
    font-size: 24rpx;
    color: #999;
}
.dpbt-more image {
    width: 32rpx;
    height: 32rpx;
}
</style>
