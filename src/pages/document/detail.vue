<template>
    <view class="content">
        <view class="doc-detail-box">
            <view class="ddb-tit">
                {{ detail.title }}
            </view>
            <view class="ddb-info">
                 <view class="ddbi-item" v-if="detail.source">上传人：{{ detail.source }}</view>
                <view class="ddbi-item">上传时间：<uni-dateformat :date="detail.release_at" format="yyyy-MM-dd"></uni-dateformat></view>
                <view class="ddbi-item">格式：{{ detail.resource?.format }}</view>
                <view class="ddbi-item">大小：{{ getFileSize(detail.resource?.filesize) }}</view>
                <view class="ddbi-item" v-if="detail.resource?.page_count">页数：{{ detail.resource.page_count }}</view>
                <view class="ddbi-item">下载次数：{{ detail.resource?.download_count }}</view>
            </view>
            <view class="ddb-cnt">
                <view v-if="detail.type_label === 'doc'">
                    <view class="ddbc-img-list">
                        <view class="ddbcil-box" v-for="(item, index) in detail.resource.preview_images_src" :key="index" v-show="index < showMax" @click="previewImg(index)">
                            <view class="ddbcil-item">
                                <image :src=item mode="widthFix"></image>
                            </view>
                            <view class="ddbcil-pn">第{{ index + 1 }}页 / 共{{ detail.resource?.page_count }}页</view>
                        </view>
                    </view>

                    <view class="ddbc-check-more" v-if="detail.resource?.preview_images_src?.length > showMax" @click="showMore">
                        <text>展开查看更多</text>
                        <image src="../../images/icon/arrow-down-s-line.png"></image>
                    </view>
                </view>

                <view v-else-if="detail.type_label === 'rich_text'">
                    <mp-html :content="detail.resource.content" />
                </view>

                <view class="ddbc-main" v-if="detail.intro">
                    <view class="ddb-tit">资料简介</view>
                    <view class="ddbcm-txt">{{ detail.intro }}</view>
                </view>

                <view class="copyright-txt">
                    <image src="../../images/icon/file-warning-fill.png"></image>
                    <view class="ct-main-cnt">版权说明：本文档由用户提供并上传，收益归属内容提供方，若内容存在侵权，可提供相关信息<navigator url="/pages/me/service">联系客服</navigator>举报或认领</view>
                </view>
                <!--相关资源-->
                <relation-contents :sid="sid" type="doc"></relation-contents>
            </view>
        </view>

        <!-- 底部功能菜单 -->
        <view class="ddf-empty-block"></view>
        <view class="doc-detail-foot">
            <view class="ddf-l">
                <view class="ddf-item" @click="attitude">
                    <image src="../../images/icon/thumb-up-line.png" v-if="detail.attitude === 0"></image>
                    <image src="../../images/icon/thumb-up-fill.png" v-if="detail.attitude === 1"></image>
                    <image src="../../images/icon/thumb-down-fill.png" v-if="detail.attitude === 2"></image>
                    <text>有用</text>
                </view>
                <view class="ddf-item" @click="favorite">
                    <image src="../../images/icon/star-fill.png" v-if="detail.favorite"></image>
                    <image src="../../images/icon/star-line.png" v-else></image>
                    <text>收藏</text>
                </view>
                <button class="ddf-item" open-type="share">
                    <image src="../../images/icon/share-forward-2-line.png"></image>
                    <text>分享</text>
                    <view class="ddfi-tips" v-if="user">赏</view>
                </button>
            </view>


            <!-- 未登录情况，点击后直接跳转至登录页面 -->
            <view class="ddf-r" v-if="!user" @click="login">
                <view class="ddfr-top">
                    <text>尚未登录</text>
                </view>
                <view class="ddfr-foot">
                    <text>点击登录后了解更多</text>
                </view>
            </view>

            <view class="ddf-r" v-if="user && !detail.download" @click="buy">
                <view class="ddfr-top">
                    <image src="../../images/icon/download-2-line.png"></image>
                    <text>下载资源</text>
                </view>
                <view class="ddfr-foot">
                    <image v-if="detail.view_limit === limitType.credit" src="../../images/icon/copper-coin-fill.png"></image>
                    <text v-if="detail.view_limit === limitType.credit">{{ detail.charge_credit }}积分</text>
                    <text v-if="detail.view_limit === limitType.amount">￥{{ detail.charge_amount }}元</text>
                    <text v-if="detail.view_limit === limitType.credit_amount">{{ detail.charge_credit }}积分/￥{{ detail.charge_amount }}元</text>
                </view>
            </view>
            <template v-if="user && detail.download">
                <template v-if="systemPlatform !== 'windows'">
                    <view class="ddf-r">
                        <view class="ddf-view-btn" @click="preview('download')">
                            <image src="../../images/icon/eye-fill.png"></image>
                            <text>预览 / 下载</text>
                        </view>
                    </view>
                </template>
                <template v-else>
                    <view class="ddf-r ddf-r-win">
                        <view class="ddf-view-btn" @click="preview('preview')">
                            <text>预览</text>
                        </view>
                    </view>
                    <view class="ddf-r ddf-r-win">
                        <view class="ddf-view-btn" @click="preview('download')">
                            <text>下载</text>
                        </view>
                    </view>
                </template>
            </template>
        </view>
    </view>
    <pay-alert ref="payAlert" :order="order" :credit="detail?.charge_credit" :allow-types="allowTypes" @creditPay="onCreditPay" @createdOrder="onCreatedOrder" @paymentCallback="onPaymentCallback"></pay-alert>
</template>

<script>
import {showDelayLoading, alert, getFileSize} from "@/lib/utils";
import api from "@/lib/api";
import {loginRequired} from "@/lib/login";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import { ContentViewLimit as cvl } from "@/lib/enums";
import {getAllowTypes} from "@/lib/pay";
import {getReferral} from "@/lib/context";
import RelationContents from "@/components/relation-contents.vue";
import PayAlert from "@/components/pay-alert.vue";
import Agreement from "@/components/agreement.vue";
import {handleFileDownload} from "@/lib/fileDownload";

export default {
    components: {
        Agreement,
        RelationContents,
        PayAlert
    },
    onLoad(e) {
        if (!e.sid) {
            uni.showModal({
                content: "参数错误",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        uni.navigateBack()
                    }
                }
            })
        }
        this.sid = e.sid
        this.getDetail()
        uni.$on('logged-in', () => {
            this.getDetail()
        })
    },
    onUnload() {
        uni.$off('logged-in')
    },
    computed: {
        ...mapState(useUserStore, ['user', 'reload']),
    },
    data() {
        return {
            sid: "",
            detail: {
                resource: {}
            },
            limitType: cvl,
            showMax: 3,
            systemPlatform: uni.getSystemInfoSync().platform,
            order: {},
            allowTypes: []
        }
    },
    onShareAppMessage(res) {
        if (res.from === 'button') {// 来自页面内分享按钮
            console.log(res.target)
        }
        let path = "/pages/document/detail?sid=" + this.detail.sid;

        if (this.user) {
            path += "&ref=" + this.user.uuid;
        }

        return {
            title: this.detail.title,
            path
        };
    },
    methods: {
        login() {
            let url = "/pages/login/index"
            uni.navigateTo({
                url
            })
        },
        getFileSize,
        getDetail() {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get("cms/contents/" + this.sid).then(res => {
                this.detail = res
                this.order.total_amount = this.detail.charge_amount
                this.allowTypes = getAllowTypes(this.detail.view_limit)
                let uuid = getReferral(true)
                if (uuid !== '') {
                    this.shareReward(uuid)
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        showMore() {
            loginRequired().then(() => {
                this.showMax = this.detail.resource.preview_images_src.length
            })
        },
        shareReward(uuid) {
            let data = {uuid: uuid, business_type: this.detail.business_type, business_id: this.detail.sid}
            api.post("invitations/share-ref", data).then(res => {

            }).catch(err => {
                console.log(err.message)
            })
        },
        attitude() {
            loginRequired().then(() => {
                const hideLoading = showDelayLoading("加载中", 200)
                if (!this.detail.attitude) {
                    api.post("attitude/" + this.detail.business_type + "/" + this.detail.sid, {attitude: 1}).then(res => {
                        this.detail.attitude = 1
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                } else {
                    api.delete("attitude/" + this.detail.business_type + "/" + this.detail.sid + "?attitude=" + this.detail.attitude).then(res => {
                        this.detail.attitude = 0
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                }
            })
        },
        favorite() {
            loginRequired().then(() => {
                const hideLoading = showDelayLoading("加载中", 200)
                if (!this.detail.favorite) {
                    api.post("favorites/" + this.detail.business_type + "/" + this.detail.sid).then(res => {
                        this.detail.favorite = true
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                } else {
                    api.delete("favorites/" + this.detail.business_type + "/" + this.detail.sid).then(res => {
                        this.detail.favorite = false
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                }
            })
        },
        buy() {
            loginRequired().then(() => {
                if (this.allowTypes.length === 0) {
                    uni.showToast({
                        title: "当前内容无需购买",
                        icon: "none",
                        mask: true
                    });
                    return
                }

                if (this.detail.view_limit === this.limitType.credit) {
                    this.$refs.payAlert.buyFromCredit()
                } else if (this.detail.view_limit === this.limitType.amount && !this.user.balance_show) {
                    this.$refs.payAlert.buyFromAmount()
                } else {
                    this.$refs.payAlert.open()
                }
            })
        },
        onCreatedOrder(buyType) {
            const hideLoading = showDelayLoading("")
            api.post("cms/contents/" + this.detail.sid + "/buy-order").then(res => {
                for (let key in res) {
                    this.$set(this.order, key, res[key])
                }
                if (buyType == 'amount') {
                    this.$refs.payAlert.payFromAmount()
                } else {
                    this.$refs.payAlert.payFromBalance()
                }
            }).catch(e => {
                uni.showModal({
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                hideLoading()
            })
        },
        onCreditPay() {
            // 扣除对应积分
            const hideLoading = showDelayLoading("购买中", 200)
            api.post("cms/contents/" + this.detail.sid + "/credit").then(res => {
                uni.showToast({
                    title: "购买成功",
                    icon: "success"
                });
                this.$refs.payAlert.close()
                this.getDetail()
                this.reload()
            }).catch(err => {
                if (err.code === 403) {
                    this.$refs.payAlert.creditNotEnough(err.message)
                } else {
                    alert(err.message)
                }
            }).finally(() => {
                hideLoading()
            })
        },
        onPaymentCallback() {
            this.getDetail()
        },
        preview(operateType) {
            uni.showLoading({
                title: '加载中...',
                mask: true
            });

            api.get("cms/contents/" + this.detail.sid + "/download").then(res => {
                let filePath = "";

                // #ifdef MP-WEIXIN
                //使用 filePath 可以保证分享出去的文件名，但是弊端是保存的文件会存在微信的用户空间，不会被系统当成临时文件自动清理
                if (this.detail.resource.filename) {
                    filePath = wx.env.USER_DATA_PATH + '/' + this.detail.resource.filename;
                }
                // #endif

                console.log(this.systemPlatform)

                // Windows 端特殊处理
                if (this.systemPlatform === 'windows') {
                    if (operateType === 'download') {
                        uni.downloadFile({
                            url: res.url,
                            success: (res) => {
                                if (res.statusCode === 200) {
                                    // 保存文件到本地
                                    wx.saveFileToDisk({
                                        filePath: res.tempFilePath,
                                        success: () => {
                                            uni.hideLoading();
                                        },
                                        fail: err => {
                                            uni.hideLoading();
                                            uni.showModal({
                                                title: '下载文件失败',
                                                content: err.errMsg,
                                                showCancel: false
                                            });
                                        }
                                    });
                                }
                            },
                            fail(err) {
                                uni.hideLoading();
                                uni.showModal({
                                    title: '下载文件失败',
                                    content: err.errMsg,
                                    showCancel: false
                                });
                            }
                        });
                    } else {
                        handleFileDownload(res.url, filePath)
                    }
                } else {
                    handleFileDownload(res.url, filePath)
                }
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            });
        },
        previewImg(currentIndex) {
            if (this.detail.resource.preview_images_src.length == 0) {
                return
            }
            let urls = []
            let urls2 = []
            let previewImageSrcList = this.detail.resource.preview_images_src.slice(0, this.showMax)
            previewImageSrcList.forEach((item, index) => {
                urls2.push(item)
            })
            uni.previewImage({
                urls: urls.concat(urls2),
                current: currentIndex
            });
        },
        updateDetail() {
            this.getDetail()
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .ddb-tit {
        font-size: 36rpx;
        font-weight: bold;
        line-height: 1.6;
    }
    .ddb-info {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        color: #999;
        padding-top: 20rpx;
        margin-bottom: 30rpx;
    }
    .ddbi-item {
        padding-right: 30rpx;
        font-size: 24rpx;
        line-height: 1.8;
        padding-bottom: 10rpx;
    }

    .ddbcil-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        padding: 6rpx;
    }
    .ddbcil-item image {
        width: 100%;
        border-radius: 12rpx;
    }
    .ddbcil-pn {
        font-size: 24rpx;
        color: #999;
        padding: 20rpx 0;
        text-align: center;
    }

    .ddbc-check-more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        background-color: #fff;
        border-radius: 12rpx;
        width: 280rpx;
        margin: 0 auto 40rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddbc-check-more text {
        font-size: 28rpx;
        padding-right: 10rpx;
    }
    .ddbc-check-more image {
        width: 32rpx;
        height: 32rpx;
    }

    /* 底部浮层 */
    .doc-detail-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding-bottom: env(safe-area-inset-bottom);
        height: 140rpx;
        border-top: 1px solid #F3F3FF;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddf-l {
        flex: 1;
        box-sizing: border-box;
        display: flex;
        padding: 0 10rpx;
    }
    .ddf-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 140rpx;
    }
    .ddf-item image {
        width: 36rpx;
        height: 36rpx;
    }
    .ddf-item text {
        padding-top: 10rpx;
        font-size: 28rpx;
    }
    .ddfi-tips {
        position: absolute;
        left: 50%;
        top: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 44rpx;
        height: 44rpx;
        border-radius: 100%;
        background-color: #f54a45;
        color: #fff;
        font-size: 24rpx;
        line-height: 1;
        transform: translateX(-50%);
        margin-left: 40rpx;
    }
    .ddf-r {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 300rpx;
        background-color: #f54a45;
        color: #fff;
        height: 140rpx;
    }
    .ddf-r-win {
        width: 150rpx;
    }
    .ddf-r-win .ddf-view-btn text {
        padding-left: 0;
    }
    .ddfr-top {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ddfr-top image {
        width: 36rpx;
        height: 36rpx;
    }
    .ddfr-top text {
        padding-left: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .ddfr-foot {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 10rpx;
    }
    .ddfr-foot image {
        width: 32rpx;
        height: 32rpx;
    }
    .ddfr-foot text {
        font-size: 24rpx;
        padding-left: 6rpx;
    }
    .ddf-view-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ddf-view-btn image {
        width: 36rpx;
        height: 36rpx;
        vertical-align: top;
        margin-top: 4rpx;
    }
    .ddf-view-btn text {
        padding-left: 20rpx;
    }
    .ddf-empty-block {
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }


    .copyright-txt {
        display: flex;
        align-items: center;
        border-radius: 12rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
        margin-top: 20rpx;
    }
    .copyright-txt image {
        width: 40rpx;
        height: 40rpx;
    }
    .copyright-txt .ct-main-cnt {
        flex: 1;
        padding-left: 20rpx;
        font-size: 24rpx;
        color: #999;
        line-height: 1.6;
    }
    .copyright-txt .ct-main-cnt navigator {
        display: inline-block;
        color: #666;
        padding:0 6rpx;
        text-decoration: underline;
    }

    .related-doc-box {
        background-color: #fff;
        border-radius: 12rpx;
        padding-bottom: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .rdl-tit {
        padding: 30rpx 30rpx 10rpx;
        font-size: 28rpx;
        color: #999;
    }
    .rdl-item {
        display: flex;
        align-items: center;
        height: 88rpx;
        padding: 0 30rpx;
    }
    .rdl-item image {
        width: 38rpx;
        height: 38rpx;
    }
    .rdl-item text {
        flex: 1;
        padding-left: 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
    }


    /* 资料简介 */
    .ddbc-main {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddbc-main .ddb-tit {
        font-size: 28rpx;
        color: #999;
        font-weight: normal;
    }
    .ddbcm-txt {
        font-size: 28rpx;
        padding: 20rpx 0 0;
        line-height: 1.6;
    }

    .ddbcm-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60rpx;
    }
    .ddbcm-btn text {
        font-size: 24rpx;
        padding-right: 10rpx;
    }
    .ddbcm-btn image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
    }
</style>