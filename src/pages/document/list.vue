<template>
    <view class="content">
        <view class="doc-lis-box">
            <view class="dlb-top">
                <view class="breadcrumb-box">
                    <view class="bb-left">
                        <navigator @click="getCatalog()">
                            <image src="../../images/icon/home-2-fill.png"></image>
                            <text>资料</text>
                        </navigator>
                        <template v-for="category in supCategories" :key="category.sid">
                            <text>/</text>
                            <navigator @click="getCatalog(category)">{{ category.name }}</navigator>
                        </template>
                    </view>
                    <view class="bb-right">
                        <view class="bbr-btn" @click="goSearch()">
                            <image src="../../images/icon/menu-search-line.png"></image>
                        </view>
                        <view class="bbr-btn" @click="goCategory()">
                            <image src="../../images/icon/apps-line.png"></image>
                        </view>
                    </view>
                </view>

                <view class="sub-sview-box" v-if="subCategories.length > 0">
                    <scroll-view class="sub-sview-list navScroll" scroll-x="true" :scroll-left="scrollLeft" >
                        <view class="sub-sview-item" :class="{cur:category.sid == this.cateId}" @click="chooseCategory(category)" v-for="category in subCategories" :key="category.sid">{{ category.name }}</view>
                    </scroll-view>
                </view>
            </view>

            <view class="docs-list-box" v-if="materials.length > 0">
                <view class="dlb-item" v-for="material in materials" :key="material.sid" @click="goDetail(material)">
                    <view class="dlbi-img">
                        <doc-icon :format="material.resource?.format" />
                    </view>
                    <view class="dlbi-cnt">
                        <view class="dlbic-tit">{{ material.title }}</view>
                        <view class="dlbic-foot">
                            <view class="dlbicf-time">时间：<uni-dateformat :date="material.release_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
                            <view class="dlbicf-pn" v-if="material.resource && material.resource.page_count">页数：{{ material.resource.page_count }}</view>
                        </view>
                    </view>
                </view>
                <uni-load-more :status="listStatus"></uni-load-more>
            </view>

            <view class="no-data-nomal-box" v-if="listLoaded && !listLoading && materials.length === 0">
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无资料</text>
            </view>
        </view>
    </view>
</template>

<script>
import {showDelayLoading, alert, getDetailUrl, removeURLParameter} from "../../lib/utils";
import api, {buildQuery} from "../../lib/api";
import DocIcon from "@/components/doc-icon.vue";

export default {
    components: {
        DocIcon
    },
    onLoad(e) {
        if (!e.cateId) {
            uni.showModal({
                content: "参数错误",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        uni.navigateBack()
                    }
                }
            })
            return
        }
        this.cateId = e.cateId
        if (this.cateId === 'material') {
            this.getSubCategory('')
        } else {
            // 获取该分类的所有上级分类和同级分类
            this.getSupCategory()
        }
        // 获取资料
        this.loadMaterials()
    },
    onReachBottom() {
        this.loadMaterials()
    },
    data() {
        return {
            cateId: undefined,
            categories: [],// 同级分类
            supCategories: [],// 上级分类
            subCategories: [],// 下级分类
            materials: [],
            nextCursor: "",
            listRows: 20,
            listLoaded: false,
            listLoading: false,
            listEnd: false,
            scrollLeft: 0,
            url: '',
            title: ''
        }
    },
    computed: {
        listStatus() {
            if (this.listLoading) {
                return "loading";
            } else if (this.listEnd) {
                return "noMore";
            } else {
                return "more";
            }
        }
    },
    watch: {
        supCategories: {
            handler() {
                if (this.supCategories.length === 0) {
                    this.title = '全部资料'
                } else {
                    this.title = this.supCategories[this.supCategories.length - 1].name
                }
                uni.setNavigationBarTitle({
                    title: this.title
                });
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        getSupCategory() {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get("cms/categories/sup", {category_id: this.cateId, classify: "material"}).then(res => {
                this.categories = res.categories
                this.supCategories = res.supCategories
                this.subCategories = res.subCategories

                if (this.subCategories.length > 0) {
                    this.subCategories.unshift({
                        "name": "全部",
                        "sid": this.cateId,
                        "logo_src": ""
                    });
                }
                let currentCategory = this.categories.filter(res => res.sid == this.cateId)[0]
                this.supCategories.push({
                    "name": currentCategory['name'],
                    "sid": currentCategory['sid'],
                    "logo_src": currentCategory['logo_src']
                })
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        loadMaterials(refresh) {
            if (this.listLoading || this.listEnd) {
                return;
            }

            let query = {list_rows: this.listRows}
            if (this.nextCursor !== '') {
                query.next_cursor = this.nextCursor
            }

            this.listLoading = true;
            const hideLoading = refresh ? showDelayLoading('加载中', 200) : null

            let url = "cms/" + this.cateId + "/contents"
            api.get(url, query).then(res => {
                this.url = url + (url.includes("?") ? "&" : "?") + buildQuery(query)
                this.nextCursor = res.next_cursor
                if (refresh) {
                    this.materials = res.data
                } else {
                    this.materials.push(...res.data)
                }
                this.listLoaded = true;
                if (res.next_cursor === '') {
                    this.listEnd = true
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                this.listLoading = false
                if (hideLoading) {
                    hideLoading()
                }
            })
        },
        chooseCategory(category) {
            this.cateId = category.sid
            this.materials = []
            this.listEnd = false
            this.nextCursor = ""
            this.getSubCategory(category)
            this.loadMaterials(true)
        },
        getCatalog(category) {
            this.materials = []
            this.listEnd = false
            this.nextCursor = ""
            if (category) {
                this.cateId = category.sid
                this.getSubCategory(category, 'pop')
            } else {
                this.cateId = "material"
                this.getSubCategory('')
            }
            this.loadMaterials(true)
        },
        getSubCategory(category, type = 'push') {
            let query = {classify: "material"}
            if (category) {
                query.pid = category.sid
                if (type === 'push') {
                    if (category.name !== '全部') {
                        this.supCategories.push(category)
                    }
                } else {
                    // 将该分类后所有分类移除
                    this.supCategories = this.removeElement(this.supCategories, category)
                }
            } else {
                query.pid = 0
                this.supCategories = []
            }
            api.get("cms/categories", query).then(res => {
                this.subCategories = res
                if (this.subCategories.length > 0) {
                    this.subCategories.unshift({
                        "name": "全部",
                        "sid": this.cateId,
                        "logo_src": ""
                    });
                }
            }).catch(err => {
                alert(err)
            })
        },
        goCategory() {
            let sid = this.supCategories.length > 0 ? this.supCategories[0]['sid'] : null
            let url = "/pages/document/category"
            if (sid) {
                url += "?cateId=" + sid
            }
            uni.navigateTo({
                url
            })
        },
        goSearch() {
            let sid = this.supCategories.length > 0 ? this.supCategories[0]['sid'] : 'material'
            uni.navigateTo({
                url: "/pages/search/doc?cateId=" + sid
            })
        },
        goDetail(data) {
            let url = getDetailUrl(data.type_label, data.sid)
            if (url === '') {
                alert("未获取到详情地址")
                return
            }

            if (data.type_label === 'video') {
                this.url = removeURLParameter(this.url, 'next_cursor')
                if (this.url.includes("release_at=")) {
                    this.url = removeURLParameter(this.url, 'release_at')
                }
                if (this.url.includes('sid=')) {
                    this.url = removeURLParameter(this.url, 'sid')
                }
                this.url += `&sid=${data.sid}&release_at=${data.release_at}`
                url += `&url=${encodeURIComponent(this.url)}`
                console.log(url)
            }

            uni.navigateTo({
                url: url
            })
        },
        removeElement(array, elementToRemove) {
            const indexToRemove = array.indexOf(elementToRemove)
            if (indexToRemove !== -1) {
                return array.slice(0, indexToRemove + 1)
            } else {
                alert("Element not found in the array.")
            }
        }
    }
}
</script>

<style>
    page {
        background-color: #FFFFFF;
    }

    .dlb-search {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        border-radius: 12rpx;
        background-color: #F3F3FF;
        margin-bottom: 30rpx;
    }
    .dlb-search image {
        width: 32rpx;
        height: 32rpx;
    }
    .dlb-search text {
        font-size: 28rpx;
        color: #999;
        padding-left: 20rpx;
    }

    .dlb-cat {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30rpx;
    }

    .dlbc-item {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        height: 100rpx;
        width: 325rpx;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }

    .dlbc-item text {
        font-size: 28rpx;
        padding-right: 20rpx;
    }

    .dlbc-item image {
        width: 32rpx;
        height: 32rpx;
    }


    /* 面包屑 */
    .breadcrumb-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #F3F3FF;
        padding: 0 0 0 10rpx;
    }
    .bb-left {
        flex: 1;
        display: flex;
        align-items: center;
        font-size: 24rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .bb-left navigator {
        display: flex;
        align-items: center;
        color: #999;
        padding: 30rpx 20rpx;
    }
    .bb-left navigator image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
        margin-right: 10rpx;
    }
    .bb-left text {
        color: #999;
    }
    .bb-right {
        display: flex;
        align-items: center;
    }
    .bbr-btn {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 90rpx;
        width: 90rpx;
    }
    .bbr-btn image {
        width: 32rpx;
        height: 32rpx;
    }
    .bbr-btn::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 20rpx;
        background-color: #999;
    }
    .bbr-btn:last-child::after {
        display: none;
    }


    .sub-sview-list {
        white-space: nowrap;
        width: 100%;
        padding: 20rpx 0;
    }
    .sub-sview-item {
        display: inline-block;
        height: 70rpx;
        line-height: 70rpx;
        padding: 0 30rpx;
        text-align: center;
        font-size: 36rpx;
        font-size: 24rpx;
        border: 1px solid #E7E7E7;
        border-radius: 70rpx;
        margin-left: 30rpx;
        color: #666;
    }
    .sub-sview-item.cur {
        background-color: #390ABC;
        color: #fff;
        border: 1px solid #390ABC;
    }


    /* 资料列表 */
    .docs-list-box {
        position: relative;
        background-color: #fff;
        border-radius: 12rpx;
        padding-bottom: 30rpx;
    }
    .dlb-top {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        background-color: #FFF;
        z-index: 10;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .dlbs-empty-block {
        height: 210rpx;
    }
    .dlb-item {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 0;
    }
    .dlbic-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        font-weight: bold;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .dlbic-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
    .dlbicf-time {
        padding-right: 30rpx;
    }

    .dlbi-img {
        position: relative;
        width: 140rpx;
        height: 140rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        border: 1px solid #e7e7e7;
    }
    .doc-type {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        width: 64rpx;
        height: 64rpx;
    }
    .dlbi-cnt {
        flex: 1;
    }
</style>