<template>
    <view class="content">
        <view class="doc-detail-box">
            <view class="ddb-tit">{{ special.name }}安全管理专题文档</view>
            <view class="ddb-info">
                <view class="ddb-price">
                    <view class="ddb-nowp">{{ special.charge_credit }}<text>积分</text></view>
                    <view class="ddb-oldp">{{ special.old_charge_credit }}<text>积分</text></view>
                </view>
                <view class="ddbi-item">发布时间：<uni-dateformat :date="special.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
            </view>
            <view class="ddb-cnt">
                <view class="related-doc-box">
                    <view class="rdl-tit">专题资料列表（共{{ special.content_count }}篇）</view>
                    <view class="related-doc-list">
                        <view class="rdl-item" v-for="item in special.contents" :key="item.sid" @click="gotoContent(item.type_label, item.sid)">
                            <doc-icon :format="item.resource?.format" />
                            <text>{{ item.title }}</text>
                        </view>
                    </view>
                </view>
                <view class="ddbc-main">
                    <view class="ddb-tit">专题简介</view>
                    <view class="ddbcm-txt">{{ special.intro }}</view>
                    <!-- <view class="ddbcm-btn">
                        <text>展开更多</text>
                        <image src="../../images/icon/arrow-down-s-line.png"></image>
                    </view> -->
                </view>
            </view>
        </view>

        <!-- 底部功能菜单 -->
        <view class="ddf-empty-block"></view>
        <view class="doc-detail-foot">
            <view class="ddf-l">
                <view class="ddf-item" @click="favorite">
                    <image src="../../images/icon/star-fill.png" v-if="special.favorite"></image>
                    <image src="../../images/icon/star-line.png" v-else></image>
                    <text>收藏</text>
                </view>
                <button class="ddf-item" open-type="share">
                    <image src="../../images/icon/share-forward-2-line.png"></image>
                    <text>分享</text>
                    <view class="ddfi-tips" v-if="user">赏</view>
                </button>
            </view>

            <!-- 未登录情况，点击后直接跳转至登录页面 -->
            <view class="ddf-r" v-if="!user" @click="login">
                <view class="ddfr-top">
                    <text>尚未登录</text>
                </view>
                <view class="ddfr-foot">
                    <text>点击登录后了解更多</text>
                </view>
            </view>

            <view class="ddf-r" v-if="user && !special.download" @click="buy">
                <view class="ddfr-top">
                    <image src="../../images/icon/shopping-cart-line.png"></image>
                    <text>购买合集</text>
                </view>
                <view class="ddfr-foot">
                    <image src="../../images/icon/copper-coin-fill.png"></image>
                    <text>{{ special.charge_credit }}积分</text>
                </view>
            </view>

            <!-- 已购买 -->
            <view class="ddf-r" v-if="user && special.download">
                <view class="ddfr-top">
                    <text>已购买</text>
                </view>
                <view class="ddfr-foot">
                    <text>点击列表下载文档</text>
                </view>
            </view>
        </view>

    </view>
</template>

<script>
import api from "../../lib/api";
import {showDelayLoading, alert, getDetailUrl} from "../../lib/utils";
import DocIcon from "../../components/doc-icon.vue";
import { mapState } from "pinia";
import { useUserStore } from "../../store/user";
import {loginRequired} from "@/lib/login";
import {getAppName} from "@/lib/context";

export default {
    components: {
        DocIcon
    },
    data() {
        return {
            sid: "",
            special: {},
            appName: getAppName()
        };
    },
    computed: {
        ...mapState(useUserStore, ["user"])
    },
    onLoad(p) {
        if (!p.sid) {
            alert("页面缺少参数！");
            return;
        }
        this.sid = p.sid
        this.getSpecial()
        uni.$on("logged-in", () => {
            this.getSpecial()
        })
    },
    onUnload() {
        uni.$off("logged-in")
    },
    methods: {
        login() {
            let url = "/pages/login/index"
            uni.navigateTo({
                url
            })
        },
        getSpecial() {
            const hideLoading = showDelayLoading();
            api.get("cms/specials/" + this.sid).then(data => {
                hideLoading();
                this.special = data;
                uni.setNavigationBarTitle({
                    title: data.name
                });
            }, e => {
                hideLoading();
                alert(e.message);
            });
        },
        gotoContent(type, sid) {
            const url = getDetailUrl(type, sid)
            if (url === '') {
                alert("暂不支持打开该类型内容")
                return;
            }
            uni.navigateTo({ url });
        },
        /**
         * 收藏、取消收藏
         */
        favorite() {
            const hide = showDelayLoading(!this.special.favorite ? "收藏中" : "取消收藏中");
            const resourceUrl = "favorites/special/" + this.special.sid;
            const method = this.special.favorite ? "DELETE" : "POST";
            api.request(resourceUrl, {method}).then(() => {
                hide();
                this.special.favorite = !this.special.favorite;
            }, e => {
                hide();
                alert(e.message);
            });
        },
        buy() {
            loginRequired().then(() => {

                if (this.user.credit >= this.special.charge_credit) {
                    uni.showModal({
                        title: '购买专题',
                        content: "您将花费" + this.special.charge_credit + "积分购买该内容，确认要购买吗？",
                        confirmText: "确认购买",
                        success: (res) => {
                            if (res.confirm) {
                                // 扣除对应积分
                                const hideLoading = showDelayLoading("加载中", 200)
                                api.post("cms/specials/" + this.special.sid + "/order").then(res => {
                                    this.special = res
                                    uni.showToast({
                                        title: "购买成功",
                                        icon: "success"
                                    });
                                }).catch(err => {
                                    if (err.code === 403) {
                                        this.creditNotEnough(err.message)
                                    } else {
                                        alert(err.message)
                                    }
                                }).finally(() => {
                                    hideLoading()
                                })
                            } else if (res.cancel) {
                                console.log('用户点击取消');
                            }
                        }
                    })
                } else {
                   this.creditNotEnough(this.user.credit)
                }
            })
        },
        creditNotEnough(credit) {
            uni.showModal({
                title: '积分不足',
                content: "当前积分余额：" + credit + "，无法完成购买。请充值或者通过完成任务来获得更多积分",
                confirmText: "去充值",
                success: (res) => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: "/pages/me/recharge"
                        })
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            })
        }
    },
    onShareAppMessage() {
        let path = "/pages/document/topic-detail?sid=" + this.special.sid;

        if (this.user) {
            path += "&ref=" + this.user.uuid;
        }

		return {
			title: this.appName,
			path,
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
		};
	}
};
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .ddb-tit {
        font-size: 36rpx;
        font-weight: bold;
        line-height: 1.6;
    }
    .ddb-price {
        display: flex;
        align-items: center;
    }
    .ddb-nowp {
        color: #f54a45;
        font-weight: bold;
        font-size: 40rpx;
    }
    .ddb-nowp text {
        font-size: 24rpx;
        padding-left: 8rpx;
    }
    .ddb-oldp {
        padding-left: 20rpx;
        text-decoration: line-through;
    }
    .ddb-oldp text {
        font-size: 24rpx;
        padding-left: 8rpx;
    }
    .ddb-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #999;
        padding-top: 20rpx;
        margin-bottom: 30rpx;
    }
    .ddbi-item {
        padding-right: 30rpx;
        font-size: 24rpx;
        line-height: 1.8;
    }

    .ddbcil-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddbcil-pn {
        font-size: 24rpx;
        color: #999;
        padding: 20rpx 0;
        text-align: center;
    }

    .ddbc-check-more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        background-color: #fff;
        border-radius: 12rpx;
        width: 280rpx;
        margin: 0 auto 40rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddbc-check-more text {
        font-size: 28rpx;
        padding-right: 10rpx;
    }
    .ddbc-check-more image {
        width: 32rpx;
        height: 32rpx;
    }

    .related-doc-box {
        background-color: #fff;
        border-radius: 12rpx;
        padding-bottom: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .rdl-tit {
        padding: 30rpx 30rpx 10rpx;
        font-size: 28rpx;
        color: #999;
    }
    .rdl-item {
        display: flex;
        align-items: center;
        height: 88rpx;
        padding: 0 30rpx;
    }
    .rdl-item image {
        width: 38rpx;
        height: 38rpx;
    }
    .rdl-item text {
        flex: 1;
        font-size: 28rpx;
        padding-left: 20rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 28rpx;
    }

    /* 底部浮层 */
    .doc-detail-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        padding-bottom: env(safe-area-inset-bottom);
        height: 140rpx;
        border-top: 1px solid #F3F3FF;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddf-l {
        flex: 1;
        box-sizing: border-box;
        display: flex;
        padding: 0 10rpx;
    }
    .ddf-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 140rpx;
    }
    .ddf-item image {
        width: 36rpx;
        height: 36rpx;
    }
    .ddf-item text {
        padding-top: 10rpx;
        font-size: 28rpx;
    }
    .ddfi-tips {
        position: absolute;
        left: 50%;
        top: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 44rpx;
        height: 44rpx;
        border-radius: 100%;
        background-color: #f54a45;
        color: #fff;
        font-size: 24rpx;
        line-height: 1;
        transform: translateX(-50%);
        margin-left: 40rpx;
    }
    .ddf-r {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 300rpx;
        background-color: #f54a45;
        color: #fff;
        height: 140rpx;
    }
    .ddfr-top {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ddfr-top image {
        width: 36rpx;
        height: 36rpx;
    }
    .ddfr-top text {
        padding-left: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .ddfr-foot {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 10rpx;
    }
    .ddfr-foot image {
        width: 32rpx;
        height: 32rpx;
    }
    .ddfr-foot text {
        font-size: 24rpx;
        padding-left: 6rpx;
    }
    .ddf-view-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ddf-view-btn image {
        width: 36rpx;
        height: 36rpx;
        vertical-align: top;
        margin-top: 4rpx;
    }
    .ddf-view-btn text {
        padding-left: 20rpx;
    }
    .ddf-empty-block {
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .ddbc-main {
        background-color: #fff;
        border-radius: 12rpx;
        margin-top: 30rpx;
        padding: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddbc-main .ddb-tit {
        font-size: 28rpx;
        color: #999;
        font-weight: normal;
    }
    .ddbcm-txt {
        font-size: 28rpx;
        padding: 20rpx 0 0;
        line-height: 1.6;
    }

    .ddbcm-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60rpx;
    }
    .ddbcm-btn text {
        font-size: 24rpx;
        padding-right: 10rpx;
    }
    .ddbcm-btn image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
    }
</style>