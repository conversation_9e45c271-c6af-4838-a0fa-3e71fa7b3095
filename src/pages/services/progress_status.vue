<template>
    <view class="sdirbnbp-staus">
        <template v-if="status == 1">
            <view class="done" v-if="data?.status == 3">
                <image src="@/images/icon/checkbox-circle-fill-s.png"></image>
                <text>已完成</text>
            </view>
            <view class="process" v-if="data?.status == 1">
                <image src="@/images/icon/time-line-s.png"></image>
                <text>待办</text>
            </view>
            <view class="process" v-if="data?.status == 2">
                <image src="@/images/icon/time-line-s.png"></image>
                <text>受理中</text>
            </view>
        </template>
        <template v-else>
            <view class="done" v-if="status == 2">
                <image src="@/images/icon/checkbox-circle-fill-s.png"></image>
                <text>已完成</text>
            </view>
            <view class="process" v-if="status == 1">
                <image src="@/images/icon/time-line-s.png"></image>
                <text>受理中</text>
            </view>
        </template>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },
    props: ['status', 'data']
}
</script>

<style lang="scss">
.sdirbnbp-staus image {
    width: 32rpx;
    height: 32rpx;
}

.sdirbnbp-staus view {
    display: flex;
    align-items: center;
    font-size: 28rpx;
}
.sdirbnbp-staus view text {
    padding-left: 10rpx;
    font-weight: bold;
}
.sdirbnbp-staus .todo {
    color: #ea712e;
}
.sdirbnbp-staus .process {
    color: #390ABC;
}
.sdirbnbp-staus .done {
    color: #fba822;
}

.sdirbnbr-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
</style>
