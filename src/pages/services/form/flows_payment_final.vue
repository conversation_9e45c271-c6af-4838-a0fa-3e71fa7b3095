<template>
    <view class="sdirb-node-box">
        <view class="sdirbnbr-top">
            <view  class="sdirbnbrt-l" v-if="people">
                <image class="snb-head-img" v-if="people?.avatar" :src="people?.avatar"></image>
                <image class="snb-head-img" v-else src="@/images/icon/teacher_avatar.jpg"></image>
                <view class="sdirbnbpl-name">
                    <view class="sdirbnbpln-tit">{{people?.name}}</view>
                    <view class="sdirbnbpln-txt">{{flow?.desc}}</view>
                </view>
            </view>
            <view  class="sdirbnbrt-l" v-else>
                <image class="snb-head-img" :src="user?.avatar"></image>
                <view class="sdirbnbpl-name">
                    <view class="sdirbnbpln-tit">{{user?.nickname}}</view>
                    <view class="sdirbnbpln-txt">{{flow?.desc}}</view>
                </view>
            </view>
            <progress_status :status="status" :data="data"></progress_status>
        </view>
        <view class="sdirbnbr-cnt" >
            <view class="sdirbnbrc-pay-info" v-if="data?.data && status == 2">
                <view class="sdirbnbrcpi-item">
                    <view class="sdirbnbrcpi-tit">付款金额</view>
                    <view class="sdirbnbrcpi-txt">{{data.data.pay_amount}}元</view>
                </view>
                <view class="sdirbnbrcpi-item" v-if="data?.data.pay_order">
                    <view class="sdirbnbrcpi-tit" >支付方式</view>
                    <view class="sdirbnbrcpi-txt" v-if="data?.data.pay_order?.payment_method == 'balance'">余额支付</view>
                    <view class="sdirbnbrcpi-txt" v-else>微信支付</view>
                </view>
            </view>
            <view class="sdirbnbrc-time" v-if="data.data?.pay_order?.payment_at"><uni-dateformat :date="data.data.pay_order.payment_at" format="MM-dd hh:mm"></uni-dateformat></view>
        </view>
    </view>
</template>

<script>
import progress_status from "@/pages/services/progress_status.vue";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";

export default {
    components: {progress_status},
    props:['status', 'data', 'people', 'flow'],
    data() {
        return {};
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    }
}
</script>

<style lang="scss">
/* 服务记录 */
.sdi-record-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin: 30rpx 30rpx 0;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}
.sdirb-tit {
    padding: 30rpx;
    font-size: 32rpx;
    font-weight: bold;
}
.sdirb-node-box {
    padding: 0 30rpx 0 20rpx;
}
.sdirbnbr-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.sdirbnbrt-l {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sdirbnb-r {
    flex: 1;
    padding-left: 30rpx;
}
.snb-head-img {
    width: 100rpx;
    height: 100rpx;
    border-radius: 100%;
    border: 10rpx solid #fff;
}
.sdirbnbpl-name {
    padding-left: 20rpx;
}
.sdirbnbpln-tit {
    font-size: 28rpx;
    font-weight: bold;
}
.sdirbnbpln-txt {
    font-size: 28rpx;
    color: #999;
    padding-top: 10rpx;
}
.sdirbnbp-staus image {
    width: 32rpx;
    height: 32rpx;
}

.sdirbnbp-staus view {
    display: flex;
    align-items: center;
    font-size: 28rpx;
}
.sdirbnbp-staus view text {
    padding-left: 10rpx;
    font-weight: bold;
}
.sdirbnbp-staus .todo {
    color: #ea712e;
}
.sdirbnbp-staus .process {
    color: #390ABC;
}
.sdirbnbp-staus .done {
    color: #fba822;
}

.sdirbnbr-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sdirbnbr-cnt {
    padding: 0 0 50rpx 80rpx;
    border-left: 1px dotted #CCC;
    margin-left: 60rpx;
}
.sdirbnbr-cnt .adidci-doc-block {
    margin-bottom: 20rpx;
}
.sdirbnbrc-time {
    font-size: 24rpx;
    color: #999;
}

.sdirb-end {
    display: flex;
    align-items: flex-start;
    font-size: 28rpx;
    color: #999;
    padding-bottom: 30rpx;
    padding-left: 62rpx;
    padding-top: 6rpx;
}
.sdirbe-circle {
    width: 30rpx;
    height: 30rpx;
    border-radius: 100%;
    border: 1px solid #CCC;
    margin-top: 4rpx;
}
.sdirbe-txt {
    padding-left: 60rpx;
}
.sdirbe-txt text {
    display: block;
    font-size: 24rpx;
    padding-top: 10rpx;
}
</style>
