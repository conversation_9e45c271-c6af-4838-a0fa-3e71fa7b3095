<template>
    <view class="adidci-doc-block" @click="preview">
        <view class="adidcidb-l">
            <image src="@/images/icon/file-excel-fill.png" v-if="type == 'excel'"></image>
            <image src="@/images/icon/file-word-fill.png" v-else-if="type == 'word'"></image>
            <image src="@/images/icon/file-pdf-2-fill.png" v-else-if="type == 'pdf'"></image>
            <image src="@/images/icon/file-ppt-fill.png" v-else-if="type == 'ppt'" mode=""></image>
            <image src="@/images/icon/file-zip-fill.png" v-else-if="type == 'zip'" mode=""></image>
            <image src="@/images/icon/file-image-fill.png" v-else-if="type == 'image'" mode=""></image>
            <image src="@/images/icon/file-video-fill-2.png" v-else-if="type == 'video'" mode=""></image>
            <view class="adidcidbl-txt">
                <view class="adidcidblt-tit">{{filename}}</view>
                <view class="adidcidblt-size">{{ type }} - {{ size }} KB</view>
            </view>
        </view>
        <view class="adidcidb-r">
            <image src="@/images/icon/arrow-right-wide-line.png"></image>
        </view>
    </view>
    <view>

    </view>
</template>

<script>
export default {
    props:{
        mime: {
            type: String,
            default: ''
        },
        item: {
            type: Object,
        },
    },
    emits:["update:type"],
    computed: {
        type() {
            let type = ''
            let filename = this.filename
            if (filename.includes('pdf')) {
                type =  'pdf'
            } else if (filename.includes('xlsx') || filename.includes('xls')) {
                type = 'excel'
            }else if (filename.includes('doc') || filename.includes('docx')) {
                type = 'word'
            }else if (filename.includes('ppt') ||　filename.includes('pptx')) {
                type = 'ppt'
            }else if (filename.includes('mp4')) {
                type = 'video'
            }else if (filename.includes('zip')) {
                type = 'zip'
            } else {
                type = 'image'
            }
            this.$emit("update:type", type)
            return type
        },
        size() {
            if (this.item?.filesize)
                return this.item?.filesize
            if (this.item?.size)
                return this.item?.size
            return ""
        },
        filename() {
            if (this.item?.filename) {
                return this.item?.filename
            }
            if (this.item?.path) {
                return this.item?.path
            }
            return "";
        },
        pathSrc() {
            if (this.item?.url)
                return this.item?.url
            if (this.item?.path_src)
                return this.item?.path_src
            return "";
        }
    },
    data() {
        return {};
    },
    methods: {
        preview() {
            if (!this.pathSrc) {
                return
            }
            if (this.type == "image") {
                uni.previewImage({
                    urls: [this.pathSrc],
                });
                return;
            }
            let filePath = "";
            const task = uni.downloadFile({
                url: this.pathSrc,
                filePath,
                success(res) {
                    uni.hideLoading()
                    uni.openDocument({
                        filePath: res.tempFilePath || res.filePath,
                        showMenu: true,
                        success: () => {},
                        fail: err => {
                            uni.showModal({
                                title: '打开文件失败',
                                content: err.errMsg,
                                showCancel: false
                            });
                        }
                    });
                },
                fail(err) {
                    uni.hideLoading();
                    uni.showModal({
                        title: '下载文件失败',
                        content: err.errMsg,
                        showCancel: false
                    });
                }
            });

            task.onProgressUpdate(res => {
                uni.showLoading({
                    title: '加载中 ' + res.progress + '%',
                    mask: true
                });
            });
        }
    },
}
</script>

<style lang="scss">

.adidci-doc-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12rpx;
    padding: 20rpx;
    background-color: #f3f3ff;
    margin-top: 20rpx;
}
.adidcidbl-txt {
    padding-left: 20rpx;
}
.adidcidb-l {
    display: flex;
    align-items: center;
}
.adidcidb-l image {
    width: 64rpx;
    height: 64rpx;
}
.adidcidblt-tit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300rpx;
    font-size: 28rpx;
}
.adidcidblt-size {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}
.adidcidb-r image {
    width: 32rpx;
    height: 32rpx;
}
</style>
