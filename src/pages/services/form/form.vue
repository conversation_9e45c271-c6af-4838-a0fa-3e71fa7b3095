<template>
    <view class="content" v-if="!loading">
        <template v-if="isStep">
            <submit-step @formStep="formStep" v-model:enterprise-id="enterprise_id" v-model:industry-id="industry_id" v-model:project_id="project_id"></submit-step>
        </template>
        <template v-else>
            <view v-for="(input, index) in inputs" :key="index">
                <InputDetail
                    :keyIndex="index"
                    :title="input.title"
                    :desc="input.desc"
                    :type="input.type"
                    :status="input.status"
                    :id="input.id"
                    :reject_reason="input.reject_reason"
                    v-model:value="input.value"
                    :isRequired="input.is_required"
                    :options="input.options"
                    @inputUpdate="inputUpdate"
                    @inputUpdateDelFiles="inputUpdateDelFiles"
                ></InputDetail>
            </view>
            <view class="sic-foot-eb"></view>
            <view class="sic-foot">
                <view class="sicf-btn" @click="saveData">
                    <text>提交需求</text>
                </view>
            </view>
        </template>
        <!-- 订阅确认弹窗 -->
<!--        <uni-popup ref="subscribePopup" type="dialog">
            <uni-popup-dialog
                title="订阅提醒"
                content="是否订阅相关消息通知？"
                :show-cancel="true"
                @confirm="handleSubscribe"
                @cancel="cancelSubscribe"
            />
        </uni-popup>-->
    </view>
</template>

<script>
import InputDetail from '@/components/form/InputDetail.vue';
import api from "@/lib/api";
import {alert, showDelayLoading} from "@/lib/utils";
import submitStep from "../operate/submit-step.vue"
import {checkOpenid, subscribeMessage} from "@/lib/wechat";
export default {
    components: {
        InputDetail,
        submitStep
    },
    watch: {
        inputs: {
            handler(newValue) {
                //console.log('用户信息已更新:', newValue);
            },
            deep: true // 开启深度监听
        }
    },
    data() {
        return {
            sid: '',
            flow_step_id: '',
            project_id: '',
            industry_id: 0,
            enterprise_id: 0,
            inputs: [],
            is_bind_category: 1,
            back: 1,
            isStep: false,
            loading: true,
            industry: [],
            enterprise: [],
        };
    },
    onLoad(e) {
        this.sid = e.sid
        this.flow_step_id = e.flow_step_id
        this.project_id = e.project_id
        if (e.back) {
            this.back = e.back
        }
        this.initData()
        checkOpenid()
        uni.$on("mp-subscribe-message", () => {
            this.postData()
        })
    },
    onUnload() {
        uni.$off("mp-subscribe-message")
    },
    methods: {
        initData(){
            api.get(`ers/projects/${this.project_id}`).then(res => {
                this.is_bind_category = res.is_bind_category
                if (this.is_bind_category && this.sid == 0) {
                    this.isStep = true
                }
                if (!this.is_bind_category && this.sid == 0) {
                    this.formStep()
                }
                if (this.sid != 0) {
                    this.orderData()
                    this.FormData()
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                this.loading = false
            })
        },
        orderData() {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get(`ers/orders/${this.sid}?project_id=${this.project_id}`).then(res => {
                this.industry_id = res.industry_id
                this.enterprise_id = res.enterprise_id
                this.$nextTick( () => {
                    this.isStep = this.is_bind_category && (!this.industry_id || !this.enterprise_id);
                })
                hideLoading()
            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(() => {
                this.loading = false
            })
        },
        FormData() {
            let data = {
                enterprise_id: this.enterprise_id,
                industry_id: this.industry_id,
            }
            api.get(`ers/orders/${this.sid}/form/${this.flow_step_id}`, data).then(res => {
                this.inputs = res.form.inputs
            }).catch(err => {
                alert(err.message)
            })
        },
        inputUpdate(e) {
            this.inputs[e.key].value = e.value
            this.inputs[e.key].isEdit = true
        },
        inputUpdateDelFiles(e) {
            if (this.inputs[e.index].delFile) {
                this.inputs[e.index].delFile.push(e.value+",delete")
            } else {
                this.inputs[e.index].delFile = [e.value+",delete"]
            }
            let delIndex  = this.inputs[e.index].value.findIndex(res => res.path == e.value)
            this.inputs[e.index].value.splice(delIndex, 1)
            this.inputs[e.index].isEdit = true
        },
        saveData() {
            let error = this.inputs.find(res => {
                if (res?.is_required && (res.type == 'file' || res.type == 'image' || res.type == 'checkbox') && !res.value?.length) {
                    return res;
                }

                if (res?.is_required && !res.value) {
                    return res;
                }
            })

            if (error) {
                let content = '';
                if (error.type == 'file') {
                    content = `上传文件${error.title}不能为空`
                } else {
                    content = `${error.title}不能为空`
                }
                uni.showModal({
                    content: content,
                    showCancel: false
                })
                return
            }

            subscribeMessage()
        },
        postData() {
            let inputs = {};
            this.inputs.forEach((res, index) => {
                if (res.isEdit) {
                    if (res.type == 'file' || res.type == 'image') {
                        inputs[res.id] = [];
                        if (res?.delFile) {
                            inputs[res.id] = res?.delFile
                        }
                        res.value.forEach(file => {
                            console.log(file)
                            inputs[res.id].push(file.key)
                        })
                        console.log(inputs[res.id])
                    } else {
                        inputs[res.id] = res.value
                    }
                }
            })

            if (this.sid != 0) {
                this.postInput(inputs)
            } else {
                let data = {
                    project_id: this.project_id,
                    industry_id: this.industry_id,
                    enterprise_id: this.enterprise_id,
                }
                api.post('ers/orders', data).then(res => {
                    this.sid = res.sid
                    this.postInput(inputs)
                }).catch(err => {
                    alert(err.message)
                })
            }
        },
        postInput(inputs) {
            let hideLoading = showDelayLoading("提交中", 200)
            api.post(`ers/orders/${this.sid}/form/${this.flow_step_id}`, {inputs: inputs}).then(res => {
                hideLoading()
                uni.$emit("services_list_order")
                uni.showToast({
                    title: "提交成功",
                    icon: "success",
                    duration: 1500,
                    success: () => {
                        this.goDetail()
                    }
                });
            }).catch(err => {
                hideLoading()
                alert(err.message)
            })
        },
        goDetail() {
            setTimeout(() => {
                uni.redirectTo({
                    url: '/pages/services/detail?sid=' + this.sid + "&project_id=" + this.project_id
                })
            }, 1500)
        },
        getForm() {
            let data = {
                project_id: this.project_id,
                industry_id: this.industry_id,
                enterprise_id: this.enterprise_id,
                step_id: this.flow_step_id,
            }
            api.get(`ers/form/inputs`, data).then(res => {
                this.inputs = res.form.inputs
                this.loading = false
            }).catch(err => alert(err.message))
        },
        formStep() {
            this.isStep = false
            api.get(`ers/projects/${this.project_id}`).then(res => {
                let form = res.steps.find( res => res.module == 'form');
                this.flow_step_id = form.id
                this.getForm()
            })


        }
    },
}
</script>

<style>
/* 添加样式 */
/* 底部按钮 */
.sic-foot {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    z-index: 10;
}
.sicf-btn {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #390abc;
    color: #fff;
    border-radius: 12rpx;
    margin-bottom: env(safe-area-inset-bottom);
}
.sicf-btn image {
    width: 36rpx;
    height: 36rpx;
}
.sicf-btn text {
    font-weight: bold;
}
.sicf-btn.disabled {
    background-color: #999;
}
.sic-foot-eb {
    height: 148rpx;
    padding-bottom: env(safe-area-inset-bottom);
}
</style>