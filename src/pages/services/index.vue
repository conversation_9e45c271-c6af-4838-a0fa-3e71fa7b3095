<template>
    <view class="content">
        <view class="s-record-link">
            <view class="srl-tit">服务订单</view>
            <view class="srl-list" >
                <view class="srl-item" @click="goList(1)">
                    <view class="srli-num">{{ getStatusProject(1) }}</view>
                    <view class="srli-txt">待办事项</view>
                </view>
                <view class="srl-item" @click="goList(2)">
                    <view class="srli-num">{{ getStatusProject(2) }}</view>
                    <view class="srli-txt">受理中</view>
                </view>
                <view class="srl-item" @click="goList(3)">
                    <view class="srli-num">{{ getStatusProject(3) }}</view>
                    <view class="srli-txt">已完结</view>
                </view>
                <view class="srl-item" @click="goList('')">
                    <view class="srli-num">{{toCount}}</view>
                    <view class="srli-txt">全部</view>
                </view>
            </view>
        </view>

        <view class="services-list-box">
            <view class="slb-tit">服务项目</view>
            <view class="slb-list">
                <view class="slbl-item" @click="project(item)" v-for="(item, index) in projectList" :key="index">
                    <image :src="item.icon_src"></image>
                    <text>{{ item.title }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert} from "@/lib/utils";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {loginRequired} from "@/lib/login";
import {getAppName} from "@/lib/context";

export default {
    data() {
        return {
            projectList: [

            ],
            orderProgress:  [
                {
                    "status_count": 0,
                    "status": 1
                },
                {
                    "status": 2,
                    "status_count": 0
                },
                {
                    "status": 3,
                    "status_count": 0
                }
            ],
            appName: getAppName()
        }
    },
    onLoad() {
        this.listData()
        if (this.user) {
            this.initData()
        } else {
            loginRequired().then(() => {

            })
        }
        uni.$on("services_list_order", () => {
            this.initData()
        })
    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("services_list_order")
    },
    computed: {
        toCount() {
            let to = 0;
            this.orderProgress.forEach(res => {
                to += res.status_count
            })
            return to
        },
        ...mapState(useUserStore, ["user"]),
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.initData()

    },
    watch: {
        user() {
            if (this.user) {
                this.initData()
            } else {
                this.projectList = []
                this.orderProgress = [
                    {
                        "status_count": 0,
                        "status": 1
                    },
                    {
                        "status": 2,
                        "status_count": 0
                    },
                    {
                        "status": 3,
                        "status_count": 0
                    }
                ]
            }
        }
    },
    methods: {
        listData() {
            api.get("ers/projects").then(res => {
                this.projectList = res
            })
        },
        initData() {
            api.get("ers/page/hall").then(res => {
                this.orderProgress = res.order_progress
                //this.projectList = res.project_list
            }).catch(err => alert(err.message)).finally(() => {
                uni.stopPullDownRefresh();
            })
        },
        project(data) {
            loginRequired().then(() => {
                uni.navigateTo({
                    url: '/pages/services/intro?id=' + data.id
                })
            })

        },
        getStatusProject(status) {
            return this.orderProgress.find(res => res.status == status).status_count
        },
        goList(type) {
            loginRequired().then(() => {
                uni.navigateTo({
                    url: '/pages/services/list?type=' + type
                })
            })
        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: '/pages/services/index'
        };
    },
}
</script>

<style>
    page {
        background-color:  #f3f3ff;
    }
    .content {
        padding-top: 30rpx;
        padding-bottom: 10rpx;
    }
    .s-record-link {
        background-color: #fff;
        border-radius: 12rpx;
        margin: 0 30rpx 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .srl-tit {
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        font-weight: bold;
        padding-left: 30rpx;
        border-bottom: 1px solid #F3F3FF;
    }
    .srl-list {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 20rpx 20rpx;
    }
    .srl-item {
        flex: 1;
        text-align: center;
        padding: 30rpx 0;
    }
    .srli-num {
        font-size: 48rpx;
        font-weight: bold;
        font-family: Arial, Helvetica, sans-serif;
        padding-bottom: 16rpx;
    }
    .srli-txt {
        font-size: 24rpx;
        color: #999;
    }


    .services-list-box {
        background-color: #fff;
        border-radius: 12rpx;
        margin: 0 30rpx 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .slb-tit {
        height: 80rpx;
        line-height: 80rpx;
        font-size: 28rpx;
        font-weight: bold;
        padding-left: 30rpx;
        border-bottom: 1px solid #F3F3FF;
    }
    .slb-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
    }
    .slbl-item {
        flex: 1 1 45%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 260rpx;
        border-right: 1px solid #F3F3FF;
        border-bottom: 1px solid #F3F3FF;
    }
    .slb-list > .slbl-item:nth-child(2n) {
      border-right-width: 0;
    }
    .slb-list > .slbl-item:nth-last-child(-n+1) {
      border-bottom-width: 0;
    }
    .slbl-item image {
        width: 80rpx;
        height: 80rpx;
    }
    .slbl-item text {
        font-size: 28rpx;
        padding-top: 30rpx;
    }
</style>