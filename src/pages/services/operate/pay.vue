<template>
    <view class="content">
        <view class="plan-box" v-if="solution_preview?.data?.files">
            <view class="pb-tit">方案信息</view>
            <view class="pb-cnt pb-cnt-preview">
                <template v-for="(item, index) in solution_preview?.data?.files" :key="index">
                    <file_img :mime="item.mime" :item="item"/>
                </template>
            </view>
        </view>
        <view class="plan-box">
            <view class="pb-tit">方案金额</view>
            <view class="pb-cnt">
                <view class="pbc-warp"><text>￥</text>{{pay_amount}}</view>
            </view>
        </view>

        <view class="plan-box">
            <view class="pb-tit">支付方式</view>
            <view class="pb-cnt">
                <view class="pbc-radio-group">
                    <view class="pbcrg-item" @click="buyType = 'amount'" :class="{checked: buyType == 'amount'}">
                        <view class="pbcrgi-radio">
                            <view class="pbcrgir-check">
                                <image src="../../../images/icon/check-line.png"></image>
                            </view>
                        </view>
                        <image class="pay-icon" src="../../../images/icon/wechat-pay-fill.png"></image>
                        <text>微信支付</text>
                    </view>

                    <view class="pbcrg-item " @click="buyType = 'balance'"  :class="{checked: buyType == 'balance'}">
                        <view class="pbcrgi-radio">
                            <view class="pbcrgir-check">
                                <image src="../../../images/icon/check-line.png"></image>
                            </view>
                        </view>
                        <image class="pay-icon" src="../../../images/icon/wallet-2-fill.png"></image>
                        <text>余额支付</text>
                    </view>

<!--                    <view class="pbcrg-item">-->
<!--                        <view class="pbcrgi-radio">-->
<!--                            <view class="pbcrgir-check">-->
<!--                                <image src="../../../images/icon/check-line.png"></image>-->
<!--                            </view>-->
<!--                        </view>-->
<!--                        <image class="pay-icon" src="../../../images/icon/bank-card-fill.png"></image>-->
<!--                        <text>对公转账</text>-->
<!--                    </view>-->
                </view>
            </view>
        </view>

        <view class="sdi-foot-eb"></view>
        <view class="sdi-foot-box">
            <view class="adifb-main">
                <view class="adifbm-money">
                    <text>￥</text>{{pay_amount}}
                </view>
                <view class="adifbm-topay" @click="pay">确认支付</view>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {makePayment} from "@/lib/pay";
import {alert, getFormatType, showDelayLoading} from "@/lib/utils";
import File_img from "@/pages/services/form/file_img.vue";

export default {
    components: {File_img},
    data() {
        return {
            sid: '',
            buyType: 'balance',
            flow_step_id: '',
            project_id: '',
            pay_amount: 0,
            to_pay_amount: 0,
            module: 'payment_final',
            inputs: [],
            solution_preview: {},
            flows: [],

        };
    },
    onLoad(e) {
        this.sid = e.sid
        this.flow_step_id = e.flow_step_id
        this.project_id = e.project_id
        this.initData()
        this.orderData()
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    },
    methods: {
        orderData() {
            const hideLoading = showDelayLoading("加载中", 200)

            api.get(`ers/orders/${this.sid}?project_id=${this.project_id}`).then(res => {
                this.flows = res.flows
                this.solution_preview = this.flows.find(res => res.module == 'solution_preview')
                let flow = this.flows.find(res => res.status == 1);
                if(flow) {
                    this.module = flow.module
                }
                hideLoading()
            }).catch(err => {
                hideLoading()
                alert(err.message)
            })
        },
        initData() {
            api.get(`ers/orders/${this.sid}/payment/${this.flow_step_id}`).then(res => {
                this.pay_amount = res.payment.pay_amount
                this.to_pay_amount = res.payment.total_amount

            }).catch(err => alert(err.message))
        },
        pay() {
            let hideLoading = showDelayLoading("请稍后", 200)

            api.post(`ers/orders/${this.sid}/payment/${this.flow_step_id}`).then(res => {
                if (this.buyType == 'amount') {
                    this.buyFromAmount(res.order_no)
                } else if (this.buyType == 'balance') {
                    this.buyFromCredit(res.order_no)
                }
                hideLoading()
            }).catch(err => {
                hideLoading()
                alert(err.message)
            })
        },
        buyFromCredit(order_no) {
            let balance = parseFloat(this.user.balance).toFixed(2)
            console.log(balance)
            uni.showModal({
                content: "您将花费" + this.pay_amount + "余额购买该内容，确认要购买吗？",
                confirmText: "确认购买",
                success: (res) => {
                    if (res.confirm) {
                        // 扣除对应积分
                        api.post('balances/payment',{order_no: order_no}).then(res => {
                            uni.hideLoading();
                            uni.$emit("services_list_order")

                            uni.showToast({
                                title: "支付成功",
                                icon: "success",
                                duration: 1500,
                                success: () => {
                                    setTimeout(() => {
                                        uni.navigateBack()
                                    },1500)
                                }
                            });
                        }).catch(err => {
                            alert(err.message)
                        })
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            })
        },
        creditNotEnough(credit) {
            uni.showModal({
                title: '余额不足',
                content: "当前余额：" + credit + "，无法完成购买。请充值或者通过完成任务来获得更多余额",
                confirmText: "确定",
                success: (res) => {

                }
            })
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        buyFromAmount(order_no) {
            uni.showLoading({
                title: "发起支付中",
                mask: true
            });
            uni.hideLoading();
            makePayment(order_no, "wechat", "mp").then(() => {
                uni.hideLoading();
                uni.$emit("services_list_order")

                uni.showToast({
                    title: "支付成功",
                    icon: "success",
                    duration: 1500,
                    success: () => {
                        setTimeout(() => {
                            uni.navigateBack()
                        },1500)
                    }
                });
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.plan-box {
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin: 30rpx;
}

.pb-tit {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 30rpx;
    border-bottom: 1px solid #f3f3ff;
    font-size: 28rpx;
}

.adidci-doc-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx;
    background-color: #fff;
}
.adidcidbl-txt {
    padding-left: 20rpx;
}
.adidcidb-l {
    display: flex;
    align-items: center;
}
.adidcidb-l image {
    width: 64rpx;
    height: 64rpx;
}
.adidcidblt-tit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300rpx;
    font-size: 28rpx;
}
.adidcidblt-size {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}
.adidcidb-r image {
    width: 32rpx;
    height: 32rpx;
}
.pb-cnt-preview {
    padding-right: 20rpx;
    padding-left: 20rpx;
    padding-bottom: 20rpx;
}
.pb-cnt .adidcidb-l image {
    width: 120rpx;
    height: 120rpx;
}

.pb-btn-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid #f3f3ff;
    padding: 20rpx 30rpx;
}

.pbbg-btn {
    height: 88rpx;
    line-height: 88rpx;
    text-align: center;
    justify-content: center;
    font-weight: bold;
    background-color: #F3F3FF;
    font-size: 28rpx;
    padding: 0 30rpx;
    border-radius: 12rpx;
}
.pbbg-btn.confirm {
    flex: 1;
    background-color: #390ABC;
    margin-left: 30rpx;
    color: #fff;
}

.pb-comfirm-info {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
    border-top: 1px solid #f3f3ff;
}
.pb-comfirm-info image {
    width: 32rpx;
    height: 32rpx;
    vertical-align: top;
}
.pb-comfirm-info text {
    padding-left: 20rpx;
    font-size: 28rpx;
    color: #fba822;
}

.pbc-warp {
    padding: 30rpx;
}
.pbc-warp text {
    font-size: 24rpx;
}



/* 支付方式选择 */
.pbc-radio-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, auto);
    padding: 10rpx;
}

.pbcrg-item {
    display: flex;
    align-items: center;
    border: 1px solid #F3F3FF;
    padding: 20rpx;
    margin: 10rpx;
    border-radius: 12rpx;
    background-color: #f3f3ff;
}

.pbcrgir-check {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    border: 1px solid #F3F3FF;
    border-radius: 100%;
    background-color: #fff;
}
.pbcrgir-check image {
    width: 32rpx;
    height: 32rpx;
    vertical-align: top;
}
.pbcrg-item text {
    font-size: 28rpx;
}
.pay-icon {
    width: 48rpx;
    height: 48rpx;
    margin: 0 10rpx 0 20rpx;
}

.pbcrg-item.checked {
    border: 1px solid #390ABC;
}
.pbcrg-item.checked .pbcrgir-check {
    background-color: #390ABC;
}
.pbcrg-item.checked text {
    font-weight: bold;
    color: #390ABC;
}


/* 底部菜单 */
.sdi-foot-box {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 10;
}
.adifb-main {
    display: flex;
    align-items: cneter;
    margin: 20rpx;
}
.adifbm-todo-btn {
    flex: 1;
    height: 100rpx;
    text-align: center;
    border: 1px solid #390ABC;
    background-color: #f3f3ff;
    color: #090abc;
    font-weight: bold;
    border-radius: 12rpx;
    line-height: 100rpx;
}
.adifbm-process {
    flex: 1;
    height: 100rpx;
    text-align: center;
    background-color: #F3F3FF;
    color: #999;
    border-radius: 12rpx;
    line-height: 100rpx;
}
.adifbm-more-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100rpx;
    height: 100rpx;
    border-radius: 12rpx;
    margin-left: 20rpx;
}
.adifbm-more-btn image {
    width: 32rpx;
    height: 32rpx;
}
.sdi-foot-eb {
    height: 140rpx;
    padding-bottom: env(safe-area-inset-bottom);
}
.adifbm-money {
    height: 100rpx;
    line-height: 100rpx;
    padding: 0 60rpx 0 0;
    font-size: 48rpx;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
}
.adifbm-money text {
    font-size: 24rpx;
}
.adifbm-topay {
    flex: 1;
    height: 100rpx;
    text-align: center;
    border: 1px solid #390ABC;
    background-color: #390ABC;
    color: #FFF;
    font-weight: bold;
    border-radius: 12rpx;
    line-height: 100rpx;
}
</style>
