<template>
    <view class="content">
        <view class="compay-pay-info">
            <view class="cpi-tit">第一步：对公汇款</view>
            <view class="cpi-cnt">
                <view class="cpi-p">账号名称：合肥网漫科技有限公司</view>
                <view class="cpi-p">开户银行：111</view>
                <view class="cpi-p">银行账号：222</view>
                <view class="cpi-p">开户地：222</view>
                <view class="cpi-p">汇款金额：￥2986.00</view>
                <view class="cpi-p">汇款备注：编号20301</view>
                <view class="cpi-tips">
                    <text>请在对公转账时务必备注订单号</text>
                </view>
            </view>
            <view class="cpicb-box">
                <view class="cpi-copy-btn">一键复制</view>
            </view>

        </view>

        <view class="compay-pay-info">
            <view class="cpi-tit">第二步：上传银行盖章回执单</view>
            <view class="upload-file">
                <view class="uf-upbtn">
                    <image src="../../../images/icon/upload-2-line.png"></image>
                    <text>上传文件</text>
                </view>
            </view>

            <view class="adidci-doc-block" style="border: none;">
                <view class="adidcidb-l">
                    <!-- <image src="../../../images/icon/file-excel-fill.png"></image> -->
                    <!-- <image src="../../../images/icon/file-word-fill.png"></image> -->
                    <!-- <image src="../../../images/icon/file-pdf-2-fill.png"></image> -->
                    <!-- <image src="../../../images/icon/file-ppt-fill.png" mode=""></image> -->
                    <!-- <image src="../../../images/icon/file-zip-fill.png" mode=""></image> -->
                    <image src="../../../images/icon/file-image-fill.png" mode=""></image>
                    <view class="adidcidbl-txt">
                        <view class="adidcidblt-tit">如果文本长度超过了元素的宽度，它就会被截断，并在末尾显示省略号。</view>
                        <view class="adidcidblt-size">PNG - 12.22 KB</view>
                    </view>
                </view>
                <view class="adidcidb-r">
                    <image src="../../../images/icon/arrow-right-wide-line.png"></image>
                </view>
            </view>
        </view>


        <view class="sdi-foot-eb"></view>
        <view class="sdi-foot-box">
            <view class="adifb-main">
                <view class="adifbm-todo-btn disabled">确认已付款</view>
            </view>
        </view>
    </view>
</template>

<script>

</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .compay-pay-info {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        margin: 30rpx;
    }
    .cpi-tit {
        padding: 30rpx;
        border-bottom: 1px solid #f3f3f3;
        font-size: 32rpx;
        font-weight: bold;
    }
    .cpi-cnt {
        padding: 30rpx 30rpx 0;
    }
    .cpi-h3 {
        font-weight: 32rpx;
        font-weight: bold;
        padding-bottom: 16rpx;
    }
    .cpi-p {
        font-size: 28rpx;
        padding-bottom: 20rpx;
        line-height: 1.6;
    }
    .cpi-tips {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #EA712E;
        font-size: 28rpx;
        padding-bottom: 20rpx;
    }
    .cpi-tips image {
        width: 32rpx;
        height: 32rpx;
    }
    .cpi-tips text {
        padding-left: 12rpx;
        padding-bottom: 4rpx;
    }

    .cpicb-box {
        padding: 0 20rpx 20rpx;
    }
    .cpi-copy-btn {
        height: 80rpx;
        text-align: center;
        border: 1px solid #390ABC;
        background-color: #f3f3ff;
        color: #090abc;
        font-size: 28rpx;
        font-weight: bold;
        border-radius: 12rpx;
        line-height: 80rpx;
    }

    .custome-service {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        margin: 30rpx;
        height: 100rpx;
    }
    .custome-service image {
        width: 32rpx;
        height: 32rpx;
    }
    .custome-service text {
        padding-left: 10rpx;
        font-size: 28rpx;
        font-weight: bold;
    }



    /* 上传框 */
    .upload-file {
        position: relative;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding-bottom: 1px;
        margin-bottom: 30rpx;
    }
    .uf-tit {
        font-size: 28rpx;
        padding: 30rpx 30rpx 0;
    }
    .uf-tit text {
        color: red;
        font-weight: bold;
        padding-left: 8rpx;
    }
    .uf-tips {
        font-size: 24rpx;
        color: #999;
        padding: 10rpx 30rpx 20rpx;
    }
    .uf-upbtn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        color: #390ABC;
    }
    .uf-upbtn image {
        width: 32rpx;
        height: 32rpx;
    }
    .uf-upbtn text {
        font-size: 28rpx;
        padding-left: 10rpx;
        font-weight: bold;
    }

    /* 上传后文件样式 */
    .adidci-doc-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border: 1px solid #e7e7e7;
        border-radius: 12rpx;
        padding: 20rpx;
    }
    .adidcidbl-txt {
        padding-left: 20rpx;
    }
    .adidcidb-l {
        display: flex;
        align-items: center;
    }
    .adidcidb-l image {
        width: 64rpx;
        height: 64rpx;
    }
    .adidcidblt-tit {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 300rpx;
        font-size: 28rpx;
    }
    .adidcidblt-size {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .adidcidb-r image {
        width: 32rpx;
        height: 32rpx;
    }

    .upload-file .adidci-doc-block {
        margin: 0 30rpx 30rpx;
        background-color: #F3F3FF;
        border: none;
    }


    /* 底部菜单 */
    .sdi-foot-box {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding-bottom: env(safe-area-inset-bottom);
        z-index: 10;
    }
    .adifb-main {
        display: flex;
        align-items: cneter;
        margin: 20rpx;
    }
    .adifbm-todo-btn {
        flex: 1;
        height: 100rpx;
        text-align: center;
        background-color: #390ABC;
        color: #FFFFFF;
        font-weight: bold;
        border-radius: 12rpx;
        line-height: 100rpx;
    }
    .adifbm-todo-btn.disabled {
        background-color: #F3F3F3;
        color: #999;
    }
    .adifbm-process {
        flex: 1;
        height: 100rpx;
        text-align: center;
        background-color: #F3F3FF;
        color: #999;
        border-radius: 12rpx;
        line-height: 100rpx;
    }
    .adifbm-more-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 100rpx;
        border-radius: 12rpx;
        margin-left: 20rpx;
    }
    .adifbm-more-btn image {
        width: 32rpx;
        height: 32rpx;
    }
    .sdi-foot-eb {
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }

</style>
