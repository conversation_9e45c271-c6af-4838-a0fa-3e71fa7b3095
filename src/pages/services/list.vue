<template>
    <view class="content">
        <view class="top-fixed-box">
            <view class="tab-nomal">
                <view class="tab-item p-rel" @click="tabStatus(1)" :class="{cur: status == 1}">待办</view>
                <view class="tab-item p-rel" @click="tabStatus(2)" :class="{cur: status == 2}">受理中</view>
                <view class="tab-item p-rel" @click="tabStatus(3)" :class="{cur: status == 3}">已完结</view>
                <view class="tab-item p-rel" @click="tabStatus('')" :class="{cur: status == ''}">全部</view>
            </view>
        </view>

        <view class="services-list">
            <view class="services-detail-info" @click="detail(item)" v-for="(item, index) in list" :key="index">
                <view class="sdi-left">
                    <view class="sdi-sn">编号：{{ item.sid }}</view>
                    <view class="sdi-top">
                        <view class="sdit-name">{{item.project.title}}</view>
                        <view class="sdit-staus end" v-if="item.status == 3">已完结</view>
                        <view class="sdit-staus processed" v-if="item.status == 2">受理中</view>
                        <view class="sdit-staus todo" v-if="item.status == 1">待办</view>

                    </view>
                    <!-- <view class="sdi-cnt">
                        <image :src="user?.avatar"></image>
                        <text>{{ user?.nickname  }}</text>
                    </view> -->
                    <view class="sdi-foot">
                        提交于 <uni-dateformat :date="item.created_at" format="yyyy年MM月dd日 hh:mm"></uni-dateformat>
                    </view>
                </view>
                <image class="sdir-icon" src="../../images/icon/arrow-right-wide-line.png"></image>
            </view>
            <view class="no-data-nomal-box" v-if="!loading && !list.length">
                <view class="ndnb-icon">
                    <image src="@/images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无数据</text>
            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert, showDelayLoading} from "@/lib/utils";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";

export default {
    data() {
        return {
            status: 1,
            list: [],
            loading: true
        }
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    },
    onLoad(e) {
        this.status = e.type
        this.initData()
        uni.$on("services_list_order", () => {
           this.tabStatus(this.status)
        })
    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("services_list_order")
    },
    methods: {
        initData() {
            const hideLoading = showDelayLoading("加载中", 200)
            this.loading = true
            api.get("ers/page/order", {status: this.status}).then(res => {
                this.list = res;
                hideLoading()
            }).catch(err => {
                alert(err.message)
                hideLoading()
            }).finally(() => {
                this.loading = false
            })
        },
        tabStatus(status) {
            this.status = status
            this.list = []
            this.initData()
        },
        detail(data) {
            uni.navigateTo({
                url: '/pages/services/detail?sid=' + data.sid + "&project_id=" + data.project.id+ "&title=" + data.project.title
            })
        }
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .top-fixed-box {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f3f3ff;
        height: 100rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }

    .tfb-eb {
        height: 100rpx;
    }

    /* tab样式 */
    .tab-nomal {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 30rpx;
    }

    .tab-item {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 30rpx;
        color: #999;
    }

    .tab-item.cur {
        position: relative;
        font-weight: 700;
        color: #090abc;
    }
    .tab-item.cur::after {
        content: '';
        position: absolute;
        height: 8rpx;
        width: 20rpx;
        background-color: #090abc;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
    }


    /* 服务订单列表 */
    .services-list {
        padding-bottom: 30rpx;
    }
    .services-detail-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        border-radius: 12rpx;
        margin: 30rpx 30rpx 0;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        padding: 30rpx;
    }
    .sdi-sn {
        font-size: 24rpx;
        color: #999;
        padding-bottom: 20rpx;
    }
    .sdi-top {
        display: flex;
        align-items: center;
    }
    .sdit-name {
        font-size: 32rpx;
        font-weight: bold;
    }
    .sdir-icon {
        width: 32rpx;
        height: 32rpx;
    }
    .sdit-staus {
        font-weight: bold;
        font-size: 28rpx;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
        margin-left: 20rpx;
    }
    .sdit-staus.todo {
        border: 1px solid #FF0000;
        color: #FF0000;
    }
    .sdit-staus.processed {
        border: 1px solid #090abc;
        color: #090abc;
    }
    .sdit-staus.end {
        border: 1px solid #999;
        color: #999;
    }
    .sdi-cnt {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
    }
    .sdi-cnt image {
        width: 60rpx;
        height: 60rpx;
        border-radius: 100%;
    }
    .sdi-cnt text {
        padding-left: 20rpx;
        font-size: 24rpx;
    }
    .sdi-foot {
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
</style>