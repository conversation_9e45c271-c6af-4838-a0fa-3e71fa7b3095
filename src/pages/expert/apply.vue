<template>
    <view class="content">
        <view class="apply-form-box">
            <view class="single-txt">
                <view class="st-name">{{ form.name.label }}
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" v-model="form.name.value" placeholder="请输入姓名"/>
                </view>
            </view>
            <view class="single-txt">
                <view class="st-name">{{ form.phone.label }}
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" type="number" v-model="form.phone.value" placeholder="请输入联系电话"/>
                </view>
            </view>
            <view class="radio-open">
                <view class="ro-name">{{ form.gender.label }}
                    <text>*</text>
                </view>
                <view class="ro-part">
                    <radio-group @change="radioChange">
                        <label v-for="(item, index) in genders" :key="index">
                            <radio :value="item.value" :checked="item.value == form.gender.value"  color="#390ABC" style="transform:scale(0.8) "/>
                            <text>{{ item.label }}</text>
                        </label>
                    </radio-group>
                </view>
            </view>
            <view class="single-txt">
                <view class="st-name">常住地
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" v-model="form.residence.value" type="text" placeholder="请输入常住地"/>
                </view>
            </view>
            <view class="single-txt">
                <view class="st-name">{{ form.education.label }}
                    <text>*</text>
                </view>
                <picker class="dropdown-part" @change="bindPickerChange" :value="form.education.value" :range="config.educations">
                    <text v-if="form.education.value">{{ form.education.value }}</text>
                    <text v-else>请选择</text>
                    <image src="../../images/icon/arrow-down-s-line.png"></image>
                </picker>
            </view>
            <view class="single-txt">
                <view class="st-name">毕业所学专业
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" v-model="form.major.value" type="text" placeholder="请输入毕业所学专业"/>
                </view>
            </view>
            <view class="single-txt">
                <view class="st-name">{{ form.occupation.label }}
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" v-model="form.occupation.value" placeholder="请输入职称/职务"/>
                </view>
            </view>
            <view class="single-txt">
                <view class="st-name">{{ form.industry.label }}
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" v-model="form.industry.value" placeholder="请输入从事专业"/>
                </view>
            </view>
            <view class="single-txt">
                <view class="st-name">{{ form.work_year.label }}
                    <text>*</text>
                </view>
                <view class="st-input">
                    <input class="uni-input" type="number" v-model="form.work_year.value" placeholder="请输入从事专业年限"/>
                </view>
            </view>
            <view class="checkbox-fold">
                <view class="cf-name">{{ form.fields.label }}
                    <text>*</text>
                </view>
                <view class="cf-box" @click="this.$refs.fieldsPop.open('bottom')">
                    <text v-if="form.fields.value">{{ form.fields_text.value }}</text>
                    <text v-else>请选择</text>
                    <image src="../../images/icon/arrow-down-s-line.png"></image>
                </view>
            </view>
            <view class="checkbox-fold">
                <view class="cf-name">{{ form.services.label }}
                    <text>*</text>
                </view>
                <view class="cf-box" @click="this.$refs.cbPop.open('bottom')">
                    <text v-if="form.services.value">{{ form.services_text.value }}</text>
                    <text v-else>请选择</text>
                    <image src="../../images/icon/arrow-down-s-line.png"></image>
                </view>
            </view>

            <view class="upload-file">
                <view class="uf-tit">{{ form.photo.label }}
                    <text>*</text>
                </view>
                <view class="uf-tips">支持PNG、JPG等格式</view>
                <view class="uf-tips">男士需蓝底且穿西装、打领带；女士需蓝底且穿西装。</view>
                <view class="adidci-doc-block" v-if="form.photo.value">
                    <view class="adidcidb-l" @click="preview(form.photo_info.value)">
                        <image src="@/images/icon/file-excel-fill.png" v-if="getFormatType(form.photo_info.value.filename) == 'excel'"></image>
                        <image src="@/images/icon/file-word-fill.png" v-else-if="getFormatType(form.photo_info.value.filename) == 'word'"></image>
                        <image src="@/images/icon/file-pdf-2-fill.png" v-else-if="getFormatType(form.photo_info.value.filename) == 'pdf'"></image>
                        <image src="@/images/icon/file-ppt-fill.png" v-else-if="getFormatType(form.photo_info.value.filename) == 'ppt'"></image>
                        <image src="@/images/icon/file-image-fill.png" v-else-if="getFormatType(form.photo_info.value.filename) == 'image'"></image>

                        <view class="adidcidbl-txt">
                            <view class="adidcidblt-tit">{{ form.photo_info.value.filename}}</view>
                            <view class="adidcidblt-size">{{ getFormatType(form.photo_info.value.filename) }}</view>
                        </view>
                    </view>
                    <view class="adidcidb-r" @click="delFile(0, 'photo')">
                        <image src="../../images/icon/close-line.png"></image>
                    </view>
                </view>
                <view class="uf-upbtn" v-if="photoUploadCount < config.fileMax['photo']" @click="openFile('photo')">
                    <image src="../../images/icon/upload-2-line.png"></image>
                    <text>上传文件</text>
                </view>
            </view>

            <view class="upload-file">
                <view class="uf-tit">{{ form.certs.label }}
                    <text>*</text>
                </view>
                <view class="uf-tips">支持PDF、DOC、PPT、PNG、JPG等格式</view>
                <view class="uf-tips">中级注册安全工程师证书或高级工程师证书两者必须有一个</view>
                <view class="adidci-doc-block" v-for="(item, index) in form.certs.value" :key="index">
                    <view class="adidcidb-l" @click="preview(item)">
                        <image src="@/images/icon/file-excel-fill.png" v-if="getFormatType(item.filename) == 'excel'"></image>
                        <image src="@/images/icon/file-word-fill.png" v-else-if="getFormatType(item.filename) == 'word'"></image>
                        <image src="@/images/icon/file-pdf-2-fill.png" v-else-if="getFormatType(item.filename) == 'pdf'"></image>
                        <image src="@/images/icon/file-ppt-fill.png" v-else-if="getFormatType(item.filename) == 'ppt'"></image>
                        <image src="@/images/icon/file-zip-fill.png" v-else-if="getFormatType(item.filename) == 'zip'"></image>
                        <image src="@/images/icon/file-video-fill-2.png" v-else-if="getFormatType(item.filename) == 'video'"></image>
                        <image src="@/images/icon/file-image-fill.png" v-else-if="getFormatType(item.filename) == 'image'"></image>
                        <view class="adidcidbl-txt">
                            <view class="adidcidblt-tit">{{ item.filename }}</view>
                            <view class="adidcidblt-size">{{ getFormatType(item.filename) }}</view>
                        </view>
                    </view>
                    <view class="adidcidb-r" @click="delFile(index, 'certs')">
                        <image src="../../images/icon/close-line.png"></image>
                    </view>
                </view>
                <view class="uf-upbtn" v-if="certsUploadCount < config.fileMax['certs']" @click="openFile('certs')">
                    <image src="../../images/icon/upload-2-line.png"></image>
                    <text>上传文件</text>
                </view>
            </view>

            <view class="mtextarea">
                <view class="mt-tit has-description">安全从业经历<text>*</text></view>
                <view class="mt-description">例：2015-2024，陕西化工科技有限公司，安全主管</view>
                <view class="mt-cnt">
                    <textarea maxlength=2000 v-model="form.safety_work_experience.value" placeholder="请输入内容"/>
                </view>
            </view>

            <view class="mtextarea">
                <view class="mt-tit">{{ form.course_scopes.label }}
                    <text>*</text>
                </view>
                <view class="mt-cnt">
                    <textarea maxlength=2000 v-model="form.course_scopes.value" placeholder="请输入内容"/>
                </view>
            </view>

            <view class="mtextarea" style="margin-bottom: 0;">
                <view class="mt-tit">{{ form.typical_cases.label }}
                    <text>*</text>
                </view>
                <view class="mt-cnt">
                    <textarea maxlength=2000 v-model="form.typical_cases.value" placeholder="请输入内容"/>
                </view>
            </view>
            <view class="upload-file">
                <view class="uf-tit">{{ form.scene_photos.label }}
                    <text>*</text>
                </view>
                <view class="uf-tips">支持PDF、DOC、PPT、PNG、JPG等格式</view>
                <view class="uf-tips">至少上传 6 张不同培训或检查照片（越多越好）</view>
                <view class="adidci-doc-block" v-for="(item, index) in form.scene_photos.value" :key="index">
                    <view class="adidcidb-l" @click="preview(item)">
                        <image src="@/images/icon/file-excel-fill.png" v-if="getFormatType(item.filename) == 'excel'"></image>
                        <image src="@/images/icon/file-word-fill.png" v-else-if="getFormatType(item.filename) == 'word'"></image>
                        <image src="@/images/icon/file-pdf-2-fill.png" v-else-if="getFormatType(item.filename) == 'pdf'"></image>
                        <image src="@/images/icon/file-ppt-fill.png" v-else-if="getFormatType(item.filename) == 'ppt'"></image>
                        <image src="@/images/icon/file-zip-fill.png" v-else-if="getFormatType(item.filename) == 'zip'"></image>
                        <image src="@/images/icon/file-video-fill-2.png" v-else-if="getFormatType(item.filename) == 'video'"></image>
                        <image src="@/images/icon/file-image-fill.png" v-else-if="getFormatType(item.filename) == 'image'"></image>
                        <view class="adidcidbl-txt">
                            <view class="adidcidblt-tit">{{ item.filename }}</view>
                            <view class="adidcidblt-size">{{ getFormatType(item.filename) }}</view>
                        </view>
                    </view>
                    <view class="adidcidb-r" @click="delFile(index, 'scene_photos')">
                        <image src="../../images/icon/close-line.png"></image>
                    </view>
                </view>
                <view class="uf-upbtn" v-if="scenePhotosUploadCount < config.fileMax['scene_photos']" @click="openFile('scene_photos')">
                    <image src="../../images/icon/upload-2-line.png"></image>
                    <text>上传文件</text>
                </view>
            </view>

            <view class="mtextarea">
                <view class="mt-tit">{{ form.serve_customers.label }}</view>
                <view class="mt-cnt">
                    <textarea maxlength=2000 v-model="form.serve_customers.value" placeholder="请输入内容"/>
                </view>
            </view>

            <view class="mtextarea">
                <view class="mt-tit">{{ form.teaching_styles.label }}</view>
                <view class="mt-cnt">
                    <textarea maxlength=2000 v-model="form.teaching_styles.value" placeholder="请输入内容"/>
                </view>
            </view>

            <view class="mtextarea">
                <view class="mt-tit">{{ form.remark.label }}</view>
                <view class="mt-cnt">
                    <textarea maxlength=2000 v-model="form.remark.value" placeholder="请输入内容"/>
                </view>
            </view>
        </view>

        <!-- 底部导航 -->
        <view class="sic-foot-eb"></view>
        <view class="sic-foot">
            <view class="sicf-btn" @click="saveData">
                <text>提交申请</text>
            </view>
        </view>

        <!-- 多选弹窗 -->
        <uni-popup ref="cbPop" type="bottom" :safe-area="false">
            <view class="checkbox-popup-box">
                <view class="cpb-tit">安全服务方向</view>
                <view class="cpb-list">
                    <checkbox-group @change="checkboxChange">
                        <view class="cpbl-item" v-for="(item, index) in config.services" :key="index" :value="item">
                            <view class="cpbli-txt">{{ item }}</view>
                            <checkbox :value="item" :checked="form.services.value?.includes(item)" color="#390abc"/>
                        </view>
                    </checkbox-group>
                </view>
                <view class="cpb-foot">
                    <view class="cpbf-btn cpbf-cancle" @click="this.$refs.cbPop.close()">取消</view>
                    <view class="cpbf-btn cpbf-submit" @click="checkboxChecked">确定选择</view>
                </view>
            </view>
        </uni-popup>

        <!-- 多选弹窗 -->
        <uni-popup ref="fieldsPop" type="bottom" :safe-area="false">
            <view class="checkbox-popup-box">
                <view class="cpb-tit">擅长领域</view>
                <view class="cpb-list">
                    <checkbox-group @change="checkboxFieldsChange">
                        <view class="cpbl-item" v-for="(item, index) in config.fields" :key="index" :value="item">
                            <view class="cpbli-txt">{{ item }}</view>
                            <checkbox :value="item" :checked="form.fields.value?.includes(item)" color="#390abc"/>
                        </view>
                    </checkbox-group>
                </view>
                <view class="cpb-foot">
                    <view class="cpbf-btn cpbf-cancle" @click="this.$refs.fieldsPop.close()">取消</view>
                    <view class="cpbf-btn cpbf-submit" @click="checkboxFieldsChecked">确定选择</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import {alert, showDelayLoading, getFormatType} from "@/lib/utils";
import api from "@/lib/api";

export default {
    onLoad() {
        console.log(this.form)
        this.getConfig()
        this.getForm()
    },
    data() {
        return {
            config: {
                educations: [],
                services: [],
                fileMax: [],
                fields: []
            },
            genders: [
                {value: 1, label: '男'},
                {value: 2, label: '女'},
            ],
            checkServices: [],
            checkFields: [],
            form: {
                name: {
                    label: '姓名',
                    value: undefined
                },
                phone: {
                    label: '联系电话',
                    value: undefined
                },
                gender: {
                    label: '性别',
                    value: undefined
                },
                residence: {
                    label: '常住地',
                    value: ''
                },
                major: {
                    label: '毕业所学专业',
                    value: ''
                },
                education: {
                    label: '最高学历',
                    value: undefined
                },
                occupation: {
                    label: '职称/职务',
                    value: undefined
                },
                industry: {
                    label: '从事专业',
                    value: undefined
                },
                work_year: {
                    label: '从事专业年限',
                    value: undefined
                },
                fields: {
                    label: '擅长领域',
                    value: undefined
                },
                fields_text: {
                    label: '擅长领域文本',
                    value: undefined
                },
                services: {
                    label: '安全服务方向',
                    value: undefined
                },
                services_text: {
                    label: '安全服务方向文本',
                    value: undefined
                },
                photo: {
                    label: '个人照片',
                    value: undefined
                },
                certs: {
                    label: '资格证书',
                    value: []
                },
                course_scopes: {
                    label: '授课范围',
                    value: undefined
                },
                typical_cases: {
                    label: '安全咨询与培训典型案例',
                    value: undefined
                },
                scene_photos: {
                    label: '现场照片',
                    value: []
                },
                serve_customers: {
                    label: '服务客户',
                    value: ''
                },
                safety_work_experience: {
                    label: '安全从业经历',
                    value: ''
                },
                teaching_styles: {
                    label: '讲师教学风格',
                    value: ''
                },
                remark: {
                    label: '备注',
                    value: ''
                },
                certs_add: {
                    label: '上传证书',
                    value: []
                },
                certs_remove: {
                    label: '删除证书',
                    value: []
                },
                scene_photos_add: {
                    label: '上传现场照片',
                    value: []
                },
                scene_photos_remove: {
                    label: '删除现场照片',
                    value: []
                },
                photo_info: {
                    label: '照片信息',
                    value: {
                        path_src: '',
                        filename: ''
                    }
                }
            },
            photoUploadCount: 0,
            certsUploadCount: 0,
            scenePhotosUploadCount: 0,
            uploadForm: {},
            detail: undefined
        }
    },
    methods: {
        getConfig() {
            const hideLoading = showDelayLoading('加载中', 200)
            api.get(`expert/form-config`).then(res => {
                this.config = res
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        getForm() {
            const hideLoading = showDelayLoading('加载中', 200)
            api.get(`expert/my-show`).then(res => {
                this.detail = res
                for (let key in this.form) {
                    if (res.hasOwnProperty(key)) {
                        if (Array.isArray(res[key])) {
                            this.form[key].value = res[key]
                            if (key == 'services') {
                                this.form['services_text'].value = res[key].join()
                            }
                            if (key == 'fields') {
                                this.form['fields_text'].value = res[key].join()
                            }
                        } else {
                            if (key == 'photo') {
                                this.form['photo_info'].value.filename = res[key].split('/').pop()
                                this.form['photo_info'].value.path_src = res['photo_url']
                            }
                            this.form[key].value = res[key]
                        }
                    }
                }
            }).catch(err => {
                if (err.code == 404) {
                    this.text = '申请成为认证讲师'
                } else {
                    alert(err.message)
                }
            }).finally(() => {
                hideLoading()
            })
        },
        getFormatType(type) {
            return getFormatType(type);
        },
        bindPickerChange(e) {
            console.log(e.detail.value)
            this.form.education.value = this.config.educations[e.detail.value]
        },
        radioChange(e) {
            for (let i = 0; i < this.genders.length; i++) {
                if (this.genders[i].value == e.detail.value) {
                    this.form.gender.value = e.detail.value
                    break;
                }
            }
        },
        checkboxChange(e) {
            let items = this.config.services,
                values = e.detail.value,
                checkServices = [];
            for (let i = 0, lenI = items.length; i < lenI; ++i) {
                const item = items[i]
                if (values.includes(item)) {
                    checkServices.push(item)
                } else {
                    checkServices = checkServices.filter(service => service !== item)
                }
            }
            this.checkServices = checkServices
        },
        checkboxChecked() {
            this.form.services.value = this.checkServices
            this.form.services_text.value = this.checkServices.join()
            this.$refs.cbPop.close()
        },
        checkboxFieldsChange(e) {
            let items = this.config.fields,
                values = e.detail.value,
                checkFields = [];
            for (let i = 0, lenI = items.length; i < lenI; ++i) {
                const item = items[i]
                if (values.includes(item)) {
                    checkFields.push(item)
                } else {
                    checkFields = checkFields.filter(field => field !== item)
                }
            }
            this.checkFields = checkFields
        },
        checkboxFieldsChecked() {
            this.form.fields.value = this.checkFields
            this.form.fields_text.value = this.checkFields.join()
            this.$refs.fieldsPop.close()
        },
        async openFile(name) {
            let itemList = ["从微信聊天选择", "从手机相册选择", "拍摄"]

            uni.showActionSheet({
                itemList: itemList,
                success: res => {
                    if (res.tapIndex == 0) {
                        wx.chooseMessageFile({
                            count: this.config.fileMax[name], //默认100
                            type: 'all',
                            success: async res => {
                                await this.uploadAllFiles(res.tempFiles, name);
                            }
                        });
                    } else if (res.tapIndex == 1) {
                        let mediaType = ['image', 'video'];

                        uni.chooseMedia({
                            count: this.config.fileMax[name],
                            sourceType: ['album'],
                            mediaType: mediaType,
                            success: async res => {
                                console.log(res)
                                await this.uploadAllFiles(res.tempFiles, name);
                            }
                        })
                    } else if (res.tapIndex == 2) {
                        let mediaType = ['image', 'video'];

                        uni.chooseMedia({
                            count: this.config.fileMax[name],
                            sourceType: ['camera'],
                            mediaType: mediaType,
                            success: async res => {
                                console.log(res)
                                await this.uploadAllFiles(res.tempFiles, name);
                            }
                        })
                    }
                }
            })
        },
        async uploadAllFiles(res, name) {
            for (const item of res) {
                await this.initData().then(() => {
                    if (item?.path) {
                        this.uploadFile(item.path, name)
                    } else if (item?.tempFilePath) {
                        this.uploadFile(item.tempFilePath, name)
                    }
                }) // 等待每个文件上传完成
            }
        },
        initData() {
            return new Promise((resolve, reject) => {
                api.get('expert/upload-config').then(res => {
                    this.uploadForm = res
                    resolve()
                }).catch(err => {
                    alert(err.message)
                    reject()
                })
            })
        },
        async uploadFile(path, name) {
            uni.showLoading({
                title: '上传中'
            });
            try {
                const uploadFileRes = await uni.uploadFile({
                    url: this.uploadForm.url,
                    filePath: path,
                    formData: this.uploadForm.form_params,
                    name: this.uploadForm.name
                });

                // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换
                let data = JSON.parse(uploadFileRes.data);
                if (data?.key) {
                    console.log(data.key)
                    if (name === 'photo') {
                        this.form.photo.value = data.key
                        this.form.photo_info.value.filename = data.filename
                        this.form.photo_info.value.path_src = data.url
                        this.photoUploadCount++
                    } else if (name === 'certs') {
                        this.form.certs.value.push({path_src: data.url, filename: data.filename})
                        this.form.certs_add.value.push(data.key)
                        this.certsUploadCount++
                    } else {
                        this.form.scene_photos.value.push({path_src: data.url, filename: data.filename})
                        this.form.scene_photos_add.value.push(data.key)
                        this.scenePhotosUploadCount++
                    }
                } else {
                    alert('上传失败');
                }
            } catch (error) {
                console.log(error);
                uni.showToast({
                    title: error.message || '上传失败',
                    duration: 2000
                });
            } finally {
                uni.hideLoading();
            }
        },
        delFile(index, type) {
            if (type === 'photo') {
                this.form.photo.value = ''
                this.form.photo_info.value.filename = ''
                this.form.photo_info.value.path_src = ''
                this.photoUploadCount--
            } else if (type === 'certs') {
                if (this.detail) {
                    this.form.certs_remove.value.push(this.form.certs.value[index].path)
                }
                this.form.certs.value.splice(index, 1)
                this.certsUploadCount--
            } else {
                if (this.detail) {
                    this.form.scene_photos_remove.value.push(this.form.scene_photos.value[index].path)
                }
                this.form.scene_photos.value.splice(index, 1)
                this.scenePhotosUploadCount--
            }
        },
        saveData() {
            console.log(this.form)
            let requiredKeys = []
            for (let key in this.form) {
                if (this.form.hasOwnProperty(key)) {
                    if ((key !== 'serve_customers' && key !== 'teaching_styles' && key !== 'remark') && (!this.form[key].value || this.form[key].value.length === 0)) {
                        if (this.detail) {
                            console.log(key)
                            if (key !== 'certs_add' && key !== 'certs_remove' && key !== 'scene_photos_add' && key !== 'scene_photos_remove') {
                                requiredKeys.push(key)
                            }
                        } else {
                            if (key !== 'certs_remove' && key !== 'scene_photos_remove') {
                                requiredKeys.push(key)
                            }
                        }
                    }
                }
            }

            if (requiredKeys.length) {
                let content = `${this.form[requiredKeys[0]].label}不能为空`
                uni.showModal({
                    content: content,
                    showCancel: false
                })
                return
            }

            let form = {}
            for (let key in this.form) {
                if (this.form.hasOwnProperty(key)) {
                    form[key] = this.form[key].value
                }
            }

            let hideLoading = showDelayLoading("提交中", 200)
            if (this.detail) {
                api.put(`expert/`+this.detail.id, form).then(res => {
                    uni.showToast({
                        title: "提交成功",
                        icon: "success",
                        duration: 1500,
                        success: () => {
                            this.goBack()
                        }
                    });
                }).catch(err => {
                    alert(err.message)
                }).finally(() => {
                    hideLoading()
                })
            } else {
                api.post(`expert`, form).then(res => {
                    uni.showToast({
                        title: "提交成功",
                        icon: "success",
                        duration: 1500,
                        success: () => {
                            this.goBack()
                        }
                    });
                }).catch(err => {
                    alert(err.message)
                }).finally(() => {
                    hideLoading()
                })
            }
        },
        goBack() {
            uni.$emit('submit-form');
            setTimeout(() => {
                uni.navigateBack()
            }, 1500)
        },
        preview(info) {
            if (!info.path_src) {
                return
            }

            if (this.getFormatType(info.filename) == "image") {
                uni.previewImage({
                    urls: [info.path_src],
                });
                return;
            }
            let filePath = "";
            const task = uni.downloadFile({
                url: info.path_src,
                filePath,
                success(res) {
                    uni.hideLoading()
                    uni.openDocument({
                        filePath: res.tempFilePath || res.filePath,
                        showMenu: true,
                        success: () => {},
                        fail: err => {
                            uni.showModal({
                                title: '打开文件失败',
                                content: err.errMsg,
                                showCancel: false
                            });
                        }
                    });
                },
                fail(err) {
                    uni.hideLoading();
                    uni.showModal({
                        title: '下载文件失败',
                        content: err.errMsg,
                        showCancel: false
                    });
                }
            });

            task.onProgressUpdate(res => {
                uni.showLoading({
                    title: '加载中 ' + res.progress + '%',
                    mask: true
                });
            });
        }
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.content {
    padding: 30rpx 0;
}

.ssgb-tit {
    height: 80rpx;
    line-height: 80rpx;
    font-size: 28rpx;
    color: #999;
    padding: 0 30rpx;
}

.ss-group-box {
    margin-top: 30rpx;
}

/* 单选、多选 */
.dropdown-part {
    flex: 1;
    display: flex;
    align-items: center;
    height: 100rpx;
    justify-content: flex-end;
}

.dropdown-part text {
    padding-right: 20rpx;
    color: #999;
    font-size: 28rpx;
}

.dropdown-part image {
    width: 32rpx;
    height: 32rpx;
}

.single-cb {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    padding: 0 0 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.st-checkbox {
    flex: 1;
    padding-top: 20rpx;
}

.st-checkbox checkbox {
    padding-right: 40rpx;
    padding-bottom: 20rpx;
}

/* 单行文本 */
.single-txt {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    background-color: #fff;
    padding: 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.st-input {
    flex: 1;
}

.st-input input {
    height: 100rpx;
    line-height: 100rpx;
    text-align: right;
}

.st-unit {
    font-size: 24rpx;
    color: #999;
}

.st-name {
    width: 200rpx;
}

.st-name text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

.ssgb-cnt .single-txt {
    margin-bottom: 1px;
}

.ssgb-cnt .upload-file {
    margin-bottom: 1px;
}

/* 开放单选 */
.radio-open {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    background-color: #fff;
    padding: 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.ro-part {
    flex: 1;
}

.ro-part radio-group {
    display: flex;
    align-items: center;
}

.ro-part label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.ro-part label text {
    padding-left: 8rpx;
}

.ro-name {
    width: 200rpx;
}

.ro-name text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

/* 折叠多选 */
.checkbox-fold {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    background-color: #fff;
    padding: 0 30rpx;
    font-size: 28rpx;
    margin-bottom: 1rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.cf-box {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.cf-box image {
    width: 32rpx;
    height: 32rpx;
}

.cf-box text {
    padding-right: 10rpx;
    max-width: 400rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #999;
}

.cf-name {
    width: 200rpx;
}

.cf-name text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

/* 折叠多选弹窗 */
.checkbox-popup-box {
    background-color: #F3F3FF;
    border-radius: 12rpx 12rpx 0 0;
    padding-bottom: 80rpx;
}

.cpb-tit {
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-weight: bold;
    border-bottom: 1px solid #F3F3FF;
}

.cpbl-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 20rpx;
    border-bottom: 1px solid #F3F3FF;
    background-color: #fff;
}

.cpbli-txt {
    font-size: 28rpx;
}

.cpb-foot {
    padding: 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cpbf-btn {
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
    font-size: 28rpx;
}

.cpbf-submit {
    flex: 1;
    background-color: #390ABC;
    color: #fff;
    border-radius: 12rpx;
    font-weight: bold;
}

.cpbf-cancle {
    width: 200rpx;
    margin-right: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
}

/* 多行文本 */
.mtextarea {
    position: relative;
    margin-bottom: 30rpx;
}

.mt-tit {
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 30rpx;
    color: #999;
    font-size: 28rpx;
}

.mt-tit.has-description {
    height: auto;
    line-height: 1;
    padding: 40rpx 30rpx 10rpx;
}

.mt-description {
    font-size: 24rpx;
    color: #999;
    padding: 10rpx 30rpx 20rpx;
}

.mt-tit text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

.mt-cnt {
    display: flex;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.mt-cnt textarea {
    flex: 1;
    padding: 30rpx;
    font-size: 28rpx;
}

/* 上传框 */
.upload-file {
    position: relative;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    padding-bottom: 1px;
    margin-bottom: 1rpx;
}

.uf-tit {
    font-size: 28rpx;
    padding: 30rpx 30rpx 0;
}

.uf-tit text {
    color: red;
    font-weight: bold;
    padding-left: 8rpx;
}

.uf-tips {
    font-size: 24rpx;
    color: #999;
    padding: 10rpx 30rpx 20rpx;
}

.uf-upbtn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    color: #390ABC;
}

.uf-upbtn image {
    width: 32rpx;
    height: 32rpx;
}

.uf-upbtn text {
    font-size: 28rpx;
    padding-left: 10rpx;
    font-weight: bold;
}

/* 上传后文件样式 */
.adidci-doc-block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e7e7e7;
    border-radius: 12rpx;
    padding: 20rpx;
}

.adidcidbl-txt {
    padding-left: 20rpx;
}

.adidcidb-l {
    display: flex;
    align-items: center;
}

.adidcidb-l image {
    width: 64rpx;
    height: 64rpx;
}

.adidcidblt-tit {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300rpx;
    font-size: 28rpx;
}

.adidcidblt-size {
    font-size: 24rpx;
    color: #999;
    padding-top: 6rpx;
}

.adidcidb-r image {
    width: 32rpx;
    height: 32rpx;
}

.upload-file .adidci-doc-block {
    margin: 0 30rpx 30rpx;
    background-color: #F3F3FF;
    border: none;
}

.uf-review-tip {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 24rpx;
    background-color: red;
    color: #fff;
    padding: 4rpx 8rpx;
}

.uf-review-tip.pass {
    background-color: #07c160;
}

/* 单选展示出来 */


/* 底部按钮 */
.sic-foot {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 15rpx 15rpx env(safe-area-inset-bottom);
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    z-index: 10;
}

.sicf-btn {
    height: 90rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #390abc;
    color: #fff;
    border-radius: 12rpx;
}

.sicf-btn image {
    width: 36rpx;
    height: 36rpx;
}

.sicf-btn text {
    font-weight: bold;
}

.sic-foot-eb {
    height: 100rpx;
    padding-bottom: env(safe-area-inset-bottom);
}

.cpb-list {
    max-height: 750rpx;
    overflow-y: scroll;
}


</style>