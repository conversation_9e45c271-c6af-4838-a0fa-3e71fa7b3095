<template>
    <view class="sub-sview-box" v-if="config?.fields?.length">
        <scroll-view class="sub-sview-list navScroll" scroll-x="true">
            <view class="sub-sview-item" :class="{'cur': currentIndex == '全部'}" @click="filterFields('全部')">全部</view>
            <view class="sub-sview-item" :class="{'cur': currentIndex == index}" v-for="(item, index) in config.fields" :key="index" @click="filterFields(index)">{{ item }}</view>
        </scroll-view>

        <view class="esb-warp">
            <view class="exam-search-box">
                <image src="../../images/icon/search-line.png"></image>
                <input class="uni-input" type="text" v-model="keywords" :focus="focus" confirm-type="search" @confirm="search()" placeholder="请输入关键词"/>
                <view class="search-clear-btn" v-if="keywords != ''" @click="clearText">
                    <view class="scb-icon">
                        <image src="../../images/icon/close-line-white.png"></image>
                    </view>
                </view>
                <button class="esb-btn" :disabled="keywords == ''" @click="search()">搜索</button>
            </view>
        </view>
    </view>
    <view class="ssb-eb"></view>

    <view class="content">
        <view class="expert-list">
            <navigator v-if="list.length > 0" class="el-item" v-for="item in list" :key="list.id" :url="'/pages/expert/detail?id=' + item.id">
                <view class="eli-l">
                    <image :src="item.photo_url" mode="widthFix"></image>
                </view>
                <view class="eli-r">
                    <view class="elir-name">{{ item.name }}<text>{{ item.education }}</text></view>
                    <view class="elir-zc">{{ item.occupation }}</view>
                    <view class="elir-field">擅长领域：{{ item.fields.join() }}</view>
                    <view class="elir-foot">
                        <view class="elirf-more">
                            <text>查看详细信息</text>
                            <image src="../../static/images/icon/arrow-right-s-line.png"></image>
                        </view>
                    </view>
                </view>
            </navigator>
            <uni-load-more :status="listStatus"></uni-load-more>
        </view>

        <!-- 底部导航 -->
        <view class="sic-foot-eb"></view>
        <view @click="apply" class="sic-foot">
            <view class="sicf-btn">
                <text>{{ text }}</text>
            </view>
        </view>
    </view>
</template>

<script>
    import api from "@/lib/api";
    import { useUserStore } from "@/store/user";
    import {alert, showDelayLoading} from "@/lib/utils";
    import {mapState} from "pinia";

    export default {
        onLoad() {
            this.getDetail()
            this.getList()
            this.getConfig()
            uni.$on('submit-form', () => {
                this.getDetail()
            })
        },
        onUnload() {
            uni.$off('submit-form')
        },
        // onPullDownRefresh() {
        //     console.log('下拉刷新')
        //     this.listEnd = false
        //     this.nextCursor = ""
        //     this.getDetail()
        //     this.getList(true)
        // },
        onReachBottom() {
            console.log('上拉加载')
            if (this.nextCursor) {
                this.getList(false, this.currentIndex)
            }
        },
        onShareAppMessage(res) {
            if (res.from === 'button') {// 来自页面内分享按钮
                console.log(res.target)
            }
            let path = "/pages/expert/list"

            if (this.user) {
                path += "?ref=" + this.user.uuid;
            }

            return {
                title: this.detail.title,
                path
            };
        },
        data() {
            return {
                list: [],
                nextCursor: null,
                listRows: 10,
                listLoaded: false,
                listLoading: false,
                listEnd: false,
                keywords: "",
                detail: {},
                text: '申请成为认证讲师',
                config: {},
                currentIndex: '全部'
            }
        },
        watch: {
            user(newValue, oldValue) {
                if (newValue) {
                    this.getDetail()
                }
            }
        },
        computed: {
            ...mapState(useUserStore, ['user']),
            listStatus() {
                if (this.listLoading) {
                    return "loading";
                } else if (this.listEnd) {
                    return "noMore";
                } else {
                    return "more";
                }
            }
        },
        methods: {
            filterFields(index) {
                this.listEnd = false
                this.nextCursor = ""
                this.currentIndex = index
                this.getList(true, index)
            },
            getConfig() {
                const hideLoading = showDelayLoading('加载中', 200)
                api.get(`expert/form-config`).then(res => {
                    this.config = res
                }).catch(err => {
                    alert(err.message)
                }).finally(() => {
                    hideLoading()
                })
            },
            apply() {
                if (!this.user) {
                    uni.showModal({
                        content: "您尚未登录",
                        confirmText: "去登录",
                        success: res => {
                            if (res.confirm) {
                                uni.navigateTo({
                                    url: "/pages/login/index"
                                })
                            }
                        }
                    })
                    return
                }
                if (this.detail) {
                    if (this.detail.status == 0 || this.detail.status == 1) {
                        uni.navigateTo({
                            url: '/pages/expert/detail?id=' + this.detail.id
                        })
                    } else {
                        uni.navigateTo({
                            url: '/pages/expert/apply'
                        })
                    }
                } else {
                    uni.navigateTo({
                        url: '/pages/expert/apply'
                    })
                }
            },
            getDetail() {
                if (!this.user) {
                    return
                }
                const hideLoading = showDelayLoading('加载中', 200)
                api.get(`expert/my-show`).then(res => {
                    this.detail = res
                    if (this.detail.status == 0) {
                        this.text = '认证资料审核中'
                    } else if (this.detail.status == 1) {
                        this.text = '我的主页'
                    } else {
                        this.text = '申请成为认证讲师'
                    }
                }).catch(err => {
                    if (err.code == 404) {
                        this.text = '申请成为认证讲师'
                    } else {
                        alert(err.message)
                    }
                }).finally(() => {
                    hideLoading()
                })
            },
            search() {
                if (this.keywords) {
                    this.list = ''
                    this.nextCursor = ''
                    this.listEnd = false
                    this.getList(true, this.currentIndex)
                }
            },
            clearText() {
                this.keywords = ''
                this.list = ''
                this.nextCursor = ''
                this.listEnd = false
                this.getList(true, this.currentIndex)
            },
            getList(refresh, index) {
                if (this.listLoading || this.listEnd) {
                    return;
                }
                let query = {list_rows: this.listRows}
                if (this.nextCursor) {
                    query.next_cursor = this.nextCursor
                }
                if (index >= 0) {
                    query.fields = this.config.fields[index]
                }
                query.search = this.keywords
                this.listLoading = true
                const hideLoading = refresh ? showDelayLoading('加载中', 200): null
                api.get(`expert`, query).then(res => {
                    if (refresh) {
                        this.list = res.data
                    } else {
                        this.list.push(...res.data)
                    }
                    this.listLoaded = true
                    console.log(this.listEnd)
                    if (!res.next_cursor) {
                        this.listEnd = true
                        console.log(this.listEnd)
                    } else {
                        this.nextCursor = res.next_cursor
                    }
                }).catch(err => {
                    alert(err.message)
                }).finally(() => {
                    this.listLoading = false
                    if (hideLoading) {
                        hideLoading()
                    }
                    uni.stopPullDownRefresh();
                })
            }
        },
    }
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .content {
        padding: 30rpx;
    }

    .el-item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        margin-bottom: 30rpx;
    }
    .eli-l {
        padding-right: 20rpx;
    }
    .eli-l image {
        width: 200rpx;
        border-radius: 12rpx;
        vertical-align: top;
    }
    .eli-r {
        flex: 1;
    }
    .elir-name {
        font-size: 36rpx;
        font-weight: bold;
        padding-bottom: 4rpx;
    }
    .elir-name text {
        font-size: 28rpx;
        padding-left: 10rpx;
        color: #999;
        font-weight: normal;
    }
    .elir-zc {
        font-size: 28rpx;
        line-height: 1.6;
    }
    .elir-field {
        padding: 10rpx 0;
        font-size: 28rpx;
        line-height: 1.6;
    }
    .elir-foot {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-top: 10rpx;
    }
    .elirf-more {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #f3f3f3;
        padding: 14rpx 14rpx 14rpx 16rpx;
        border-radius: 12rpx;
    }
    .elirf-more text {
        font-size: 28rpx;
        padding-right: 6rpx;
    }
    .elirf-more image {
        width: 32rpx;
        height: 32rpx;
    }

    /* 底部导航 */
    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 20rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        z-index: 10;
    }
    .sicf-btn {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 12rpx;
        margin-bottom: env(safe-area-inset-bottom);
    }
    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }
    .sicf-btn text {
        font-weight: bold;
    }
    .sicf-btn.disabled {
        background-color: #999;
    }
    .sic-foot-eb {
        height: 148rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }


    /* 分类 */
    .sub-sview-box {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background-color: #f3f3ff;
        z-index: 10;
    }
    .sub-sview-list {
        white-space: nowrap;
        width: 100%;
        padding: 20rpx 0;
    }
    .sub-sview-item {
        display: inline-block;
        height: 70rpx;
        line-height: 70rpx;
        padding: 0 30rpx;
        text-align: center;
        font-size: 36rpx;
        font-size: 24rpx;
        border: 1px solid transparent;
        background-color: #fff;
        border-radius: 70rpx;
        margin-left: 30rpx;
        color: #666;
    }
    .sub-sview-item.cur {
        background-color: #390ABC;
        color: #fff;
        border: 1px solid #390ABC;
    }

    .ssb-eb {
        height: 206rpx;
    }


    /* 搜索框 */
    .esb-warp {
        padding: 0 30rpx;
        background-color: #f3f3ff;
    }
    .exam-search-box {
        display: flex;
        align-items: center;
        background-color: #FFF;
        border-radius: 12rpx;
        padding: 0 0 0 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .exam-search-box image {
        width: 32rpx;
        height: 32rpx;
    }
    .exam-search-box .uni-input {
        flex: 1;
        height: 100rpx;
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .esb-btn {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #390ABC;
    }
    .search-clear-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        padding: 0 20rpx;
    }
    .scb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        border-radius: 100%;
        background-color: rgba(0, 0, 0, .3);
        margin-right: 10rpx;
    }
    .search-clear-btn image {
        width: 28rpx;
        height: 28rpx;
    }

</style>