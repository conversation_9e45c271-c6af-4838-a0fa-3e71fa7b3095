<template>
    <view class="content">
        <view class="hgb-tab-box">
            <view class="tab-item" :class="{cur:category.sid == this.cateId}" @click="chooseCategory(category.sid)" v-for="category in categories" :key="category.sid">{{ category.name }}</view>
        </view>


        <view class="tab-pane">
            <view class="news-list" v-if="list.length > 0">
                <!-- 文章无缩略图 -->
                <view class="news-item" v-for="item in list" :key="item.sid" @click="goDetail(item)">
                    <image class="nli-fm" v-if="item.cover" :src=item.cover_src  mode="widthFix"></image>
                    <image class="nli-fm" v-if="!item.cover && item.type_label === 'rich_text'" src="../../images/noimg/article-noimg-2.png"  mode="widthFix"></image>
                    <image class="nli-fm" v-if="!item.cover && item.type_label === 'video'" src="../../images/noimg/article-noimg.png"  mode="widthFix"></image>
                    <view class="nli-bottom">
                        <view class="nlir-tit"><image v-if="item.type_label === 'video'" src="../../images/icon/vidicon-fill.png"></image>{{ item.title }}</view>
                        <view class="nlir-foot">
                            <text v-if="item.source">来源：{{ item.source }}</text>
                            时间：<uni-dateformat :date="item.release_at" format="yyyy-MM-dd"></uni-dateformat>
                            <text>浏览：{{ item.views }}</text>
                        </view>
                    </view>
                </view>
                <uni-load-more :status="listStatus"></uni-load-more>
            </view>

            <view class="no-data-nomal-box" v-if="listLoaded && !listLoading && list.length === 0">
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无数据</text>
            </view>

        </view>
    </view>
</template>

<script>
import {alert, getDetailUrl, showDelayLoading} from "@/lib/utils";
import api from "@/lib/api";

export default {
    onLoad(e) {
        if (e.cateId) {
            this.cateId = e.cateId
        }
        const ps = [
            // 分类
            api.get("cms/categories", {classify: 'news'}).then(res => {
                this.categories = res
                this.categories.unshift({
                    "name": "全部",
                    "sid": "news",
                    "logo_src": ""
                });
            }).catch(err => {
                alert(err.message)
            })
        ]
        this.loading = true
        uni.showLoading({
            title: '加载中',
            mask: true
        })

        Promise.all(ps)
            .then(() => {
                this.loading = false
                uni.hideLoading()
            }, e => {
                this.loading = false
                uni.hideLoading()
                alert(e.message)
            })

        this.loadList()
    },
    onPullDownRefresh() {
        this.list = []
        this.listEnd = false
        this.listNextCursor = ""
        this.loadList(true, () => uni.stopPullDownRefresh())
    },
    onReachBottom() {
        this.loadList()
    },
    computed: {
        listStatus() {
            if (this.listLoading) {
                return "loading"
            } else if (this.listEnd) {
                return "noMore"
            } else {
                return "more"
            }
        }
    },
    data() {
        return {
            cateId: 'news',
            categories: [],
            list: [],
            listNextCursor: "",
            listRows: 10,
            listLoaded: false,
            listLoading: false,
            listEnd: false
        }
    },
    methods: {
        loadList(refresh, callback, checkLoading) {
            if (this.listLoading || this.listEnd) {
                return
            }

            let query = {list_rows: this.listRows}
            if (this.listNextCursor !== '') {
                query.next_cursor = this.listNextCursor
            }

            this.listLoading = true
            const hideLoading = checkLoading ? showDelayLoading('加载中', 200) : null
            api.get("cms/" + this.cateId + "/contents", query).then(res => {
                this.listNextCursor = res.next_cursor
                if (refresh) {
                    this.list = res.data
                } else {
                    this.list.push(...res.data)
                }
                this.listLoaded = true
                if (res.next_cursor === '') {
                    this.listEnd = true
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                this.listLoading = false
                if (callback) {
                    callback()
                }
                if (hideLoading) {
                    hideLoading()
                }
            })
        },
        chooseCategory(cateId) {
            this.cateId = cateId
            this.listNextCursor = ""
            this.listEnd = false
            this.list = []
            this.loadList(true, null, true)
        },
        goDetail(data) {
            let url = getDetailUrl(data.type_label, data.sid)
            if (url === '') {
                alert("未获取到详情地址")
                return
            }
            uni.navigateTo({
                url: url
            })
        }
    }
}
</script>

<style>
    page {
        background-color:#F3F3FF ;
    }

    /* tab样式 */
   .hgb-tab-box {
       position: -webkit-sticky;
       position: sticky;
       top: 0;
       display: flex;
       align-items: center;
       justify-content: center;
       flex-wrap: wrap;
       background-color: #f3f3ff;
       z-index: 10;
       box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
   }
    .tab-item {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 20rpx;
        color: #999;
        margin-left: 10rpx;
    }
    .tab-item.cur {
        position: relative;
        font-weight: 700;
        color: #090abc;
    }
    .tab-item.cur::after {
        content: '';
        position: absolute;
        height: 8rpx;
        width: 20rpx;
        background-color: #090abc;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
    }


    /* 新闻列表 */
    .tab-pane {
        padding: 30rpx 30rpx 0;
    }
    .news-item {
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .nli-fm {
        width: 100%;
        border-radius: 12rpx 12rpx 0 0;
        vertical-align: top;
    }
    .nli-bottom {
        padding: 20rpx 30rpx 30rpx;
    }
    .nlir-tit {
        font-weight: bold;
        line-height: 1.6;
    }
    .nlir-tit image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
        margin-right: 20rpx;
        margin-top: 10rpx;
        float: left;
    }
    .nlir-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top: 16rpx;
    }
    .nlir-foot text {
        padding-right: 20rpx;
    }
    /* .nlir-foot text {
        position: relative;
        padding-left: 20rpx;
        margin-left: 20rpx;
    }
    .nlir-foot text::after {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 20rpx;
        background-color: #e7e7e7;
    } */

</style>