<template>
    <view class="content">
        <swiper class="swiper" duration="300" :vertical="true" @change="swiperChange">
            <swiper-item v-for="(detail, index) in list" :key="index">
                <view class="fixed-top">
                    <view class="ft-head">
                        <video v-if="(detail.own_content || detail.view_limit === limitType.free)
                        && detail.resource?.video_src
                        && (index === currentIndex || index === currentIndex + 1)"
                            :id="'myVideo' + detail.sid"
                            :src="detail.resource?.video_src"
                            :poster="detail.resource?.video_cover"
                            :autoplay="detail.autoplay"
                        />

                        <!-- 视频需付费情况 -->
                        <view class="need-buy-tips" v-else>
                            <view class="nbt-txt">此视频为付费内容，购买后即可观看</view>
                             <button class="nbt-btn" v-if="detail.view_limit === limitType.credit" @click="buy">
                                <view class="nbtb-top">
                                    <image src="../../images/icon/copper-coin-fill-new.png"></image>
                                    <text>{{ detail.charge_credit }}积分</text>
                                </view>
                                <view class="nbtb-txt">立即购买</view>
                            </button>
                            <button class="nbt-btn" v-if="detail.view_limit === limitType.amount">
                                <view class="nbtb-top">
                                    <text>{{ detail.charge_amount }}元</text>
                                </view>
                                <view class="nbtb-txt" @click="buy">立即支付</view>
                            </button>
                            <button class="nbt-btn" v-if="detail.view_limit === limitType.credit_amount">
                                <view class="nbtb-top">
                                    <image src="../../images/icon/copper-coin-fill-new.png"></image>
                                    <text>{{ detail.charge_credit }}积分/{{ detail.charge_amount }}元</text>
                                </view>
                                <view class="nbtb-txt" @click="buy">立即购买</view>
                            </button>
                        </view>
                    </view>
                    <view class="ft-tit">
                        <view class="ftt-h3">
                            <text>{{ detail.title }}</text>
                        </view>
                        <view class="ftt-p">
                            <view v-if="detail.source">来源：{{ detail.source }}</view>
                            <view>时间：<uni-dateformat :date="detail.release_at" format="yyyy-MM-dd"/></view>
                            <view>浏览：{{ detail.views }}</view>
                        </view>
                    </view>
                    <view class="video-main-right">
                        <view class="vmr-item" @click="attitude(detail)">
                            <image src="../../images/video/thumb-up-fill-big-color.png" v-if="detail.is_attitude"></image>
                            <image src="../../images/video/thumb-up-fill-big.png" v-else></image>
                            <text>有用</text>
                        </view>
                        <view class="vmr-item" @click="favorite(detail)">
                            <image src="../../images/video/star-fill-big-color.png" v-if="detail.is_favorite"></image>
                            <image src="../../images/video/star-fill-big.png" v-else></image>
                            <text>收藏</text>
                        </view>
                        <button class="vmr-item" open-type="share">
                            <image src="../../images/video/share-forward-fill-big.png"></image>
                            <text>分享</text>
                        </button>
                        <view class="vmr-item" @click="download">
                            <image src="../../images/video/video-download-fill-big.png"></image>
                            <text>下载</text>
                        </view>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
    <pay-alert ref="payAlert" :order="order" :credit="list[currentIndex]?.charge_credit" :allow-types="allowTypes" @creditPay="onCreditPay" @createdOrder="onCreatedOrder" @paymentCallback="onPaymentCallback" title="购买资料"></pay-alert>

</template>

<script>
import {alert, showDelayLoading} from "@/lib/utils";
import api from "@/lib/api";
import {getReferral} from "@/lib/context";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {ContentViewLimit as cvl} from "@/lib/enums";
import {loginRequired} from "@/lib/login";
import {getAllowTypes} from "@/lib/pay";
import PayAlert from "@/components/pay-alert.vue";

export default {
    components: {PayAlert},
    onLoad(e) {
        if (!e.sid) {
            uni.showModal({
                content: "参数错误",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        uni.navigateBack()
                    }
                }
            })
        }
        this.sid = e.sid
        if (e.url) {
            this.url = decodeURIComponent(e.url)
            console.log(this.url)
            this.getList()
        } else {
            uni.$on('video-url', (data) => {
                this.url = data.url
                console.log(this.url)
                this.getList()
            })
        }

        uni.$on('logged-in', () => {
            this.nextCursor = ''
            this.list = []
            this.getList()
        })
    },
    onUnload() {
        uni.$off('video-url')
        uni.$off('logged-in')
    },
    computed: {
        ...mapState(useUserStore, ['user', 'reload'])
    },
    onShareAppMessage(res) {
        if (res.from === 'button') {// 来自页面内分享按钮
            console.log(res.target)
        }
        let path = "/pages/article/video?sid=" + this.list[this.currentIndex].sid

        if (this.user) {
            path += "&ref=" + this.user.uuid;
        }
        path += "&url=" + encodeURIComponent(this.url)

        return {
            title: this.list[this.currentIndex].title,
            path,
            imageUrl: this.list[this.currentIndex].resource.video_cover ?? this.list[this.currentIndex].cover_src
        };
    },
    data() {
        return {
            sid: "",
            url: "",
            currentIndex: 0,
            lastCurrentIndex: 0,
            list: [],
            limitType: cvl,
            nextCursor: "",
            total: 0,
            buyType: 'credit',
            order: {},
            allowTypes: []
        }
    },
    methods: {
        getList(type) {
            const hideLoading = showDelayLoading("加载中", 200)
            if (this.nextCursor !== '') {
                this.url += "&next_cursor=" + this.nextCursor
            }
            api.get(this.url).then(res => {
                if (!res.data) {
                    let list = []
                    res.forEach(item => {
                        if (item.type_label === 'video') {
                            list.push(item)
                        }
                    })
                    this.list = this.moveArrayToTop(list, 'sid', this.sid)
                } else {
                    this.list.push(...res.data)
                    this.nextCursor = res.next_cursor
                    if (type === 'swiper') {
                        this.total += res.data.length
                    }
                }
                this.list.forEach((item, index) => {
                    item.autoplay = index === this.currentIndex
                })

                this.order.total_amount = this.list[this.currentIndex].charge_amount
                this.allowTypes = getAllowTypes(this.list[this.currentIndex].view_limit)
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        getDetail(sid) {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get("cms/contents/" + sid).then(res => {
                res.is_attitude = res.attitude
                res.is_favorite = res.favorite
                res.own_content = res.download
                this.list[this.currentIndex] = res
                let uuid = getReferral(true)
                if (uuid !== '') {
                    this.shareReward(uuid)
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                hideLoading()
            })
        },
        shareReward(uuid) {
            let data = {uuid: uuid, business_type: this.list[this.currentIndex].business_type, business_id: this.list[this.currentIndex].sid}
            api.post("invitations/share-ref", data).then(res => {

            }).catch(err => {
                console.log(err.message)
            })
        },
        login() {
            let url = "/pages/login/index"
            uni.navigateTo({
                url
            })
        },
        attitude(detail) {
            loginRequired().then(() => {
                const hideLoading = showDelayLoading("加载中", 200)
                if (!detail.is_attitude) {
                    api.post("attitude/" + detail.business_type + "/" + detail.sid, {attitude: 1}).then(res => {
                        detail.is_attitude = true
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                } else {
                    api.delete("attitude/" + detail.business_type + "/" + detail.sid + "?attitude=1").then(res => {
                        detail.is_attitude = false
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                }
            })
        },
        favorite(detail) {
            loginRequired().then(() => {
                const hideLoading = showDelayLoading("加载中", 200)
                if (!detail.is_favorite) {
                    api.post("favorites/" + detail.business_type + "/" + detail.sid).then(res => {
                        detail.is_favorite = true
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                } else {
                    api.delete("favorites/" + detail.business_type + "/" + detail.sid).then(res => {
                        detail.is_favorite = false
                    }).catch(err => {
                        alert(err.message)
                    }).finally(() => {
                        hideLoading()
                    })
                }
            })
        },
        buy() {
            loginRequired().then(() => {
                if (this.allowTypes.length === 0) {
                    uni.showToast({
                        title: "当前内容无需购买",
                        icon: "none",
                        mask: true
                    });
                    return
                }
                if (this.list[this.currentIndex].view_limit === this.limitType.credit) {
                    this.$refs.payAlert.buyFromCredit()
                } else if (this.list[this.currentIndex].view_limit === this.limitType.amount && !this.user.balance_show) {
                    this.$refs.payAlert.buyFromAmount()
                } else {
                    this.$refs.payAlert.open()
                }
            })
        },
        onCreatedOrder(buyType) {
            const hideLoading = showDelayLoading("")
            api.post("cms/contents/" + this.list[this.currentIndex].sid + "/buy-order").then(res => {
                for (let key in res) {
                    this.$set(this.order, key, res[key])
                }
                if (buyType == 'amount') {
                    this.$refs.payAlert.payFromAmount()
                } else {
                    this.$refs.payAlert.payFromBalance()
                }
            }).catch(e => {
                uni.showModal({
                    content: e.message,
                    showCancel: false
                });
            }).finally(() => {
                hideLoading()
            })
        },
        onCreditPay() {
            // 扣除对应积分
            const hideLoading = showDelayLoading("购买中", 200)
            api.post("cms/contents/" + this.list[this.currentIndex].sid + "/credit").then(res => {
                uni.showToast({
                    title: "购买成功",
                    icon: "success"
                });
                this.$refs.payAlert.close()
                this.getDetail(this.list[this.currentIndex].sid)
                this.reload()
            }).catch(err => {
                if (err.code === 403) {
                    this.$refs.payAlert.creditNotEnough(err.message)
                } else {
                    alert(err.message)
                }
            }).finally(() => {
                hideLoading()
            })
        },
        onPaymentCallback() {
            this.getDetail(this.list[this.currentIndex].sid)
        },
        download() {
            loginRequired().then(() => {
                if (!this.list[this.currentIndex].own_content) {
                    this.buy()
                    return
                }

                uni.showLoading({
                    title: '下载中...',
                    mask: true
                });

                api.get("cms/contents/" + this.list[this.currentIndex].sid + "/download").then(res => {
                    let filePath = "";

                    // #ifdef MP-WEIXIN
                    //使用 filePath 可以保证分享出去的文件名，但是弊端是保存的文件会存在微信的用户空间，不会被系统当成临时文件自动清理
                    if (this.list[this.currentIndex].resource.filename) {
                        filePath = wx.env.USER_DATA_PATH + '/' + this.list[this.currentIndex].resource.filename;
                    }
                    // #endif

                    const task = uni.downloadFile({
                        url: res.url,
                        filePath,
                        success: (res) => {
                            if (res.statusCode === 200) {
                                uni.hideLoading()

                                // 保存视频到手机相册
                                uni.saveVideoToPhotosAlbum({
                                    filePath: res.tempFilePath,
                                    success: () => {
                                        // 成功提示
                                        uni.showToast({
                                            title: "视频已保存至手机相册",
                                            icon: "success"
                                        })
                                    }
                                })
                            }
                        },
                        fail(err) {
                            uni.hideLoading();
                            uni.showModal({
                                title: '下载文件失败',
                                content: err.errMsg,
                                showCancel: false
                            });
                        }
                    });

                    task.onProgressUpdate(res => {
                        uni.showLoading({
                            title: '加载中 ' + res.progress + '%',
                            mask: true
                        });
                    });

                }).catch(err => {
                    uni.hideLoading();
                    alert(err.message)
                });
            })
        },
        swiperChange(e) {
            this.currentIndex = e.detail.current
            console.log(this.currentIndex, this.lastCurrentIndex)

            this.$set(this.order, 'total_amount', this.list[this.currentIndex].charge_amount)
            const allowTypes = getAllowTypes(this.list[this.currentIndex].view_limit)
            this.$set(this, 'allowTypes', allowTypes)

            this.list.forEach((item, index) => {
                if (index === this.lastCurrentIndex) {
                    this.videoContext = uni.createVideoContext('myVideo' + item.sid)
                    this.videoContext.pause()
                }
                if (index === this.currentIndex) {
                    this.videoContext = uni.createVideoContext('myVideo' + item.sid)
                    this.videoContext.play()
                }
            })
            // 向下滑动且滑动到新数据第10条且游标不为空
            if (this.currentIndex > this.lastCurrentIndex && this.currentIndex === this.total + 9 && this.nextCursor !== '') {
                this.getList('swiper')
            }
            this.lastCurrentIndex = this.currentIndex// 更新上一次的index为当前的index
            api.get("cms/contents/" + this.list[this.currentIndex].sid).then(res => {}).catch(err => {})
        },
        /**
         * 指定键值数组置顶
         */
        moveArrayToTop(arr, key, value) {
            const index = arr.findIndex(item => item[key] === value);
            if (index !== -1) {
                const item = arr.splice(index, 1)[0];
                arr.unshift(item);
            }
            return arr;
        }
    }
}
</script>

<style>
    page {
        width: 100%;
        height: 100%;
        background-color: #000;
    }
    .swiper {
        height: 100%;
    }
    .content {
        width: 100%;
        height: 100%;
    }
    .img-part {
        width: 100%;
        height: 100%;
    }
    .img-part image {
        width: 100%;
        height: 100%;
    }
    .fixed-top {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        background-color: #fff;
        z-index: 4;
    }
    .ft-head {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #000;
    }
    .ft-head video {
        width: 100%;
        height: 100%;
    }
    .ft-tit {
        box-sizing: border-box;
        width: 100%;
        padding: 30rpx;
        background-color: #000;
        color: #fff;
    }
    .ftt-h3 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 1.6;
        padding-right: 110rpx;
    }
    .ftt-h3 text {
        flex: 1;
    }
    .ftth3-share {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        width: 88rpx;
        margin-left: 20rpx;
    }
    .ftth3-share image {
        width: 36rpx;
        height: 36rpx;
    }
    .ftt-p {
        display: flex;
        align-items: center;
        padding-top: 20rpx;
        font-size: 24rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .ftt-p view {
        padding-right: 20rpx;
        color: #999;
    }



    /* 底部浮层 */
    .doc-detail-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        height: 140rpx;
        width: 100%;
        border-top: 1px solid #F3F3FF;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ddf-l {
        flex: 1;
        box-sizing: border-box;
        display: flex;
        padding: 0 10rpx;
    }
    .ddf-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
        height: 140rpx;
    }
    .ddf-item image {
        width: 36rpx;
        height: 36rpx;
    }
    .ddf-item text {
        padding-top: 10rpx;
        font-size: 28rpx;
    }
    .ddfi-tips {
        position: absolute;
        left: 50%;
        top: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 44rpx;
        height: 44rpx;
        border-radius: 100%;
        background-color: #f54a45;
        color: #fff;
        font-size: 24rpx;
        line-height: 1;
        transform: translateX(-50%);
        margin-left: 40rpx;
    }
    .ddf-r {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 300rpx;
        background-color: #f54a45;
        color: #fff;
        height: 140rpx;
    }
    .ddfr-top {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ddfr-top image {
        width: 36rpx;
        height: 36rpx;
    }
    .ddfr-top text {
        padding-left: 10rpx;
        font-size: 32rpx;
        font-weight: bold;
    }
    .ddfr-foot {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 10rpx;
    }
    .ddfr-foot image {
        width: 32rpx;
        height: 32rpx;
    }
    .ddfr-foot text {
        font-size: 24rpx;
        padding-left: 6rpx;
    }
    .ddf-view-btn {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ddf-view-btn image {
        width: 36rpx;
        height: 36rpx;
        vertical-align: top;
        margin-top: 4rpx;
    }
    .ddf-view-btn text {
        padding-left: 20rpx;
    }
    .ddf-empty-block {
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }


    /* 右侧按钮 */
    .video-main-right {
        position: fixed;
        right: 30rpx;
        bottom: 300rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .vmr-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        padding: 30rpx 0;
        margin-bottom: 30rpx;
    }
    .vmr-item image {
        width: 64rpx;
        height: 64rpx;
    }
    .vmr-item text {
        color: #fff;
        font-size: 28rpx;
        padding-top: 14rpx;
        text-shadow: 0 0 30rpx rgba(0, 0, 0, .3);
    }


    .iphone-pd {
        padding-bottom: env(safe-area-inset-bottom);
    }


    /* 购买视频样式 */
    .need-buy-tips {
        background-color: #000;
        color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .nbt-txt {
        font-size: 32rpx;
        padding-bottom: 40rpx;
    }
    .nbtb-top {
        display: flex;
        align-items: center;
        justify-content: center;
        padding-right: 20rpx;
        margin-right: 20rpx;
        border-right: 1px solid rgba(0, 0, 0, .1);
    }
    .nbt-btn {
        background-image: linear-gradient(55deg, #ecbd7d, #fadeae 100%, #fadaa0 0);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 100rpx;
        padding: 36rpx 50rpx;
        line-height: 1;
    }
    .nbtb-top image {
        width: 32rpx;
        height: 32rpx;
    }
    .nbtb-top text {
        padding-left: 10rpx;
        font-size: 24rpx;
        color: #7f5011;
    }
    .nbtb-txt {
        font-size: 32rpx;
        color: #7f5011;
    }
</style>