<template>
    <view class="content">
        <view class="new-detail-tit">{{ detail.title }}</view>
        <view class="nd-info">
            <view class="ndi-p" v-if="detail.source">来源：{{ detail.source }}</view>
            <view class="ndi-p">时间：<uni-dateformat :date="detail.release_at" format="yyyy-MM-dd"></uni-dateformat></view>
            <view class="ndi-p">浏览：{{ detail.views }}</view>
        </view>

        <mp-html :content="detail.resource.content" />

        <relation-contents :sid="sid" type="rich_text"></relation-contents>

        <view class="nfs-eb"></view>
        <button class="nd-foot-share" open-type="share">
            <image src="../../images/icon/share-forward-2-line.png"></image>
            <text>分享</text>
        </button>
    </view>
</template>

<script>
import {showDelayLoading, alert} from "@/lib/utils";
import api from "@/lib/api";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {getReferral} from "@/lib/context"
import RelationContents from "@/components/relation-contents.vue";

export default {
    components: {
        RelationContents,
        mpHtml
    },
    computed: {
        ...mapState(useUserStore, ['user'])
    },
    onLoad(e) {
        if (!e.sid) {
            uni.showModal({
                content: "参数错误",
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        uni.navigateBack()
                    }
                }
            })
        }
        this.sid = e.sid
        this.getDetail()
    },
    data() {
        return {
            sid: "",
            detail: {
                resource: {}
            }
        }
    },
    onShareAppMessage(res) {
        if (res.from === 'button') {// 来自页面内分享按钮
            console.log(res.target)
        }
        let path = "/pages/article/detail?sid=" + this.detail.sid;

        if (this.user) {
            path += "&ref=" + this.user.uuid;
        }

        return {
            title: this.detail.title,
            path
        };
    },
    methods: {
        getDetail() {
            const hideLoading = showDelayLoading("加载中", 200)
            api.get("cms/contents/" + this.sid).then(res => {
                this.detail = res
                let uuid = getReferral(true)
                if (uuid !== '') {
                    this.shareReward(uuid)
                }
            }).catch(err => {
                alert(err)
            }).finally(() => {
                hideLoading()
            })
        },
        shareReward(uuid) {
            let data = {uuid: uuid, business_type: this.detail.business_type, business_id: this.detail.sid}
            api.post("invitations/share-ref", data).then(res => {

            }).catch(err => {
                console.log(err.message)
            })
        },
    }
}
</script>

<style>
    page {
        background-color: #FFFFFF;
    }
    .content {
        padding: 30rpx;
    }
    .new-detail-tit {
        font-size: 44rpx;
        margin-bottom: 30rpx;
    }

    .nd-info {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
        color:  #999;
        font-size: 24rpx;
    }
    .ndi-p {
        padding-right: 30rpx;
    }

    .nd-rich-box {
        line-height: 1.8;
        font-size: 32rpx;
        word-break: break-all!important;
        word-wrap: break-word!important;
        margin-bottom: 30rpx;
    }
    .nd-rich-box image {
        max-width: 100%!important;
        vertical-align: top;
    }
    .nd-foot-share {
        box-sizing: content-box;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 120rpx;
        padding-bottom: env(safe-area-inset-bottom);
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        border-top: 1px solid #F3F3FF;
    }
    .nd-foot-share image {
        width: 32rpx;
        height: 32rpx;
    }
    .nd-foot-share text {
        font-size: 24rpx;
        padding-top: 10rpx;
    }
    .nfs-eb {
        height: 120rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .doc-type {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        width: 64rpx;
        height: 64rpx;
    }
</style>