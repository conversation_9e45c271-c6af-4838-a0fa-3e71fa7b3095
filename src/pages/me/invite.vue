<template>
    <view class="content">
        <view class="top-fixed-box">
            <view class="tab-nomal">
                <view :class="{'tab-item':true, 'cur':current=='recommend'}" @click="current='recommend'">推荐好友</view>
                <view :class="{'tab-item':true, 'cur':current=='records'}" @click="current='records'">推荐记录</view>
            </view>
        </view>

        <view class="invite-main-box" v-show="current=='recommend'">
            <view class="top-part-img">
                <image src="https://img.shiwusuo100.com/assets/app-static/yaoqin-banner.png" mode="widthFix"></image>
            </view>
            <view class="invite-rules">
                <view class="inr-tit">攻略：</view>
                <view class="inr-p">
                    1、分享小程序卡片给好友；
                </view>
                <view class="inr-p">
                    2、好友通过您分享的卡片，成功注册即可获得奖励。
                </view>
            </view>
            <button class="invite-share-btn" open-type="share" @click="share">分享给好友</button>
        </view>

        <view class="invite-record-list" v-show="current=='records'">
            <uni-table border stripe emptyText="">
            	<!-- 表头行 -->
            	<uni-tr>
            		<uni-th align="left" width="200rpx">日期</uni-th>
            		<uni-th align="left" width="200rpx">手机</uni-th>
            		<uni-th align="left" width="200rpx">状态</uni-th>
            	</uni-tr>
            	<!-- 表格数据行 -->
            	<uni-tr v-for="item in records" :key="item.id">
            		<uni-td><uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm" :threshold="[60000, 86400000]"></uni-dateformat></uni-td>
            		<uni-td>{{ maskPhone(item.invitee.phone) }}</uni-td>
            		<uni-td v-if="item.status == 2">已完成 <br> +{{ item.credit }}积分</uni-td>
            		<uni-td v-else>已邀请</uni-td>
            	</uni-tr>
            </uni-table>
            <uni-load-more :status="listStatus"></uni-load-more>
        </view>

    </view>
</template>

<script>
import api from '@/lib/api.js';
import { useUserStore } from '@/store/user.js';
import { mapState } from 'pinia';
import {getAppName} from "@/lib/context";

export default {
    data() {
        return {
            current: "recommend",
            records: [],
            listLoaded: false,
            listLoading: false,
            listEnd: false,
            listNextCursor: "",
            appName: getAppName()
        };
    },
    computed: {
        ...mapState(useUserStore, ["user"]),
        listStatus() {
            if (this.listLoading) {
                return "loading";
            } else if (this.listEnd) {
                return "noMore";
            } else {
                return "more";
            }
        }
    },
    watch: {
        current(v) {
            if (v == "records" && !this.listLoaded) {
                this.loadRecords();
            }
        }
    },
    onPullDownRefresh() {
        this.listEnd = false;
        this.listNextCursor = "";
        this.loadRecords(true, () => uni.stopPullDownRefresh());
    },
    onReachBottom() {
        this.loadRecords();
    },
    methods: {
        loadRecords(refresh, callback) {
            if (this.listLoading || this.listEnd) {
                return;
            }

            this.listLoading = true;

            api.get("invitations", {next_cursor: this.listNextCursor})
                .then(data => {
                    this.listNextCursor = data.next_cursor;
                    if (refresh) {
                        this.records = data.data;
                    } else {
                        this.records.push(...data.data);
                    }
                    this.listLoaded = true;
                    if (!data.next_cursor) {
                        this.listEnd = true;
                    }
                }, e => {
                    uni.showModal({
                        title: "加载失败",
                        content: e.message,
                        showCancel: false
                    });
                })
                .finally(() => {
                    this.listLoading = false;
                    if (callback) {
                        callback();
                    }
                });
        },
        maskPhone(phone) {
            return phone.slice(0, 3) + "****" + phone.slice(-4);
        }
    },
    onShareAppMessage() {
		return {
			title: this.user.nickname + "邀请您加入" + this.appName,
			path: "/pages/index/index?ref=" + encodeURIComponent(this.user.uuid),
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
		};
	},
};
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .top-fixed-box {
        position: -webkit-sticky;
        position: sticky;
        top: 0;
        background-color: #f3f3ff;
        height: 100rpx;
        z-index: 10;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .tfb-eb {
        height: 80rpx;
    }
    /* tab样式 */
    .tab-nomal {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 30rpx;
    }
    .tab-item {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 30rpx;
        color: #999;
    }
    .tab-item.cur {
        position: relative;
        font-weight: 700;
        color: #090abc;
    }
    .tab-item.cur::after {
        content: '';
        position: absolute;
        height: 8rpx;
        width: 20rpx;
        background-color: #090abc;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0;
    }

    .top-part-img {
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        padding: 6rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .top-part-img image {
        width: 100%;
        vertical-align: top;
        border-radius: 12rpx;
    }

    .invite-rules {
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .inr-tit {
        font-size: 28rpx;
        font-weight: bold;
        padding-bottom: 10rpx;
    }
    .inr-p {
        font-size: 28rpx;
        line-height: 1.6;
        padding-bottom: 8rpx;
        color: #666;
    }
    .inr-p:last-child {
        padding-bottom: 0;
    }

    .invite-share-btn {
        height: 100rpx;
        border-radius: 12rpx;
        background-color: rgb(43, 162, 69);
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        text-align: center;
        line-height: 100rpx;
        text-align: center;
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
    }

    .invite-main-box,.invite-record-list {
        padding: 30rpx;
    }
</style>