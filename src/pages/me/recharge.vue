<template>
    <view class="content" v-if="loaded">
        <view class="user-box">
            <view class="ub-title">充值账号</view>
            <view class="ub-text">
                {{ user.nickname }} <text> / 当前积分：{{ user.credit }}</text>
            </view>
        </view>

        <view class="user-box">
            <view class="ub-title">充值金额</view>
            <view class="ub-select-box">
                <view :class="{'usb-item': true, 'nomal-cur': cpIndex == index}" v-for="(item, index) in creditPackages" :key="index" @click="changePackage(index)">
                    <view class="usb-h3">{{ item.price }}<view class="usb-span">元</view></view>
                    <text>{{ item.credit }}积分</text>
                    <view class="usb-cur-icon">
                        <image src="../../images/icon/check-line.png" mode="widthFix"></image>
                    </view>
                </view>
            </view>
            <view class="custom-recharge-box">
                <view :class="{ 'crb-cur': cpIndex == -1 }" class="crb-main">
                    <view class="crb-l">￥</view>
                    <input  class="uni-input" placeholder="自定义金额" type="number" inputmode="numeric" :value="customAmount" @click="changePackage(-1)" @input="inputAmount">
                    <view class="usb-cur-icon">
                        <image src="../../images/icon/check-line.png" mode="widthFix"></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="user-box">
            <view class="ub-title">支付方式</view>
            <view class="ub-select-box" style="padding-right: 0;">
                <view class="small-usb-item" :class="{'nomal-cur': payType === 'wechat'}" @click="payType = 'wechat'">
                    <image class="pay-icon" src="../../images/icon/wechat-pay-fill.png" mode="widthFix"></image>
                    <text>微信支付</text>
                    <view class="usb-cur-icon">
                        <image src="../../images/icon/check-line.png" mode="widthFix"></image>
                    </view>
                </view>
                <view class="small-usb-item" :class="{'nomal-cur': payType === 'balance'}" @click="payType = 'balance'" v-if="this.user?.balance_show">
                    <image class="pay-icon" src="@/images/icon/wallet-2-fill.png" mode="widthFix"></image>
                    <text>余额支付</text>
                    <view class="usb-cur-icon">
                        <image src="../../images/icon/check-line.png" mode="widthFix"></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="gap-foot"></view>

        <view class="buy-foot-box">
            <view class="bfb-cost">
                <view v-if="amount > 0" class="bfbc-main">
                    <view class="bfb-h3"><text class="bfb-s-d">￥</text>{{ amount }}</view>
                    <text class="hasbg">共{{ credits }}积分</text>
                </view>
                <view v-else class="bfbc-main">
                    <view class="bfb-h3"><text class="bfb-s-d">￥</text>0</view>
                    <text class="hasbg">共0积分</text>
                </view>
            </view>
            <button  class="bfb-button" @click="payment" :disabled="amount < 1 || paying">{{paying ? "支付中.." : "立即支付"}}</button>
        </view>

        <!-- 微信确认支付弹窗 -->
        <uni-popup :mask-click="false" ref="popup" type="center">
            <view class="wx-pay-confirm">
                <view class="wpc-txt">请确认微信支付是否已完成</view>
                <view class="wpc-yes" @click="checkOrder()">已完成支付</view>
                <view class="wpc-no" @click="this.$refs.popup.close()">支付遇到问题，重新支付</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import api from "@/lib/api";
import { useUserStore } from "@/store/user";
import { mapActions, mapState } from "pinia";
import { makePayment } from "@/lib/pay";
import {alert, showDelayLoading} from "@/lib/utils";

export default {
    data() {
        return {
            cpIndex: 0, //creditPackageIndex
            paymentAmount: 0,
            customAmount: 0,
            orderCredit: 0,
            creditPackages: [],
            creditMultiple: 0,
            paying: false,
            payType: 'wechat'
        };
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
        amount() {
            return this.cpIndex >= 0 ? this.creditPackages[this.cpIndex]?.price : this.customAmount;
        },
        credits() {
            return this.cpIndex >= 0 ? this.creditPackages[this.cpIndex]?.credit : this.customAmount * this.creditMultiple;
        }
    },
    onLoad() {
        uni.showLoading({
            title: "页面加载中"
        });

        api.get("pages/recharge")
            .then(data => {
                this.creditPackages = data.credit_packages;
                this.creditMultiple = data.credit_multiple;
                uni.hideLoading();
            }, e => {
                uni.hideLoading();
                uni.showModal({
                    title: "页面加载失败",
                    content: e.message,
                    showCancel: false
                });
            });
    },
    methods: {
        ...mapActions(useUserStore, ["reload"]),
        payment() {
            if (this.amount < 1) {
                uni.showModal({
                    title: "温馨提示",
                    content: "请输入充值金额",
                    showCancel: false
                });
                return;
            }

            if (this.payType === 'wechat') {
                this.paymentWechat()
            } else {
                this.paymentBalance()
            }
        },
        paymentWechat() {
            uni.showLoading({
                title: "发起支付中",
                mask: true
            });

            api.post("credits/order", {amount: this.amount})
                .then(data => makePayment(data.order_no, "wechat", "mp"))
                .then(() => {
                    this.paymentSuccessCallback()
                })
                .catch(e => {
                    this.paymentFailCallback(e.message)
                })
                .finally(() => {
                    uni.hideLoading()
                })
        },
        paymentBalance() {
            uni.showModal({
                content: "您将花费" + this.amount + "余额购买积分，确认要购买吗？",
                confirmText: "确认购买",
                success: (res) => {
                    if (res.confirm) {
                        uni.showLoading({
                            title: "发起支付中",
                            mask: true
                        });

                        api.post("credits/order", {amount: this.amount})
                            .then(data => api.post('balances/payment', {order_no: data.order_no}))
                            .then(() => {
                                this.paymentSuccessCallback()
                            })
                            .catch(e => {
                                this.paymentFailCallback(e.message)
                            })
                            .finally(() => {
                                uni.hideLoading();
                            })
                    }
                }
            });
        },
        paymentSuccessCallback() {
            uni.showToast({
                title: "充值成功",
                icon: "success",
                success: () => {
                    this.reload();
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500)
                }
            });
        },
        paymentFailCallback(errMessage) {
            uni.showModal({
                title: "支付失败",
                content: errMessage,
                showCancel: false
            });
        },
        inputAmount(e) {
            this.customAmount = parseInt(e.detail.value);
            this.orderCredit = this.customAmount * this.creditMultiple;
        },
        changePackage(index) {
            this.cpIndex = index;
            if (index >= 0) {
                this.paymentAmount = this.creditPackages[index].price;
                this.orderCredit = this.creditPackages[index].credit;
            } else {
                this.paymentAmount = this.customAmount;
                this.orderCredit = this.customAmount * this.creditMultiple;
            }
        }
    }
};
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 0 30rpx 30rpx;
    }
    .user-box {
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ub-title {
        display: flex;
        justify-content: space-between;
        font-size: 28rpx;
        padding: 30rpx;
        color: #999;
    }
    .ub-title span {
        color: orange;
    }

    .ub-tip {
        background-color: #fef7ed;
        border: 1px solid #fae0b5;
        border-radius: 0 0 12rpx 12rpx;
        padding: 30rpx;
        font-size: 28rpx;
        color: #f0a020;
    }

    .ub-select-box {
        display: flex;
        flex-wrap: wrap;
        padding: 0 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .usb-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 185rpx;
        height: 186rpx;
        text-align: center;
        font-size: 28rpx;
        margin-right: 30rpx;
        margin-bottom: 30rpx;
        border: 1px solid #e7e7e7;
        border-radius: 12rpx;
    }
    .usb-item:nth-child(3n) {
        margin-right: 0;
    }
    .usb-item .usb-h3 {
        font-size: 48rpx;
    }
    .usb-item .usb-h3 .usb-span {
        display: inline-block;
        font-size: 28rpx;
    }
    .usb-item text {
        text-align: center;
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #999;
    }
    .ub-warn-tip {
        margin: 0 30rpx;
        padding: 20rpx;
        background-color: #fef7ed;
        border: 1px solid #fae0b5;
        color: #f0a020;
        font-size: 24rpx;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
    }
    .small-usb-item {
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 296rpx;
        height: 60rpx;
        line-height: 60rpx;
        padding: 30rpx 0;
        border-radius: 12rpx;
        border: 1px solid #e7e7e7;
        margin-right: 26rpx;
        margin-bottom: 26rpx;
    }
    .small-usb-item:nth-child(2n) {
        margin-right: 0;
    }
    .small-usb-item .pay-icon {
        /*  #ifdef MP-WEIXIN  */
        height: 48rpx;
        /*  #endif  */
        width: 48rpx;
        vertical-align: top;
    }
    .small-usb-item text {
        padding-left: 20rpx;
        font-size: 30rpx;
    }
    .usb-discount {
        position: absolute;
        right: -12rpx;
        top: -12rpx;
        background-color: #ccc;
        color: #fff;
        font-size: 24rpx;
        height: 50rpx;
        line-height: 50rpx;
        padding: 0 10rpx;
        border-radius: 0 12rpx;
        z-index: 2;
    }
    .usb-cur-icon {
        display: none;
        align-items: center;
        justify-content: center;
        position: absolute;
        right: -2rpx;
        bottom: -2rpx;
        height: 40rpx;
        width: 40rpx;
        background-color: #065cfd;
        color: #fff;
        border-radius: 12rpx 0;
    }
    .usb-cur-icon image {
        width: 32rpx;
        height: 32rpx;
        color: #fff;
    }

    .nomal-cur {
        border: 1px solid #065cfd;
        background-color: #edf5fe;
        color: #065cfd;
        font-weight: bold;
    }
    .nomal-cur .usb-cur-icon {
        display: flex;
    }

    .nomal-cur .usb-discount {
        background-color: red;
    }

    .buy-foot-box {
        position: fixed;
        display: flex;
        justify-content: space-between;
        align-items: center;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 20rpx 30rpx;
        background-color: #fff;
        box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.05);
        padding-bottom: env(safe-area-inset-bottom);
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .bfb-cost {
        color: #999;
        font-size: 28rpx;
    }
    .bfbc-main {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
    }
    .bfb-cost .bfb-h3 {
        font-size: 64rpx;
        color: #f0a020;
        font-weight: 700;
    }
    .bfb-cost .bfb-s-d {
        font-size: 24rpx;
    }
    .bfb-cost .hasbg {
        display: inline-block;
        background-color: #f0a020;
        border-radius: 12rpx 12rpx 12rpx 0;
        color: #fff;
        padding: 6rpx 12rpx;
        transform: scale(0.8);
    }
    .bfb-button {
        width: 200rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: bold;
        background-color: #065cfd;
        color: #fff;
        border-radius: 80rpx;
        margin-bottom: 20rpx;
    }

    .send-friend {
        background-color: #f0a020;
        width: 250rpx;
    }
    .bfb-button.disabled {
        background-color: #ccc;
    }
    .gap-foot {
        height: 150rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }

    .ub-text {
        font-size: 48rpx;
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        padding: 0 30rpx 30rpx;
    }

    .ub-text text {
        font-size: 28rpx;
        color: #f0a020;
    }

    .ub-input-box {
        margin: 0 30rpx 30rpx;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f3f3f3;
        padding-bottom: 20rpx;
    }
    .ub-input-box text {
        font-size: 32rpx;
    }
    .ub-input-box input {
        flex: 1;
        margin-left: 10rpx;
    }
    .ub-input-tips {
        display: flex;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
        padding: 0 30rpx 30rpx;
    }
    .ub-input-tips b {
        font-weight: normal;
    }

    .bfb-tips {
        font-size: 24rpx;
        color: #999;
        padding-bottom: 20rpx;
    }

    /* 自定义金额 */
    .custom-recharge-box {
        padding: 0 30rpx 30rpx;
    }
    .crb-main {
        position: relative;
        display: flex;
        align-items: center;
        height: 100rpx;
        border: 1px solid #E7E7E7;
        border-radius: 12rpx;
        box-sizing: border-box;
    }
    .crb-main.crb-cur {
        border: 1px solid #065cfd;
        background-color: #edf5fe;
    }
    .crb-main.crb-cur .usb-cur-icon {
        display: flex;
    }
    .crb-main input {
        flex: 1;
        padding-right: 20rpx;
        font-size: 28rpx;
        font-family: Arial, Helvetica, sans-serif;
    }
    .crb-l {
        padding: 0 20rpx;
        color: #999;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 24rpx;
    }
    .crb-coin-num {
        font-size: 24rpx;
        color: #999;
        padding: 0 40rpx 0 30rpx;
        border-left: 1px solid #e7e7e7;
        margin-left: 20rpx;
    }
    .crb-coin-num text {
        font-size: 28rpx;
        color: #f0a020;
        padding: 0 8rpx;
    }

    /* 微信确认支付弹窗 */
    .wx-pay-confirm {
        width: 500rpx;
        text-align: center;
        background-color: #fff;
        border-radius: 12rpx;
    }
    .wpc-txt {
        padding: 40rpx 0;
        border-bottom: 1px solid #e7e7e7;
    }
    .wpc-yes {
        font-size: 32rpx;
        color: #065cfd;
        height: 100rpx;
        line-height: 100rpx;
        border-bottom: 1px solid #e7e7e7;
    }
    .wpc-no {
        padding: 20rpx;
        font-size: 28rpx;
        color: #999;
    }
</style>
