<template>
    <view class="content">
        <view class="docs-list-box">
            <navigator :url="'/pages/document/detail?sid=' + item.resource.sid" class="dlb-item" v-for="(item, index) in list" :key="index">
                <view class="dlbi-img">
                    <doc-icon :format="item.content_doc?.format" />
                </view>
                <view class="dlbi-cnt">
                    <view class="dlbic-tit">{{item.resource.title}}</view>
                    <view class="dlbic-foot">
                        <view class="dlbicf-time">时间：<uni-dateformat :date="item.created_at" format="yyyy-MM-dd"></uni-dateformat></view>
                        <view class="dlbicf-pn" v-if="item.content_doc">页数：{{ item.content_doc?.page_count }}</view>
                    </view>
                </view>
            </navigator>

        </view>


        <!-- 暂无数据 -->
        <view class="no-data-nomal-box" v-if="!loading && list.length == 0">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无收藏</text>
        </view>
    </view>
</template>

<script>
import DocIcon from "@/components/doc-icon.vue";
import api from "@/lib/api";
import { alert } from "@/lib/utils";

export default {
    components: {
        DocIcon
    },
    data() {
        return{
            type: "",
            list: [],
            loading: true,
            cursor:'',
        }
    },
    onLoad(query) {
        this.initData()
    },
    onReachBottom() {
        if (this.cursor){
            this.initData()
        }
        console.log("上拉加载");
    },
    methods:{
        initData(){
            let data = {
                cursor: this.cursor
            }
            this.loading = true
            api.get("favorites", data).then(res => {
                uni.hideLoading();
                this.list.push(...res.data)
                this.cursor = res.next_cursor
            })
                .catch(err =>  {
                    alert(err.message)
                })
                .finally(() =>{
                    this.loading = false
                })
        }
    }
}
</script>

<style>
    page {
        background-color: #FFFFFF;
    }
    .content {
        padding: 30rpx;
    }
    .docs-list-box {
        background-color: #fff;
        border-radius: 12rpx;
    }
    .download-date {
        font-weight: bold;
        font-size: 32rpx;
    }
    .docs-list-box {
        margin-bottom: 30rpx;
    }

    /* 资料列表 */
    .dlb-item {
        display: flex;
        align-items: center;
        padding: 0 0 30rpx;
    }
    .dlbic-tit {
        height: 80rpx;
        line-height: 40rpx;
        font-size: 28rpx;
        font-weight: bold;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
    }
    .dlbic-foot {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #999;
        padding-top:20rpx;
    }
    .dlbicf-time {
        padding-right: 30rpx;
    }

    .dlbi-img {
        position: relative;
        width: 140rpx;
        height: 140rpx;
        margin-right: 20rpx;
        border-radius: 12rpx;
        border: 1px solid #e7e7e7;
    }
    .doc-type {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        width: 64rpx;
        height: 64rpx;
    }
    .dlbi-cnt {
        flex: 1;
    }
</style>