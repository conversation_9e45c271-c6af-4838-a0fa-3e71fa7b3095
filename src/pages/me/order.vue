<template>
    <view class="content">
        <view class="rr-list">
            <view class="rr-list-item" @click="goPage(item)" :class="{'r-process': item.business_type == 'credit', 'sucess': item.business_type != 'credit' }" v-for="(item, index) in list" :key="index">
                <view class="rrli-top">
                    <view class="rrli-top-num">订单编号：{{ item.order_no }}</view>
                    <view class="rrli-top-state" v-if="item.business_type == 'credit'">充值成功</view>
                    <view class="rrli-top-state" v-else>购买成功</view>
                </view>
                <view class="rrli-mid" >
                    <view class="rrlim-l">
                        <view class="coin-icon">
                            <image v-if="item.business_type == 'credit'" src="../../images/icon/copper-coin-fill-2.png"></image>
                            <image v-else-if="item.business_type == 'topic'" src="../../images/icon/file-list-2-fill.png"></image>
                            <image v-else-if="item.business_type.indexOf('cms') > -1" :src="item.resource?.cover_src"></image>
                            <image v-else src="../../images/icon/file-list-2-fill.png"></image>
                        </view>
                        <view class="rrli-mid-main">
                            <view class="rrli-h3" v-if="item.credit">积分充值<text>- 共{{ item.credit }}积分</text></view>
                            <view class="rrli-h3" v-else>{{ item.title }}</view>
                            <view class="rrli-p">实付款：￥<text>{{ item.total_amount }}</text></view>
                        </view>
                    </view>
                    <view class="rrlim-btn" @click="operation(item, index)">
                        <image src="../../images/icon/more-2-line.png"></image>
                    </view>
                </view>
                <view class="rrli-foot flex-btw">
                    <view class="rrli-foot-tip" >
                        <uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat>
                    </view>
                    <view  @click="service" class="rrli-foot-btn" hover-class="hover-class">联系客服</view>

                </view>
            </view>
        </view>

        <!--暂无数据-->
        <view class="no-data-nomal-box" v-if="!loading && list.length === 0">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无订单数据</text>
        </view>
    </view>
</template>

<script>
import DocIcon from "@/components/doc-icon.vue";
import api from "@/lib/api";
import {getImageByFormat, showDelayLoading, alert} from "@/lib/utils";

export default {
    components: {
        DocIcon
    },
    data() {
        return{
            next_cursor: '',
            list: [],
            loading: true

        }
    },
    onLoad(query) {
        this.initData()
    },
    methods: {
        initData(){
            let loading = showDelayLoading("加载中", 200)

            let data = {
                cursor: this.next_cursor
            }
            api.get("orders", data).then(res => {
                this.list.push(...res.data)
                this.next_cursor = res.next_cursor
                loading();
            }).catch(err => {
                alert(err.message)
                loading()
            })
                .finally(() => {
                    this.loading = false
                })
        },
        goPage(data){

        },
        operation(data, index){
            uni.showActionSheet({
                itemList: ['删除'],
                success: res => {
                    console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
                    if (res.tapIndex == 0){
                        uni.showModal({
                            content: "确认要删除该订单吗？",
                            confirmText: "确定删除",
                            success: (res) => {
                                if (res.confirm) {
                                    api.delete('orders/'+ data.order_no).then(res => {
                                        this.list.splice(index, 1)
                                        uni.showToast({
                                            title: '删除成功',
                                            icon: 'success',
                                            duration: 2000
                                        })
                                    }).catch(err => alert(err.message))
                                }
                            }
                        })
                    }else if (res.tapIndex == 1){
                        this.del(item)
                    }
                },
                fail: function (res) {
                    console.log(res.errMsg);
                }
            });
        },
        service(){
            uni.navigateTo({
                url:'/pages/me/service'
            })
        }
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .rr-list {
        padding: 30rpx;
    }
    .rr-list-item {
        background-color: #fff;
        margin-bottom: 30rpx;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .rrli-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 30rpx;
        border-bottom: 1px solid #f3f3f3;
        font-size: 24rpx;
        color: #999;
    }
    .rrli-mid {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1px solid #f3f3f3;
    }
    .rrli-mid image {
        width: 100rpx;
        height: 100rpx;
    }
    .rrli-mid-main {
        padding-left: 20rpx;
    }
    .rrlim-l {
        display: flex;
        align-items: center;
    }
    .rrlim-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 100rpx;
    }
    .rrlim-btn image {
        width: 32rpx;
        height: 32rpx;
    }
    .rrli-h3 {
        font-size: 28rpx;
    }
    .rrli-h3 text {
        font-size: 24rpx;
        padding-left: 10rpx;
    }
    .rrli-p {
        font-size: 24rpx;
        padding-top: 8rpx;
    }
    .rrli-p text {
        font-size: 32rpx;
        font-weight: bold;
        color: red;
    }
    .rrli-foot {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 20rpx 30rpx;
        font-size: 24rpx;
        color: #999;
    }
    .rrli-foot.flex-btw {
        justify-content: space-between;
    }
    .rrli-foot-btn {
        padding: 16rpx 30rpx;
        border: 1px solid #E7E7E7;
        border-radius: 60rpx;
        font-size: 28rpx;
        color: #333;
    }
    .sucess .rrli-top-state {
        color: #ffa00e;
        font-weight: bold;
    }
    .r-process .rrli-top-state {
        color: red;
        font-weight: bold;
    }

    .no-record {
        text-align: center;
    }
    .ndt-icon image {
        width: 500rpx;
    }
    .ndt-tip {
        font-size: 24rpx;
        text-align: center;
        color: #999;
        margin-top: -100rpx;
    }
</style>
