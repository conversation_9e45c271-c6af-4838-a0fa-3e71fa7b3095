<template>
	<view class="change-name-box">
		<view class="cnb-input">
			<text>昵称</text>
            <input type="nickname" focus class="nomal-input" v-model="nickname" @blur="getNickname"  placeholder="输入昵称" />

        </view>
		<button class="btn btn-submit" @click="editNickname">提交</button>
	</view>
</template>

<script>
import api from "../../lib/api";
import {mapActions} from "pinia";
import {useUserStore} from "@/store/user";

export default {
	data() {
		return {
			nickname: null
		}
	},
	onLoad(e) {
		this.nickname = e.nickname
	},
	methods: {
        ...mapActions(useUserStore,['reload', 'logout']),

        getNickname(e) {
            console.log(e)
			this.nickname = e.detail.value
            console.log(this.nickname)
		},
		editNickname() {
			uni.showLoading({
				title: '保存中'
			});
			let data = {
				nickname: this.nickname
			}
			api.put('users/nickname', data).then(res => {
				uni.hideLoading();
				uni.showToast({
					title: "修改成功",
					icon: 'success'
				});
                this.reload()
				setTimeout(function () {
					uni.navigateBack()
				}, 2000)
			}, err => {
				uni.hideLoading();
				uni.showToast({
					title: err.message,
					icon: 'none'
				});
			})
		}
	}
}
</script>

<style>
    page {
		background-color: #F3F3FF;
    }
	.change-name-box {
		padding: 32rpx;
		background-color: #F3F3FF;
	}
	.cnb-input {
		background-color: #ffffff;
		border-radius: 12rpx;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		margin-bottom: 30rpx;
	}
	.cnb-input text {
		padding-right: 20rpx;
		font-size: 32rpx;
		color: #999;
		line-height: 100rpx;
	}
	.nomal-input {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		font-size: 32rpx;
	}

	.btn-submit {
		background-color: #db5132;
		color: #fff;
		font-size: 32rpx;
		border-radius: 12rpx!important;
		height: 100rpx!important;
		line-height: 100rpx!important;
	}
</style>
