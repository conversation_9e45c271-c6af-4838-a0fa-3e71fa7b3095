<template>
    <view class="content">
        <view class="docs-list-box">
            <template  v-for="(item ,index) in list" :key="item.id">
                <view class="download-date" v-if="index === 0 || item.created_date !== list[index - 1].created_date">{{item.created_date}}</view>
                <navigator url="###" class="dlb-item" @click="goDetail(item.content)">
                    <view class="dlbi-img">
                        <doc-icon :format="item.content.resource?.format" />
                    </view>
                    <view class="dlbi-cnt">
                        <view class="dlbic-tit">{{item.content.title}} </view>
                        <view class="dlbic-foot">
                            <view class="dlbicf-time">时间：<uni-dateformat :date="item.created_at" format="yyyy-MM-dd"></uni-dateformat></view>
                            <view class="dlbicf-pn" v-if="item.content.resource?.page_count">页数：{{ item.content.resource?.page_count }}</view>
                        </view>
                    </view>
                </navigator>
            </template>
        </view>

        <!-- 暂无数据 -->
        <view class="no-data-nomal-box" v-if="!loading && list.length === 0">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无下载</text>
        </view>
    </view>
</template>

<script>
import DocIcon from "@/components/doc-icon.vue";
import api from "@/lib/api";
import {alert, getDetailUrl, removeURLParameter, showDelayLoading} from "@/lib/utils";

export default {
    components: {
        DocIcon
    },
    data() {
        return {
            list: [],
            loading: true
        }
    },
    onLoad(query) {
        this.initData()
    },
    methods: {
        initData() {
            let loading = showDelayLoading("加载中", 200)
            api.get("cms/download-records").then(res =>{
                this.list = res
            }).catch(err =>  {
                alert(err.message)
            }).finally(() => {
                this.loading = false
                loading()
            })
        },
        goDetail(data) {
            let url = getDetailUrl(data.type_label, data.sid)
            if (url === '') {
                alert("未获取到详情地址")
                return
            }
            uni.navigateTo({
                url: url,
                success: () => {
                    if (data.type_label === 'video') {
                        uni.$emit("video-url", {
                            url: 'cms/download-records?type=' + data.type_label
                        })
                    }
                }
            })
        },
    },

}
</script>

<style>
page {
    background-color: #FFFFFF;
}

.content {
    padding: 30rpx;
}

.docs-list-box {
    background-color: #fff;
    border-radius: 12rpx;
}

.download-date {
    font-weight: bold;
    font-size: 32rpx;
    padding-bottom: 20rpx;
}

.docs-list-box {
    margin-bottom: 30rpx;
}

/* 资料列表 */
.dlb-item {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    border-radius: 12rpx;
}

.dlbic-tit {
    height: 80rpx;
    line-height: 40rpx;
    font-size: 28rpx;
    font-weight: bold;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.dlbic-foot {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #999;
    padding-top: 20rpx;
}

.dlbicf-time {
    padding-right: 30rpx;
}

.dlbi-img {
    position: relative;
    width: 140rpx;
    height: 140rpx;
    margin-right: 20rpx;
    border-radius: 12rpx;
    border: 1px solid #e7e7e7;
}

.doc-type {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 64rpx;
    height: 64rpx;
}

.dlbi-cnt {
    flex: 1;
}
</style>