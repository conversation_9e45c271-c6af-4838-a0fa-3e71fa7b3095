<template>
    <view class="contet">
        <view class="integral-num">
            <view class="in-b">{{ user.credit }}</view>
            <text>积分</text>
        </view>

        <view class="tab-nomal">
            <view class="tab-item" @click="switchType('')" :class="{'cur': !type}">全部</view>
            <view class="tab-item" @click="switchType('recharge')" :class="{'cur': type == 'recharge'}">获取</view>
            <view class="tab-item" @click="switchType('consume')" :class="{'cur': type == 'consume'}">消耗</view>
        </view>


        <view class="ir-list">
            <view class="ir-item" v-for="(item, index) in list" :key="index">
                <view class="ir-item-txt">
                    <view class="irit-h3">{{ item.remark }}</view>
                    <view class="irit-p"></view>
                    <view class="irit-span">
                        <uni-dateformat :date="item.created_at" format="MM-dd hh:mm:ss"></uni-dateformat>
                    </view>
                </view>
                <view class="ir-item-num add" v-if="item.type == 'recharge'">+{{ item.change_credit }}</view>
                <view class="ir-item-num reduce" v-else>{{ item.change_credit }}</view>
            </view>

            <!-- 暂无数据 -->
            <view class="no-data-nomal-box" v-if="!loading && list.length == 0">
                <view class="ndnb-icon">
                    <image src="../../images/empty.png" mode="widthFix"></image>
                </view>
                <text class="ndnb-tip">暂无数据</text>
            </view>
        </view>

    </view>
</template>

<script>
import api from "@/lib/api";
import {showDelayLoading, alert} from "@/lib/utils";
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store/user";

export default {
    data() {
        return {
            type: "",
            list: [],
            loading: true,
            cursor: '',
            hideLoading: ''
        }
    },
    computed: {
        ...mapState(useUserStore, ['user']),
    },
    onLoad(query) {
        this.hideLoading = showDelayLoading("请稍后", 200)
        this.initData()
        this.reload()
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.list = [];
        this.cursor = ""
        this.initData()

    },
    onReachBottom() {
        if (this.cursor) {
            this.initData()
        }
        console.log("上拉加载");
    },
    methods: {
        ...mapActions(useUserStore, ['reload', 'logout']),

        initData() {
            let data = {
                type: this.type,
                cursor: this.cursor
            }
            this.loading = true
            api.get("credit-records", data).then(res => {
                this.hideLoading()
                this.list.push(...res.data)
                this.cursor = res.next_cursor
                uni.stopPullDownRefresh();
            })
                .catch(err => {
                    this.hideLoading()

                    alert(err.message)
                })
                .finally(() => {
                    this.loading = false
                })
        },
        switchType(type) {
            this.hideLoading =showDelayLoading("请稍后", 200)

            this.list = [];
            this.cursor = ""
            this.type = type
            this.initData()
        }
    },
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.integral-num {
    padding: 30rpx 60rpx;
}

.integral-num .in-b {
    display: inline-block;
    font-size: 64rpx;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.integral-num text {
    font-size: 28rpx;
    color: #999;
    padding-left: 10rpx;
}

/* tab样式 */
.tab-nomal {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
}

.tab-item {
    padding: 30rpx;
    color: #999;
}

.tab-item.cur {
    font-weight: 700;
    color: #333;
}

/* 贝壳记录 */
.ir-list {
    background-color: #fff;
    margin: 0 30rpx;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.ir-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx;
    border-bottom: 1px solid #f3f3f3;
}

.ir-item-txt .irit-h3 {
    font-size: 32rpx;
    font-weight: 700;
}

.ir-item-txt .irit-p {
    font-size: 24rpx;
    padding: 14rpx 0;
    line-height: 1.6;
    color: #666;
}

.ir-item-txt .irit-span {
    display: block;
    font-size: 24rpx;
    color: #999;
}

.ir-item-txt .irit-span.c-orange {
    color: #fe9100;
    font-weight: bold;
}

.ir-item-num {
    font-size: 48rpx;
    font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
    padding-left: 30rpx;
}

.ir-item-num.reduce {
    color: red;
}

.ir-item-num.add {
    color: orange;
}

.decrement {
    width: 400rpx;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
}

.decrement .iin-txt {
    font-size: 24rpx;
    color: #999;
}

.decrement .iin-txt text {
    font-size: 48rpx;
    color: red;
}

.decrement .iin-back {
    font-size: 24rpx;
    background-color: #f3f3f3;
    border-radius: 60rpx;
    padding: 10rpx 20rpx 12rpx;
    margin-top: 10rpx;
    color: #999;
}

.no-record {
    text-align: center;
}

.ndt-icon image {
    width: 500rpx;
}

.ndt-tip {
    font-size: 24rpx;
    text-align: center;
    color: #999;
    margin-top: -100rpx;
}
</style>
