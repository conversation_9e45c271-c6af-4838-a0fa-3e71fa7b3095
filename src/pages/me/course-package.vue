<template>
    <view class="content">
        <view class="tl-list" v-if="list.length > 0">
            <view class="tll-item" :class="{timeout: item.is_expiration}" v-for="item in list" :key="item.sid" @click="goCoursePack(item.sid)">
                <view class="tlli-top">
                    <image :src="item.cover_src" mode="widthFix"></image>
                    <view class="timeout-tips" v-if="item.is_expiration">已过期</view>
                </view>
                <view class="tlli-mid">
                    <view class="tllim-l">
                        <image src="../../static/images/icon/file-zip-fill.png"></image>
                    </view>
                    <view class="tllim-r">
                        <view class="tllim-tit">{{ item.title }}</view>
                        <view class="tllim-foot">
                            <view class="tllimf-l">学习进度 {{ item.resource.progress_percent }}%</view>
                            <view class="tllimf-l">有效期至 {{item.expiration}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="no-data-nomal-box" v-else-if="loaded">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无数据</text>
        </view>
    </view>
</template>

<script>
import api from '@/lib/api.js';
import { alert, showDelayLoading } from '@/lib/utils.js';

export default {
    data() {
        return {
            list: [],
            loaded: false
        };
    },
    onLoad() {
        this.loadData();
    },
    methods: {
        loadData() {
            const cancel = showDelayLoading("加载中");
            api.get("cms/own-contents/course_pack").then(data => {
                this.list = data;
                this.loaded = true;
            }, err => {
                alert(err.message)
            }).finally(() => {
                cancel();
            });
        },
        goCoursePack(sid) {
            uni.navigateTo({
                url: '/pages/training/video/course-package?sid=' + sid
            });
        }
    }
};
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .tll-item {
        background-color: #fff;
        margin: 30rpx 30rpx 0;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    }
    .tlli-top {
        position: relative;
    }
    .tlli-top image {
        width: 100%;
        border-radius: 12rpx 12rpx 0 0;
        vertical-align: top;
    }
    .tlli-top .timeout-tips {
        display: none;
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        background-color: #999999;
        color: #FFFFFF;
        padding: 10rpx;
        border-radius: 6rpx;
        font-size: 24rpx;
    }

    .tlli-mid {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 20rpx 20rpx 30rpx;
    }
    .tllim-l {
        position: relative;
        padding-right: 20rpx;
    }
    .tllim-l image {
        width: 48rpx;
        height: 48rpx;
        vertical-align: top;
    }
    .tllim-r {
        flex: 1;
    }
    .tllim-tit {
        font-weight: bold;
        font-size: 32rpx;
        line-height: 1.6;
    }
    .tllim-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }

    /* 过期提示 */
    .tll-item.timeout .tlli-top .timeout-tips {
        display: inline-block;
    }
    .tll-item.timeout .tllim-tit {
        color: #999;
    }
</style>