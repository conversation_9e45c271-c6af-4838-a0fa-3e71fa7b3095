<template>
    <view class="content">

        <view class="tmain-lib-box" v-if="orgList.length && user">
            <view class="tlb-tit">
                <view class="tlbt-txt">我的培训机构</view>
            </view>
            <view class="tlb-org-list">
                <view class="tlbol-item" @click="goOrgDetail(item)"  v-for="item in orgList"  :key="item.sid">
                    <view class="tlboli-l">
                        <image src="/src/images/icon/building-4-line.png"></image>
                        <text>{{item.name}}</text>
                    </view>
                    <view class="tlboli-r">
                        <image src="/src/images/icon/arrow-right-s-line.png"></image>
                    </view>
                </view>
            </view>
        </view>

        <view class="ttb-warp">
            <!--
            <view class="training-tit-box">
                <view class="ttb-tit">
                    <text>中级注册安全工程师</text>
                </view>
                <view class="ttb-timing">
                    <view class="ttbt-top">距离考试</view>
                    <view class="ct-txt">{{ diffInDays }}
                        <text>天</text>
                    </view>
                </view>
            </view>
            -->


            <view class="tmain-lib-box">
                <view class="tlb-tit">
                    <view class="tlbt-txt">我的科目</view>
                </view>
                <uv-skeletons :loading="loading" :skeleton="skeleton">
                    <template>
                        <view class="tlb-empty-box" v-if="!isBuy || !user">
                            <view class="teb-txt">您尚未购买科目</view>
                            <view class="teb-cnt" @click="goCategory">
                                <image src="../../images/icon/add-line-2.png"></image>
                                <text>选择报考科目</text>
                            </view>
                        </view>

                        <view v-else class="training-exam">
                            <view class="te-list">
                                <view class="tel-item">
                                    <view class="teli-num">{{ subjectCount }}</view>
                                    <view class="teli-tit">总练习数</view>
                                </view>
                                <view class="tel-item">
                                    <view class="teli-num">{{ compRate }}%</view>
                                    <view class="teli-tit">完成率</view>
                                </view>
                                <view class="tel-item">
                                    <view class="teli-num">{{ right }}%</view>
                                    <view class="teli-tit">正确率</view>
                                </view>
                            </view>
                            <view class="te-tips">你上次正在练习
                                <text>{{ topic.name }}</text>
                                习题
                            </view>
                            <view class="te-start-btn" @click="start(this.topicId)">继续练习</view>
                        </view>

                        <!-- 已购科目列表 -->
                        <view class="tlb-sub-list" v-if="user" style="margin-bottom: 30rpx;">
                            <view class="tlbsl-item" @click="start(item.sid)" v-for="(item, index) in topicList"
                                  :key="index">
                                <view class="tlbsli-l">
                                    <text class="tlbsli-tip">已购</text>
                                    {{ item.name }}
                                    <text>共{{ item.subjects_count }}题</text>
                                </view>
                                <image src="../../images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                    </template>

                </uv-skeletons>
                <template v-if="recTopicList.length">
                    <view class="tlb-tit">
                        <view class="tlbt-txt">推荐科目</view>
                        <view class="tlbt-more" @click="goCategory">
                            <text>查看全部</text>
                            <image src="../../images/icon/arrow-drop-right-fill.png"></image>
                        </view>
                    </view>
                    <view class="tlb-sub-list">
                        <view class="tlbsl-item" @click="start(item.sid)" v-for="(item,index) in recTopicList"
                              :key="index">
                            <view class="tlbsli-l">
                                {{ item.name }}
                                <text>共{{ item.subjects_count }}题</text>
                            </view>
                            <image src="../../images/icon/arrow-right-s-line.png"></image>
                        </view>

                    </view>
                </template>
            </view>
        </view>

        <view class="video-list-box">
            <view class="my-vlb-box" v-if="meOwnContent.length">
                <view class="tlb-tit">
                    <view class="tlbt-txt">我的课程</view>
                </view>
                <view class="my-vlb-list">
                    <view class="my-vlb-item" v-for="(item, index) in meOwnContent" :key="index">
                        <view class="mvi-top">
                            <view class="mvit-l">
                                <view class="mvitl-tit">{{ item.title }}</view>
                                <view class="mvitl-teacher">讲师：{{ item.resource?.teacher_name }}</view>
                            </view>
                            <view class="mvit-r" @click="clickCourseData(item)">去学习</view>
                        </view>
                        <view class="mvi-foot">
                            <progress active-color="#df4b27" :percent="percent(item)"></progress>
                        </view>
                    </view>
                </view>
            </view>

            <view class="tlb-tit">
                <view class="tlbt-txt">全部课程</view>
                <navigator url="/pages/search/class" class="tlbt-s-btn">
                    <image src="../../images/icon/search-line.png"></image>
                    <text>搜索课程</text>
                </navigator>
            </view>

            <scroll-view class="sub-sview-list navScroll" scroll-x="true">
                <view class="sub-sview-item" @click="clickCourse('all')" :class="{cur: courseCategoriesIndex == 'all'}">
                    全部
                </view>
                <view class="sub-sview-item" @click="clickCourse(index)" :class="{cur: courseCategoriesIndex == index}"
                      v-for="(item, index) in courseCategories" :key="index">{{ item.name }}
                </view>
            </scroll-view>
            <view class="vlb-pane">
                <uv-skeletons :loading="loadingCategories" :skeleton="skeletonCourse">
                    <view class="video-item" @click="clickCourseData(item)" v-for="(item, index) in courseList"
                          :key="index">
                        <image :src="item.cover_src"></image>
                        <view class="vi-info">
                            <view class="vii-tit">{{ item.title }}</view>
                            <view class="vii-teacher">讲师：{{ item.resource.teacher_name }}</view>
                            <view class="vii-foot">
                                <view class="viif-l">{{ item.resource.learning_count }}人在学</view>
                                <view class="viif-r" v-if="item.view_limit == limitType.amount">￥{{ item.charge_amount }}</view>
                                <view class="viif-r" v-if="item.view_limit == limitType.credit">{{ item.charge_credit }}积分</view>
                                <view class="viif-r" v-if="item.view_limit == limitType.credit_amount">{{ item.charge_credit }}积分/￥{{ item.charge_amount }}</view>
                                <view class="viif-r" v-if="item.view_limit == limitType.free">免费</view>
                            </view>
                        </view>
                    </view>
                    <!--暂无数据-->
                    <view class="no-data-nomal-box" v-if="!loadingCategories && courseList.length === 0">
                        <view class="ndnb-icon">
                            <image src="../../images/empty.png" mode="widthFix"></image>
                        </view>
                        <text class="ndnb-tip">暂无数据</text>
                    </view>
                </uv-skeletons>
            </view>
        </view>
    </view>
</template>

<script>
import api, {setToken} from "../../lib/api";
import {getDetailUrl, showDelayLoading, alert} from "../../lib/utils";
import {loginRequired} from "@/lib/login";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {tabBarPageLoad} from "@/lib/tabbar.js";
import { ContentViewLimit as cvl } from "@/lib/enums";

export default {
    data() {
        return {
            compRate: 0,
            courseCategoryId: 0,
            name: "",
            right: 0,
            subjectCount: 0,
            topicId: 0,
            diffInDays: 0,
            courseCategories: [],
            courseCategoriesIndex: 'all',
            courseList: [],
            scroll_id: '',
            loading: true,
            loadingCategories: false,
            isInitOpen: false,
            isBuy: true,
            topic: {},
            topicList: [],
            recTopicList: [],
            meOwnContent: [],
            orgList: [],
            limitType: cvl,
            skeleton: [
                {
                    type: 'flex',
                    style: "marginTop:50rpx",
                    children: [
                        {
                            type: 'custom',
                            style: 'width:150rpx;height:70rpx;marginLeft:70rpx; marginBottom:30rpx;'
                        }, {
                            type: 'custom',
                            style: 'width:150rpx;height:70rpx;marginLeft:70rpx;'
                        }, {
                            type: 'custom',
                            style: 'width:150rpx;height:70rpx;marginLeft:70rpx;'
                        },
                    ]
                },
                {
                    type: 'custom',
                    num: 1,
                    style: 'width:500rpx;height:88rpx;marginRight: 50rpx;marginLeft: 100rpx;'
                },
                30,
                {
                    type: 'line',
                    style: "marginBottom:50rpx;marginLeft:50rpx;marginRight: 50rpx;",
                    num: 3,
                }
            ],
            skeletonCourse: [
                {
                    type: 'flex',
                    num: 5,
                    style: 'backgroundColor: #fff;borderRadius: 12rpx;marginLeft: 30rpx;',
                    children: [
                        {
                            type: 'custom',
                            num: 1,
                            style: 'width:136rpx;height:136rpx;marginRight: 10rpx;marginLeft: 10rpx;marginTop: 50rpx;'
                        }, {
                            type: 'line',
                            num: 3,
                            gap: '30rpx',
                            style: ['width: 200rpx;marginTop: 40rpx;', 'width:300rpx;', 'marginRight: 10rpx;']
                        },
                    ]
                },
            ]

        }
    },
    onLoad(query) {
        this.initData()
        this.initCourse()
        this.getCourseList()
        uni.$on('topic_init_data', () => {
            this.courseList = []
            this.scroll_id = ''
            this.initData()
            this.initCourse()
            this.getOrgList()
            this.getCourseList()
        });
        if (this.user) {
            this.getOrgList()
        }
        tabBarPageLoad();
    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("topic_init_data")
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.scroll_id && this.loading) {
            this.getCourseList();
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    watch: {
        user() {
            if (this.user) {
                this.initData()
                this.getOrgList()
            }
        }
    },
    methods: {
        initData() {
            let hideLoading = showDelayLoading('加载中', 200)
            this.loading = true
            api.get("pages/train").then(res => {
                hideLoading()
                this.compRate = res.comp_rate;
                this.courseCategoryId = res.course_category_id;
                this.name = res.name;
                this.right = res.right;
                this.subjectCount = res.subject_count;
                this.topicId = res.topic_id;
                this.diffInDays = res.diff_in_days;
                this.isInitOpen = res.is_init_open;
                this.isBuy = res.is_buy;
                this.topic = res.topic;
                this.topicList = res.topic_list;
                this.recTopicList = res.rec_topic_list;
                this.meOwnContent = res.me_own_content;
            }).catch(err => {
                hideLoading();
                alert(err.message)
            }).finally(() => {
                this.loading = false
            })
        },
        chooseTopic(sid) {
            api.get("training/topics/" + sid).then(res => {
                this.compRate = res.comp_rate;
                this.courseCategoryId = res.course_category_id;
                this.name = res.name;
                this.right = res.right;
                this.subjectCount = res.subject_count;
                this.topicId = res.topic_id;
                this.diffInDays = res.diff_in_days;
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
        initCourse() {
            api.get('cms/categories?classify=course').then(res => {
                this.courseCategories = res;
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            }).finally(() => {
            })
        },
        getCourseList() {
            let cate_id = "course"
            if (this.courseCategoriesIndex != 'all') {
                cate_id = this.courseCategories[this.courseCategoriesIndex].sid
            }
            let data = {
                scroll_id: this.scroll_id
            }

            this.loadingCategories = true
            api.get('cms/' + cate_id + '/contents', data).then(res => {
                this.courseList.push(...res.data);
                this.scroll_id = res.next_cursor
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            }).finally(() => {

                this.loadingCategories = false
            })
        },
        getOrgList() {

            api.get(`orgs`).then(res => {
                this.orgList = res
            })
        },
        start(topicId) {
            loginRequired().then(() => {

                api.put("training/topics/" + topicId + "/choose").then(res => {
                    this.initData()
                }).catch(err => {
                    alert(err.message)
                })
                if (this.isInitOpen) {
                    uni.navigateTo({
                        url: '/pages/training/exam/category?topic_id='
                    })
                } else {
                    uni.navigateTo({
                        url: '/pages/training/exam/index?topic_id=' + topicId
                    })
                }

            })
        },
        percent(data) {
            if (!data.progresses_count) {
                return 0
            }
            return data.progresses_count / data.sections_count * 100
        },
        goCategory() {
            loginRequired().then(() => {
                uni.navigateTo({
                    url: '/pages/training/exam/category'
                })
            })
        },
        clickCourse(index) {
            this.loadingCategories = true
            this.courseList = []
            this.scroll_id = ''
            this.courseCategoriesIndex = index
            this.getCourseList()
        },
        clickCourseData(data) {
            let url = getDetailUrl("course", data.sid)
            uni.navigateTo({
                url
            })
        },
        goOrgDetail(data) {
            uni.navigateTo({
                url: '/packageOrg/organization/home?org_sid=' + data.sid
            })
        }
    },


}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

.training-tit-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;
}

.ttb-tit {
    display: flex;
    align-items: center;
    height: 90rpx;
    font-size: 48rpx;
    padding-right: 10rpx;
}

.ttb-tit text {
    flex: 1;
}

.sel-lib-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    background-color: #fff;
    border-radius: 100%;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    margin-left: 20rpx;
}

.sel-lib-icon image {
    width: 32rpx;
    height: 32rpx;
    vertical-align: top;
}

.ttb-timing {
    position: relative;
    width: 120rpx;
    height: 90rpx;
    font-size: 28rpx;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    border-radius: 12rpx;
    background-color: #fff;
}

.ttbt-top {
    font-size: 24rpx;
    color: #fff;
    background-color: #390ABC;
    padding: 4rpx 0 4rpx 6rpx;
    border-radius: 12rpx 12rpx 30rpx 0;
}

.ct-txt {
    text-align: center;
    font-size: 28rpx;
    height: 50rpx;
    line-height: 50rpx;
    font-weight: bold;
    color: #390ABC;
}

.ct-txt text {
    font-size: 24rpx;
    padding-left: 6rpx;
    color: #999;
    font-weight: normal;
}

.training-exam {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx 30rpx 40rpx;
}

.te-list {
    display: flex;
    align-items: center;
}

.tel-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0 40rpx;
}

.teli-num {
    font-size: 38rpx;
    font-weight: bold;
}

.teli-tit {
    font-size: 28rpx;
    padding-top: 12rpx;
    color: #999;
}

.te-tips {
    font-size: 28rpx;
    color: #999;
    padding-bottom: 30rpx;
    text-align: center;
}

.te-tips text {
    color: #390ABC;
    padding: 0 12rpx;
    font-weight: bold;
}

.te-start-btn {
    height: 88rpx;
    line-height: 88rpx;
    background-color: #df4b27;
    color: #fff;
    border-radius: 88rpx;
    text-align: center;
    margin: 0 40rpx;
}

.video-list-box {
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    border-radius: 12rpx;
}

.vlb-tab {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #F3F3FF;
}

.vlb-pane {
    padding-bottom: 30rpx;
}

.vlbt-item {
    display: flex;
    align-items: center;
    padding: 30rpx 30rpx 40rpx;
    color: #999;
    font-size: 32rpx;
}

.vlbt-item.cur {
    position: relative;
    font-weight: bold;
    color: #333;
}

.vlbt-item.cur::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 20rpx;
    width: 20rpx;
    height: 8rpx;
    border-radius: 6rpx;
    background-color: #390ABC;
}

.video-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 12rpx;
    padding: 30rpx 30rpx 0;
}

.video-item image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
}

.vi-info {
    flex: 1;
    padding-left: 20rpx;
}

.vii-tit {
    height: 80rpx;
    line-height: 40rpx;
    font-weight: bold;
    font-size: 28rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.vii-teacher {
    padding: 10rpx 0;
    font-size: 28rpx;
}

.vii-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    padding-top: 8rpx;
}

.viif-l {
    color: #999;
}

.viif-r {
    color: red;
    font-weight: bold;
}


.sub-sview-list {
    white-space: nowrap;
    width: 100%;
    padding: 20rpx 0;
    border-bottom: 1px solid #F3F3FF;
}

.sub-sview-item {
    display: inline-block;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    font-size: 24rpx;
    border: 1px solid #E7E7E7;
    border-radius: 70rpx;
    margin-left: 30rpx;
    color: #666;
}

.sub-sview-item.cur {
    background-color: #390ABC;
    color: #fff;
    border: 1px solid #390ABC;
}

/* 优化样式 */
.tmain-lib-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    padding-bottom: 20rpx;
}

.tlb-tit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100rpx;
    padding: 0 20rpx 0 30rpx;
}
.tlbt-s-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 68rpx;
    border-radius: 68rpx;
    padding: 0 30rpx;
}
.tlbt-s-btn image {
    width: 36rpx;
    height: 36rpx;
    margin-top: 4rpx;
}
.tlbt-s-btn text {
    font-size: 28rpx;
    color: #999;
    padding-left: 10rpx;
}

.tlbt-txt {
    font-size: 28rpx;
    font-weight: bold;
}

.tlbt-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100rpx;
}

.tlbt-more text {
    font-size: 24rpx;
    color: #999;
}

.tlbt-more image {
    width: 32rpx;
    height: 32rpx;
}

.tlb-empty-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60rpx 0 80rpx;
}

.teb-txt {
    color: #999;
}

.teb-cnt {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #FFA10A;
    font-size: 28rpx;
    padding-top: 10rpx;
}

.teb-cnt image {
    height: 32rpx;
    width: 32rpx;
}

.teb-cnt text {
    padding-left: 6rpx;
}


.tlbsl-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
}

.tlbsli-l text {
    font-size: 24rpx;
    color: #999;
    padding-left: 20rpx;
}

.tlbsl-item image {
    width: 32rpx;
    height: 32rpx;
}

.tlbsli-l .tlbsli-tip {
    background-color: #df4b27;
    color: #fff;
    padding: 4rpx 8rpx;
    border-radius: 6rpx;
    margin-right: 6rpx;
    margin-right: 8rpx;
}

.my-vlb-item {
    padding: 0 30rpx;
    margin-bottom: 30rpx;
}

.mvi-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mvitl-tit {
    font-size: 28rpx;
    font-weight: bold;
}

.mvitl-teacher {
    font-size: 24rpx;
    color: #999;
    padding-top: 10rpx;
}

.mvit-r {
    height: 68rpx;
    padding: 0 30rpx;
    line-height: 68rpx;
    background-color: #df4b27;
    color: #fff;
    border-radius: 68rpx;
    font-size: 24rpx;
}

.mvi-foot {
    position: relative;
    height: 14rpx;
    background-color: #e7e7e7;
    border-radius: 14rpx;
    margin-top: 20rpx;
}

.mvi-foot text {
    position: absolute;
    left: 0;
    top: 0;
    height: 14rpx;
    background-color: #df4b27;
    border-radius: 14rpx;
}

.my-vlb-box {
    border-bottom: 1px solid #F3f3ff;
}

/* 培训机构 */
.tlbol-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 30rpx;
    height: 80rpx;
}
.tlboli-l {
    flex: 1;
    display: flex;
    align-items: center;
}
.tlboli-l image,
.tlboli-r image {
    width: 32rpx;
    height: 32rpx;
}
.tlboli-l text {
    font-size: 28rpx;
    padding-left: 10rpx;
}

</style>