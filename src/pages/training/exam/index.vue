<template>
    <view class="content">
        <view @click="goCategory" class="change-cat">
            <view  class="cc-tit" >切换科目</view>
            <view class="cc-cat-txt">
                <text>{{ name }}</text>
                <image src="../../../images/icon/arrow-right-s-line.png"></image>
            </view>
        </view>

        <view class="buy-vip-tips" v-if="loading == false && !isBuy">
            <view class="bvt-tit">
                <image src="../../../images/icon/file-warning-fill.png"></image>
                <text>尚未购买该题库</text>
            </view>
            <view class="bvt-buy" @click="buyTopic">点击购买</view>
        </view>

        <view class="buy-vip-tips" v-if="!isPermanent && isPurchased">
            <view class="bvt-tit">
                <image src="../../../images/icon/vip-fill.png"></image>
                <text>有效期至：</text>
                <uni-dateformat :date="expiredAt" format="yyyy-MM-dd hh:mm"></uni-dateformat>
            </view>
            <view class="bvt-xu" @click="buyTopic">续费</view>
        </view>

        <view class="exam-main-box">
            <view class="emb-l">
                <view class="emb-item" @click="special">
                    <image src="../../../images/exam/lib_03.png"></image>
                    <text>专项训练</text>
                </view>
                <view class="emb-item" @click="chapter" v-if="chapterCount">
                    <image src="../../../images/exam/lib_01.png"></image>
                    <text>章节练习</text>
                </view>
                <view class="emb-item" @click="search">
                    <image src="../../../images/exam/lib_02.png"></image>
                    <text>题库搜索</text>
                </view>
                <view class="emb-item" v-if="courseCategoryId || courseContentId" @click="goVideo">
                    <image src="../../../images/exam/lib_09.png"></image>
                    <text>视频讲解</text>
                </view>
            </view>
            <view class="emb-m">
                <view class="emb-exam-btn" @click="start(3)">
                    模拟考试
                </view>
            </view>
            <view class="emb-r">
                <view class="emb-item" @click="start(1)">
                    <image src="../../../images/exam/lib_10.png"></image>
                    <text>顺序练习</text>
                </view>
                <view class="emb-item" @click="start(5)">
                    <image src="../../../images/exam/lib_06.png"></image>
                    <text>题目收藏</text>
                </view>
                <view class="emb-item" @click="start(4)">
                    <image src="../../../images/exam/lib_11.png"></image>
                    <text>错题本</text>
                </view>
            </view>
        </view>
    </view>
    <uni-popup ref="contact" type="center">
        <view class="contact-box">
            <view class="cb-tit">联系客服</view>
            <view class="cb-content">
                <view class="cbc-h">长按二维码，添加客服</view>
                <view class="cbc-img" v-if="orgData">
                    <image :show-menu-by-longpress="true" :src="orgData.service_qrcode_url" />
                </view>
            </view>
            <view class="cb-foot-close" @click="this.$refs.contact.close()">关闭</view>
        </view>
    </uni-popup>
</template>

<script>
import api from "../../../lib/api";
import {showDelayLoading, alert, showToast, getDetailUrl} from "../../../lib/utils";
import {makePayment} from "@/lib/pay";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";

export default {
    data() {
        return{
            topicSid: '',
            name: '',
            isPurchased:'',
            expiredAt:'',
            isPermanent: false,
            isBuy: false,
            courseCategoryId: '',
            courseContentId: '',
            chapterCount: '',
            loading:false,
            judge:0,
            multi:0,
            single:0,
            videoSid: '',
            enrollSid: '',
            orgId: '',
        }
    },
    onLoad(query) {

        if (query.org_sid) {
            this.orgId =  query.org_sid
        }

        if (query.enroll_sid) {
            this.enrollSid =  query.enroll_sid
        }
        if (query.topic_id){
            this.topicSid = query.topic_id
            this.initData()
        }
        uni.$on('choose_topic', id => {
            this.topicSid = id
            this.initData()
        })
        uni.$on('buy_topic_success', () => {
            this.initData()
        })

    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("choose_topic")
        uni.$off("buy_topic_success")
    },
    computed: {
        ...mapState(useUserStore, ['orgData']),
    },
    methods:{
        initData(){
            this.loading = true
            api.get("training/topics/"+ this.topicSid, {org_sid: this.orgId}).then(res => {
                this.name = res.name;
                this.isBuy = res.isBuy;
                this.isPurchased = res.is_purchased;
                this.courseCategoryId = res.course_category_id
                this.courseContentId = res.course_content_id
                this.judge = res.judge
                this.isPermanent = res.is_permanent
                this.expiredAt = res.expired_at
                this.multi = res.multi
                this.single = res.single
                this.chapterCount = res.chapter_count
                this.videoSid = res.video_sid
            }).finally(() => this.loading = false)
        },
        alertModel() {
            if (this.orgId && false) {
                this.$refs.contact.open('center')
            } else {
                uni.showModal({
                    title:'温馨提示',
                    content:"您尚未购买该题库，请购买后再试",
                    confirmText:"去购买",
                    success:(res) => {
                        if (res.confirm){
                            this.buyTopic()
                        }
                    }
                })
            }
        },
        goCategory() {
            if (this.orgId) {
                uni.navigateTo({
                    url: "/packageOrg/organization/topics?is_back=1&topic_id=" + this.topicSid
                })
                return
            }
            uni.navigateTo({
                url: '/pages/training/exam/category?is_back=1&topic_id=' + this.topicSid
            })
        },
        /**
         * 开始考试
         */
        start(type){
            if(this.isBuy == false){
                this.alertModel()
                return;
            }
            if (type == 3){
                uni.navigateTo({
                    url: "/pages/training/exam/start-test?topic_sid=" + this.topicSid + "&name=" +this.name + "&org_sid=" + this.orgId
                })
                return
            }

            api.get("training/tests/current/"+this.topicSid+"?type=" +type).then(res => {
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.test.sid
                })

            }).catch(err =>{

                if (err.code == 404){
                    this.createTest(type)
                }else {
                    uni.hideLoading();
                    alert(err.message)
                }
            })
        },
        createTest(type){
            const hideLoading = showDelayLoading("请稍后", 200)

            api.post("training/"+this.topicSid+"/tests",{type : type}).then(res => {
                hideLoading()
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.sid
                })
            }).catch(err => { uni.hideLoading();alert(err.message)})
        },
        buyTopic(){
            uni.navigateTo({
                url:'/pages/training/exam/buy?sid=' + this.topicSid + "&org_sid=" + this.orgId + "&enroll_sid=" + this.enrollSid
            })
        },
        search(){
            if(this.isBuy == false){
                this.alertModel()
                return;
            }
            uni.navigateTo({
                url: '/pages/training/exam/search?topic_id=' + this.topicSid
            })
        },
        special(){
            if(!this.isBuy){
                this.alertModel()
                return;
            }
            uni.navigateTo({
                url: '/pages/training/exam/special?topic_id=' + this.topicSid +"&judge=" + this.judge + "&multi=" + this.multi + "&single=" + this.single
            })
        },
        chapter(){
            if(!this.isBuy){
                this.alertModel()
                return;
            }
            uni.navigateTo({
                url: '/pages/training/exam/chapter?topic_id=' + this.topicSid +"&judge=" + this.judge + "&multi=" + this.multi + "&single=" + this.single
            })
        },
        goVideo(){
            if (this.courseCategoryId){
                uni.navigateTo({
                    url: "/pages/training/video/list?cateId="+ this.courseCategoryId
                })
            }else {
                uni.navigateTo({
                    url:"/pages/training/video/detail?sid=" + this.courseContentId + `&org_sid=${this.orgId}&enroll_sid=${this.enrollSid}`
                })
            }

        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .change-cat {
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        padding-bottom: 10rpx;
    }
    .cc-tit {
        font-size: 28rpx;
        padding: 30rpx 30rpx 10rpx;
        color: #999;
    }
    .cc-cat-txt {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;
        height: 100rpx;
        font-size: 28rpx;
        font-weight: bold;
    }
    .cc-cat-txt image {
        width: 32rpx;
        height: 32rpx;
    }

    .exam-main-box {
        display: flex;
        align-items: center;
        padding: 30rpx;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .emb-l {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    .emb-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30rpx;
    }
    .emb-item image {
        width: 100rpx;
        height: 100rpx;
    }
    .emb-item text {
        font-size: 28rpx;
        font-weight: bold;
        padding-top: 20rpx;
    }
    .emb-m {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .emb-exam-btn {
        width: 180rpx;
        height: 180rpx;
        border-radius: 100%;
        background-image: linear-gradient(to top, #4481eb 0%, #04befe 100%);
        color: #fff;
        text-align: center;
        line-height: 180rpx;
        font-size: 28rpx;
    }
    .buy-vip-tips {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fefdeb;
        margin-bottom: 30rpx;
        height: 80rpx;
        padding: 10rpx 20rpx 10rpx 30rpx;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .bvt-tit {
        display: flex;
        align-items: center;
        color: #F2AC3C;
    }
    .bvt-tit image {
        width: 48rpx;
        height: 48rpx;
    }
    .bvt-tit text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .bvt-buy {
        background-color: #f2ac3c;
        border-radius: 12rpx;
        height: 60rpx;
        line-height: 60rpx;
        color: #fff;
        font-size: 24rpx;
        padding: 0 26rpx;
    }
    .bvt-xu {
        font-size: 28rpx;
        text-decoration: underline;
        padding: 20rpx 30rpx;
        color: #f2ac3c;
    }


    /* 联系客服弹窗 */
    .contact-box {
        background-color: #fff;
        border-radius: 20rpx;
        width: 600rpx;
    }
    .cb-tit {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        font-weight: bold;
    }
    .cbc-h {
        text-align: center;
        height: 60rpx;
        line-height: 60rpx;
    }
    .cbc-img {
        padding: 30rpx;
        width: 540rpx;
    }
    .cbc-img image {
        width: 540rpx;
    }
    .cb-foot-close {
        text-align: center;
        height: 100rpx;
        background-color: #f3f3f3;
        color: #999;
        font-size: 28rpx;
        line-height: 100rpx;
        border-radius: 0 0 20rpx 20rpx;
    }
</style>