<template>
    <view class="content">
        <view class="esb-fixed">
            <view class="exam-search-box">
                <image src="../../../images/icon/search-line.png"></image>
                <input class="uni-input" v-model="content" :focus="focus" placeholder="请输入关键词" @input="clearInput"/>
                <view class="search-clear-btn" v-if="content" @click="clearIcon">
                    <view class="scb-icon">
                        <image src="@/images/icon/close-line-white.png"></image>
                    </view>
                </view>
                <button :disabled="content.length === 0" class="esb-btn" @click="search">搜索</button>
            </view>
        </view>
        <view class="esb-eb"></view>

        <!-- 搜索记录 -->
        <view class="search-history" v-if="searchName.length && !contentText">
            <view class="sh-tit">
                <text>最近搜索</text>
                <view class="sht-delete" @click="delSearchName">
                    <image  src="../../../images/icon/delete-bin-line.png"></image>
                </view>
            </view>
            <view class="sh-list">
                <view class="shl-item" @click="getSearchName(item)"  v-for="(item, index) in searchName" :key="index">{{item}}</view>
            </view>
        </view>

        <view v-if="isSearch" class="es-info">与<text>{{contentText}}</text>相关的试题：
        </view>
        <view class="es-result-list">
            <view class="esrl-item" v-for="(item, index) in list" :key="index">
                <view class="esrl-tit">
                    <text class="esrl-type">
                         <template v-if="item.type == 1">单选题</template>
                         <template v-else-if="item.type == 2">多选题</template>
                         <template v-else>判断题</template>
                    </text>
                    <mp-html :content="item.intro"></mp-html>
                </view>
                <view class="esrl-r-l" v-for="(option, optionIndex) in item.option" :key="optionIndex">
                    <view class="esrl-r-l-item">{{option.name}}</view>
                </view>
                <view class="esrl-daan">参考答案：
                    <template v-for="(option, optionIndex) in item.option" :key="optionIndex">
                        <text v-if="option.is_correct == 1">{{abcd[optionIndex]}}</text>
                    </template>
                </view>
                <view class="esrl-jiexi-cnt" v-if="item.analysis">
                    <text>解析：</text>
                    <mp-html :content="item.analysis"></mp-html>
                </view>
            </view>
            <uni-load-more v-if="list.length"  :status="statusLoad" />

        </view>

        <!-- 暂无数据 -->
        <view class="no-data-nomal-box" v-if="!loading && list.length === 0">
            <view class="ndnb-icon">
                <image src="../../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">抱歉，未检索到相关题目</text>
        </view>
    </view>
</template>

<script>
import api from "../../../lib/api";
import {alert} from "../../../lib/utils";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'

export default {
    components: {
        mpHtml
    },
    data() {
        return {
            abcd: ["A", "B", "C", "D"],
            list: [],
            topicId: '',
            contentText: '',
            content: '',
            scroll_id: '',
            isSearch: false,
            loading: true,
            focus: true,
            searchName: [],
            showClearIcon: false,
            statusLoad: 'loading',

        }
    },
    onLoad(query) {
        if (query.topic_id) {
            this.topicId = query.topic_id
        }
        if (uni.getStorageSync('searchName')){
            this.searchName = uni.getStorageSync('searchName');
        }
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.scroll_id) {
            this.getSubject();
        }
    },
    methods: {
        search() {
            if (!this.content){
                return
            }
            this.scroll_id = ''
            this.list = []
            if (!this.searchName.includes(this.content)){
                this.searchName.unshift(this.content)

                if (this.searchName.length > 20) {
                    this.searchName.pop(); // 删除数组中的最后一个元素
                }
            }

            uni.setStorageSync('searchName', this.searchName)
            this.getSubject()
        },
        getSubject() {

            if (!this.content) {
                uni.showToast({
                    title: "请输入关键词",
                    icon: "none"
                })
                return
            }

            let data = {
                intro: this.content,
                cursor: this.scroll_id
            }
            this.contentText = data.intro
            this.isSearch = true
            this.loading = true
            api.get("training/topics/" + this.topicId + "/search", data).then(res => {
                this.list.push(...res.data)
                console.log(this.list)
                this.scroll_id = res.next_cursor
                if (!res.data.length){
                    this.statusLoad = 'noMore'
                }
            }).catch(err => alert(err.message)).finally(() => {
                this.loading = false
            })
        },
        delSearchName(){
            this.searchName = [];
            uni.setStorageSync('searchName', this.searchName)
        },
        getSearchName(name){
            this.content = name
            this.search()
        },
        clearInput(e) {
            if (e.detail.value.length > 0) {
                this.showClearIcon = true;
            } else {
                this.showClearIcon = false;
            }
        },
        clearIcon() {
            this.content = '';
            this.contentText = '';
            this.list = []
            this.showClearIcon = false;
            this.isSearch = false;
            this.loading = true;
        }
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

.esb-fixed {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    background-color: #F3F3FF;
    padding: 30rpx;
    z-index: 3;
}

.esb-eb {
    height: 130rpx;
}

.exam-search-box {
    display: flex;
    align-items: center;
    background-color: #FFF;
    border-radius: 12rpx;
    padding: 0 0 0 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.exam-search-box image {
    width: 32rpx;
    height: 32rpx;
}

.exam-search-box .uni-input {
    flex: 1;
    height: 100rpx;
    padding-left: 20rpx;
    font-size: 28rpx;
}

.esb-btn {
    height: 100rpx;
    line-height: 100rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    color: #390ABC;
}
.clear {
    height: 100rpx;
    line-height: 100rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
}

.es-info {
    padding: 0 0 30rpx;
    font-size: 28rpx;
    color: #999;
}

.es-info text {
    font-weight: bold;
    font-style: italic;
    padding: 0 10rpx;
    color: #333;
}

.esrl-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.esrl-tit {
    font-size: 28rpx;
    font-weight: bold;
    padding-bottom: 10rpx;
    line-height: 1.8;
}

.esrl-type {
    height: 30rpx;
    line-height: 30rpx;
    padding: 8rpx 10rpx;
    background-color: #065CDF;
    color: #fff;
    display: inline-block;
    border-radius: 12rpx;
    font-size: 28rpx;
    margin-right: 10rpx;
    font-weight: normal;
}

.esrl-r-l-item {
    font-size: 28rpx;
    line-height: 1.8;
    padding-bottom: 20rpx;
}

.esrl-daan {
    font-size: 28rpx;
    padding-bottom: 20rpx;
    font-weight: bold;
}

.esrl-jiexi-cnt {
    font-size: 28rpx;
    line-height: 1.8;
}

.esrl-jiexi-cnt text {
    font-weight: bold;
}

/* 搜索历史 */
    .sh-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10rpx;
    }
    .sh-tit text {
        font-size: 28rpx;
        color: #999;
    }
    .sht-delete {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        width: 80rpx;
    }
    .sht-delete image {
        width: 32rpx;
        height: 32rpx;
    }
    .sh-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
    }
    .shl-item {
        height: 80rpx;
        line-height: 80rpx;
        padding: 0 30rpx;
        margin: 0 30rpx 30rpx 0;
        background-color: #fff;
        border-radius: 80rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }

/* 搜索清楚按钮 */
.search-clear-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100rpx;
    padding: 0 20rpx;
}
.scb-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
    border-radius: 100%;
    background-color: rgba(0, 0, 0, .3);
    margin-right: 10rpx;
}
.search-clear-btn image {
    width: 28rpx;
    height: 28rpx;
}
</style>