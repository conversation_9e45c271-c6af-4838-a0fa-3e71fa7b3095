<template>
    <view class="subject-container">
        <view class="indicator">{{ currentSubject.id }} / {{ subjectsTotal }}</view>
        <swiper class="swiper" :current="currentSubjectIndex" @change="subjectChange" @animationfinish="animationFinished" :duration="200" disable-programmatic-animation easing-function="easeOutCubic">
            <swiper-item v-for="item in subjects" :key="item.id">
                <view class="swiper-item">{{ item.name }}</view>
            </swiper-item>
        </swiper>
        <button @click="bigStep">往后一大步</button>
    </view>
</template>

<script>
const allSubjects = [];

for (let i=0; i<1000; i++) {
    const id = i + 1;
    allSubjects.push({
        id: id.toString(),
        name: "第 " + id + " 题"
    });
}

export default {
    data() {
        return {
            subjects: [],
            currentSubjectIndex: 0,
            subjectsTotal: 0,
            currentPage: 1,
            pageCount: 10
        }
    },
    computed: {
        currentSubject() {
            return this.subjects[this.currentSubjectIndex] || {};
        }
    },
    onLoad() {
        this.subjectsTotal = allSubjects.length;
        this.loadPage(this.currentPage);
    },
    methods: {
        loadPage(page, index) {
            uni.showNavigationBarLoading();

            const count = this.pageCount;
            const pageTotal = Math.ceil(allSubjects.length / count);

            console.log("load page", {page, index});

            const start = (page - 1) * count;
            const subjects = allSubjects.slice(start, start + count);

            //上一页的影子题，一定要把实际的对象放在 assign() 的第二个参数
            if (page > 1) {
                const prevPageShadow = Object.assign({
                    shadow: "prev"
                }, allSubjects[(page - 1) * count - 1]);
                subjects.unshift(prevPageShadow);
            }

            //下一页的影子题
            if (page < pageTotal) {
                const nextPageShadow = Object.assign({
                    shadow: "next"
                }, allSubjects[page * count]);
                subjects.push(nextPageShadow);
            }

            //模拟加载延迟的效果
            setTimeout(() => {
                //默认定位到第一个
                if (index == undefined) {
                    index = subjects[0].shadow == "prev" ? 1 : 0;
                }

                uni.hideNavigationBarLoading();
                this.subjects = subjects;
                this.currentSubjectIndex = index;
                this.currentPage = page;
            }, 300);
        },
        subjectChange(e) {
            console.log("change", e);
            this.currentSubjectIndex = e.detail.current;

            //在切页时立即切换
            //优点：永远都能正常切换
            //缺点：切换时动画还未结束，数据就可能更新了，造成数据跳动或卡顿的现象
            const subject = this.subjects[e.detail.current];
            if (subject.shadow) {
                setTimeout(() => this.switchSwiperPosition(subject.shadow), 200);
            }
        },
        animationFinished(e) {
            // console.log("animationfinish", e);

            //在动画结束时切换（注意在 change 或 animationfinish 中切换必须二选一，不能同时使用）
            //优点：比较流畅，正常情况下用户无感知
            //缺点：如果用户连续滑动，不给动画结束的机会，会出现切页时无法加载下一页数据，拉不动下一页

            // const subject = this.subjects[e.detail.current];
            // if (subject.shadow) {
            //     this.switchSwiperPosition(subject.shadow);
            // }
        },
        /**
         * 切换 Swiper 分页定位
         * @param {String} shadow 往前翻还是往后翻
         */
        switchSwiperPosition(shadow) {
            if (shadow === "prev") {
                const page = this.currentPage - 1;
                //往前翻的时候，如果到了第一页，实际的数量比分页数量多1条（后面多一条填充数据），如果没到第一条，实际的数量比分页的数量多2条（前后各多一条填充数据）
                //所以定位到前一页实际数据的最后一条要考虑这里多的数量
                const index = page == 1 ? this.pageCount - 1 : this.pageCount;
                this.loadPage(page, index);
            } else {
                this.loadPage(this.currentPage + 1);
            }
        },
        bigStep() {
            const to = this.currentSubjectIndex + 5;
            if (this.subjects[to]) {
                this.currentSubjectIndex = to;
            } else {
                this.currentSubjectIndex = this.subjects.length - 1;
            }
        }
    }
};
</script>

<style>
page, .subject-container {
    height: 100%;
}

.swiper {
    height: 80%;
}

.indicator {
    padding: 30rpx 20rpx;
    text-align: center;
}

.swiper-item {
    padding: 200rpx 20rpx;
    text-align: center;
    box-sizing: content-box;
    background-color: #ccc;
}
</style>