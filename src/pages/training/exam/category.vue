<template>
    <view class="content">
        <view class="category-list">
            <view class="cl-item" :class="{cur: item.sid == sid}" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
                <text>{{ item.name }}</text>
                <image v-if="item.sid == sid" src="../../../images/icon/check-line-blue.png"></image>

                <image v-else src="../../../images/icon/arrow-right-s-line.png"></image>
            </view>
        </view>
    </view>
</template>

<script>
    import {alert} from "../../../lib/utils";
    import api from "../../../lib/api";

    export default {
        data() {
            return {
                list: [],
                isBack: false,
                sid:''
            }
        },
        onLoad(query) {

            this.initData()
            this.sid = query.topic_id
            if (query.is_back){
                this.isBack = true
            }
        },
        methods:{
            initData(){
                api.get("training/topics").then(res => {
                    this.list = res;
                }).catch(err =>  { uni.hideLoading();alert(err.message)})
            },
            goDetail(item){
                this.sid = item.sid
                api.put("training/topics/"+item.sid+"/choose").then(res => {
                    uni.$emit('choose_topic', item.sid)
                    uni.$emit('topic_init_data')
                }).catch(err => {
                    uni.hideLoading();
                    alert(err.message)
                })

                if (this.isBack){
                    uni.navigateBack()
                }else {
                    uni.navigateTo({
                        url:'/pages/training/exam/index?topic_id=' + item.sid
                    })
                }

            }
        }

    }
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .cl-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 120rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 30rpx;
        margin-bottom: 30rpx;
        font-weight: bold;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        border: 1px solid #fff;
    }
    .cl-item.cur {
        background-color: #edf5fe;
        color: #065CDF;
    }
    .content image {
        width: 32rpx;
        height: 32rpx;
    }

</style>