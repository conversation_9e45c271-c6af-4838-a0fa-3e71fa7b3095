<template>
    <view class="content">
        <view class="category-list">
            <view class="cl-item" v-if="single">
                <text>单选题（{{ single }}题）</text>
                <view class="start-test" @click="start(2)">开始练习</view>
            </view>
            <view class="cl-item" v-if="judge">
                <text>判断题（{{ judge }}题）</text>
                <view class="start-test" @click="start(7)">开始练习</view>
            </view>
            <view class="cl-item" v-if="multi">
                <text>多选题（{{ multi }}题）</text>
                <view class="start-test" @click="start(6)">开始练习</view>
            </view>
        </view>
    </view>
</template>

<script>
import {showDelayLoading, alert} from "../../../lib/utils";
import api from "../../../lib/api";

export default {
    data() {
        return {
            topicId: 0,
            judge: 0,
            multi: 0,
            single: 0,
        }
    },
    onLoad(query) {
        if (query.topic_id) {
            this.topicId = query.topic_id
        }
        if (query.judge) {
            this.judge = query.judge
        }
        if (query.multi) {
            this.multi = query.multi
        }
        if (query.single) {
            this.single = query.single
        }

    },
    methods: {
        start(type) {
            // if(this.isPurchased == false){
            //     uni.showModal({
            //         content:"请先购买该题库",
            //         showCancel: false
            //     })
            // }


            api.get("training/tests/current/" + this.topicId + "?type=" + type).then(res => {
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.test.sid
                })

            }).catch(err => {
                if (err.code == 404) {
                    this.createTest(type)
                } else {
                    uni.hideLoading();
                    alert(err.message)
                }
            })
        },
        createTest(type) {
            const hideLoading = showDelayLoading("请稍后", 200)

            api.post("training/" + this.topicId + "/tests", {type: type}).then(res => {
                hideLoading()
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.sid
                })
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

.cl-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 120rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 0 20rpx 0 30rpx;
    margin-bottom: 30rpx;
    font-weight: bold;
}

.content image {
    width: 32rpx;
    height: 32rpx;
}

.start-test {
    background-color: #390ABC;
    border-radius: 68rpx;
    height: 68rpx;
    line-height: 68rpx;
    color: #fff;
    font-size: 28rpx;
    padding: 0 26rpx;
}
</style>