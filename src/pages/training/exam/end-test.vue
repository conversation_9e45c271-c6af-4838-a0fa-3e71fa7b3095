<template>
    <view class="content">
        <view class="paper-report-box">
            <view class="ppb-tit">生成时间：
                <uni-dateformat :date="test.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat>
                用时：{{ differenceText }}
            </view>
            <view class="ppb-data">
                <view class="ppbd-item">
                    <view class="ppbdi-num">{{ test.score }}</view>
                    <view class="ppbdi-txt">得分</view>
                </view>
                <view class="ppbd-item">
                    <view class="ppbdi-num">{{ right }}%</view>
                    <view class="ppbdi-txt">正确率</view>
                </view>
            </view>
            <view class="ppb-pane-box">
                <view class="ppbpb-item">
                    <view class="ppbpbi-num">{{ test?.subject_completed_count }}
                        <text>道</text>
                    </view>
                    <view class="ppbpbi-tit">总题数</view>
                </view>
                <view class="ppbpb-item">
                    <view class="ppbpbi-num">{{ test?.subject_correct_count }}
                        <text>道</text>
                    </view>
                    <view class="ppbpbi-tit">答对</view>
                </view>
                <view class="ppbpb-item">
                    <view class="ppbpbi-num">{{ test?.subject_error_count }}
                        <text>道</text>
                    </view>
                    <view class="ppbpbi-tit">答错</view>
                </view>
                <view class="ppbpb-item">
                    <view class="ppbpbi-num">{{ test?.subject_completed_not_count }}
                        <text>道</text>
                    </view>
                    <view class="ppbpbi-tit">未答</view>
                </view>
            </view>

            <view class="quick-study-box" v-if="courseCategoryId || courseContentId" @click="goVideo">
                <view class="quick-study">快速提升通过率</view>
            </view>

        </view>

        <view class="paper-detail-box">
            <view class="pdb-tit">答题卡</view>
            <view class="pdb-info">
                <view class="pdbi-item">
                    <text class="right"></text>
                    答对
                </view>
                <view class="pdbi-item">
                    <text class="wrong"></text>
                    答错
                </view>
                <view class="pdbi-item">
                    <text class="no"></text>
                    未答
                </view>
            </view>
            <view class="pdb-tit">单项选择题</view>
            <view class="pdb-link">
                <template v-for="(item, index) in testList" :key="index" >
                    <template v-if="item.type != 2" >
                        <view class="pbdl-item no" @click="testDetails(item)" v-if="item.test_option.option_id == 0">{{ index + 1 }}</view>
                        <view class="pbdl-item right" @click="testDetails(item)" v-else-if="item.test_option.correct == 1">{{ index + 1 }}</view>
                        <view class="pbdl-item wrong" @click="testDetails(item)" v-else>{{ index + 1 }}</view>
                    </template>
                </template>
            </view>
            <template v-if="mcCount">
                <view class="pdb-tit">多项选择题</view>
                <view class="pdb-link">
                    <template v-for="(item, index) in testList" :key="index" >
                        <template v-if="item.type == 2">
                            <view class="pbdl-item no" @click="testDetails(item)" v-if="item.test_option.option_id == 0">{{ index + 1 }}</view>
                            <view class="pbdl-item right" @click="testDetails(item)" v-else-if="item.test_option.correct == 1">{{ index + 1 }}</view>
                            <view class="pbdl-item wrong" @click="testDetails(item)" v-else>{{ index + 1 }}</view>
                        </template>
                    </template>
                </view>
            </template>
        </view>
    </view>
</template>

<script>
import api from "../../../lib/api";
import {alert, getDetailUrl, showToast} from "../../../lib/utils";

export default {
    data() {
        return {
            testId: '',
            test: {},
            testList: [],
            differenceText: '',
            right: '',
            courseCategoryId:'',
            courseContentId:'',
        }
    },
    onLoad(query) {
        if (query.test_id) {
            this.testId = query.test_id
            this.initData()
        }
    },
    computed: {
        mcCount() {
            let list = this.testList.filter(item => { item.type == 2})
            return list.length
        }
    },
    methods: {
        initData() {
            api.get("training/tests/" + this.testId + "/results").then(res => {
                this.test = res.test
                this.differenceText = res.difference_text
                this.courseCategoryId = res.course_category_id
                this.courseContentId = res.course_content_id
                this.testList = res.test_list;
                this.right = res.right
            }).catch(err => {
                alert(err.message)
            })
        },
        goVideo() {
            if (this.courseCategoryId){
                uni.navigateTo({
                    url: "/pages/training/video/list?cateId="+ this.courseCategoryId
                })
            }else {
                uni.navigateTo({
                    url:"/pages/training/video/detail?sid=" + this.courseContentId
                })
            }
        },
        testDetails(data) {
            uni.navigateTo({
                url: "/pages/training/exam/test?sid="+ this.testId + "&subjects_id=" + data.subjectSid
            })
        }
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

.paper-report-box {
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.ppb-tit {
    padding: 30rpx;
    text-align: center;
    font-size: 24rpx;
}

.ppb-data {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 30rpx 60rpx;
}

.ppbd-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.ppbdi-num {
    font-size: 48rpx;
    font-weight: bold;
}

.ppbdi-txt {
    font-size: 28rpx;
    color: #999;
    padding-top: 20rpx;
}

.ppb-pane-box {
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #F3F3FF;
}

.ppbpb-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0;
}

.ppbpbi-num {
    font-size: 36rpx;
    font-weight: bold;
}

.ppbpbi-num text {
    font-size: 24rpx;
    color: #999;
    font-weight: normal;
    padding-left: 10rpx;
}

.ppbpbi-tit {
    padding-top: 20rpx;
    font-size: 28rpx;
}

.paper-detail-box {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.pdb-tit {
    padding-bottom: 20rpx;
}

.pdb-info {
    display: flex;
    align-items: center;
    padding-bottom: 40rpx;
}

.pdbi-item {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #999;
    margin-right: 60rpx;
}

.pdbi-item text {
    display: inline-block;
    width: 30rpx;
    height: 30rpx;
    border-radius: 100%;
    background-color: #f3f3f3;
    margin-right: 10rpx;
}

.pdbi-item text.right {
    background-color: rgb(111, 196, 173);
}

.pdbi-item text.wrong {
    background-color: rgb(218, 100, 96);
}

.pdb-link {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding-bottom: 20rpx;
}

.pdb-link:last-child {
    padding-bottom: 0;
}

.pbdl-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80rpx;
    height: 80rpx;
    border-radius: 100%;
    background-color: #f3f3f3;
    margin-right: 24rpx;
    margin-bottom: 24rpx;
}

.pbdl-item.right {
    background-color: rgb(111, 196, 173);
    color: #fff;
}

.pbdl-item.wrong {
    background-color: rgb(218, 100, 96);
    color: #fff;
}

.quick-study-box {
    padding: 30rpx;
    border-top: 1px solid #F3F3FF;
}

.quick-study {
    background-color: #090ABC;
    color: #fff;
    height: 90rpx;
    border-radius: 90rpx;
    font-weight: bold;
    margin: 0 auto;
    text-align: center;
    line-height: 90rpx;
    width: 500rpx;
}
</style>