<template>
    <view class="content">

        <view class="hgb-vlbs-top">
            <view class="hgb-vlb-search">
                <navigator url="/pages/search/class" class="hgb-vlbs-main">
                    <image src="../../../images/icon/search-line.png"></image>
                    <text>搜索课程</text>
                </navigator>
            </view>
            <view class="vlb-tab">
                <scroll-view class="sub-sview-list navScroll" scroll-x="true">
                    <view class="sub-sview-item" :class="{cur:this.cateId === item.sid}" v-for="item in categories"
                          :key="item.sid" @click="chooseCategory(item.sid)">{{ item.name }}
                    </view>
                </scroll-view>
            </view>
        </view>
        <course :list="list" :list-status="listStatus" :list-loaded="listLoaded" :list-loading="listLoading"></course>
    </view>
</template>

<script>
import {getDetailUrl, showDelayLoading, alert} from "@/lib/utils";
import api from "@/lib/api";
import Course from "@/components/course.vue";

export default {
    components: {Course},
    onLoad(e) {
        if (e.cateId) {
            this.cateId = e.cateId
        }
        this.getList(true)
    },
    onReachBottom() {
        this.getList()
    },
    data() {
        return {
            cateId: "course",
            categories: [],
            list: [],
            nextCursor: "",
            listRows: 20,
            listLoaded: false,
            listLoading: false,
            listEnd: false,
        }
    },
    computed: {
        listStatus() {
            if (this.listLoading) {
                return "loading";
            } else if (this.listEnd) {
                return "noMore";
            } else {
                return "more";
            }
        }
    },
    methods: {
        getList(refresh) {
            if (this.listLoading || this.listEnd) {
                return;
            }

            this.listLoading = true;
            const hideLoading = refresh ? showDelayLoading('加载中', 200) : null

            api.get("cms/" + this.cateId + "/contents", {list_rows: this.listRows, next_cursor: this.nextCursor}).then(res => {
                if (this.categories.length === 0) {
                    const categories = res.category
                    if (categories.length > 0) {
                        categories.unshift({
                            "name": "全部",
                            "sid": "course",
                            "logo_src": ""
                        });
                    }
                    this.categories = categories
                }
                this.nextCursor = res.next_cursor
                if (refresh) {
                    this.list = res.data
                } else {
                    this.list.push(...res.data)
                }
                this.listLoaded = true;
                if (res.next_cursor === '') {
                    this.listEnd = true
                }
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                this.listLoading = false
                if (hideLoading) {
                    hideLoading()
                }
            })
        },
        chooseCategory(cateId) {
            this.cateId = cateId
            this.list = []
            this.listEnd = false
            this.nextCursor = ""
            this.getList(true)
        },
        goCourseDetail(item) {
            let url = getDetailUrl('course', item.sid)
            if (url === '') {
                alert("未获取到详情地址")
                return
            }
            uni.navigateTo({
                url: url
            })
        }
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}
.hgb-vlbs-top {
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    background-color: #F3F3FF;
}
.hgb-vlb-search {
    padding: 10rpx 30rpx;
}
.hgb-vlbs-main {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 12rpx;
    height: 80rpx;
}
.hgb-vlbs-main image {
    width: 32rpx;
    height: 32rpx;
    margin-top: 4rpx;
}
.hgb-vlbs-main text {
    color: #999;
    font-size: 28rpx;
    padding-left: 10rpx;
}
.vlb-tab {
    display: flex;
    align-items: center;
    background-color: #F3F3FF;
}

.vlb-pane {
    padding: 30rpx;
}

.vlbt-item {
    display: flex;
    align-items: center;
    padding: 30rpx 30rpx 40rpx;
    color: #999;
    font-size: 32rpx;
}

.vlbt-item.cur {
    position: relative;
    font-weight: bold;
    color: #333;
}

.vlbt-item.cur::after {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 20rpx;
    width: 20rpx;
    height: 8rpx;
    border-radius: 8rpx;
    background-color: #390abc;
}

.video-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.video-item image {
    width: 200rpx;
    height: 200rpx;
    border-radius: 12rpx;
}

.vi-info {
    flex: 1;
    padding-left: 20rpx;
}

.vii-tit {
    height: 80rpx;
    line-height: 40rpx;
    font-weight: bold;
    font-size: 28rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.vii-teacher {
    padding: 10rpx 0;
    font-size: 28rpx;
}

.vii-foot {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 24rpx;
    padding-top: 8rpx;
}

.viif-l {
    color: #999;
}

.viif-r {
    color: red;
    font-weight: bold;
}


/* 分类滚动 */
.sub-sview-list {
    white-space: nowrap;
    width: 100%;
    padding: 20rpx 0;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    display: flex;
    align-items: center;
    background-color: #F3F3FF;
}

.sub-sview-item {
    display: inline-block;
    height: 70rpx;
    line-height: 70rpx;
    padding: 0 30rpx;
    text-align: center;
    font-size: 36rpx;
    font-size: 24rpx;
    border-radius: 70rpx;
    margin-left: 30rpx;
    color: #666;
    background-color: #fff;
    border: 1px solid #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.sub-sview-item.cur {
    background-color: #390ABC;
    color: #fff;
    border: 1px solid #390ABC;
}
</style>