<template>
    <view class="content">
        <view id="ndComment1" class="nd-cnt-box">
            <uv-skeletons :loading="loading" :skeleton="skeleton">
                <view class="acli-top">
                    <view class="aclit-l">
                        <image :src="question.user?.avatar" mode="aspectFill"></image>
                    </view>
                    <view class="aclit-r">
                        <view class="aclitr-tit">{{question.user?.nickname}}</view>
                        <view class="aclitr-time"><uni-dateformat :threshold="[0,86400000]" :date="question.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
                    </view>
                </view>
                <view class="acli-main">
                    <view class="aclic-txt">
                        <mp-html :content="tagContent(question)?.replace(/\n/g, '<br/>')"></mp-html>
                    </view>
                    <view class="aclic-img">
                        <image :src="item" @click="previewImage(question.image_urls, item, sid)" v-for="(item,index) in question.image_urls" :key="index" mode="aspectFill"></image>
                    </view>
                </view>
            </uv-skeletons>

        </view>
        <view id="ndComment2" class="nd-comment">
            <view class="ndc-tit" >最新评论<text >{{ question.answer_count }}</text></view>

            <uv-skeletons :loading="loading" :skeleton="skeletonList">
                <view class="ndc-list">
                    <view class="ndcl-item" v-for="(item, index) in answersList"  :key="index">
                        <view class="ndcli-top">
                            <view class="ndclit-l">
                                <image :src="item.user.avatar" mode="aspectFill"></image>
                            </view>
                            <view class="ndclit-r">
                                <view class="ndclitr-tit">{{item.user.nickname}}</view>
                                <view class="ndclitr-time"><uni-dateformat :threshold="[0,86400000]" :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
                            </view>
                        </view>
                        <view class="ndcl-txt">{{item.content}}</view>
                        <view class="ndcl-foot">
                            <view class="ndclf-item" @click="like(item)">
                                <image v-if="item.is_like" src="../../images/icon/thumb-up-fill.png"></image>
                                <image v-else src="../../images/icon/thumb-up-line.png"></image>
                                <text v-if="item.like_count">{{ item.like_count }}</text>
                                <text v-else>赞</text>
                            </view>
                            <view class="ndclf-item" @click="dislike(item)">
                                <image v-if="item.dislike" src="../../images/icon/thumb-down-fill.png"></image>
                                <image v-else src="../../images/icon/thumb-down-line.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
            </uv-skeletons>

        </view>
        <view id="ndComment3" class="no-data-nomal-box" v-if="!answersList.length && !loading">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无评论</text>
        </view>
        <!-- 评论弹窗 -->
        <uni-popup ref="popup" :safe-area="false" type="bottom" border-radius="10px 10px 0 0">
            <view class="n-comment-popbox">
                <view class="ncpb-tit">
                    <view class="ncpbt-l" @click="this.$refs.popup.close()">取消</view>
                    <view class="ncpbt-txt">评论</view>
                    <view class="ncpbt-r" @click="postAnswer()">发表</view>
                </view>
                <view class="ncpb-input-warp">
                    <view class="ncpb-input">
                        <textarea v-model="content"
                                  :always-embed="true"
                                  :adjust-position="true"
                                  :cursor-spacing="30"
                                  @confirm="postAnswer()"
                                  name="" id="" cols="30" rows="10"  placeholder="请输入内容"></textarea>
                    </view>
                </view>
            </view>
        </uni-popup>

        <view class="nd-footer-pane">
            <view class="ndfp-btn" @click="openContent">说几句吧？</view>
            <view class="ndfp-r">
                <button open-type="share"  class="ndfp-item">
                    <image src="../../images/icon/share-forward-2-line.png"></image>
                    <text>分享</text>
                </button>
                <view class="ndfp-item" @click="comment()">
                    <image src="../../images/icon/chat-4-line.png"></image>
                    <text v-if="question.answer_count">{{question.answer_count}}</text>
                    <text v-else>评论</text>
                </view>
                <view class="ndfp-item" @click="attitude">
                    <image v-if="question.is_like" src="../../images/icon/thumb-up-fill.png"></image>
                    <image v-else src="../../images/icon/thumb-up-line.png"></image>
                    <text v-if="question.attitudes_count">{{ question.attitudes_count }}</text>
                    <text v-else>赞</text>
                </view>
            </view>
        </view>

        <view class="empty-block"></view>


    </view>
</template>

<script>
import api from "../../lib/api";
import {showDelayLoading, alert} from "../../lib/utils";
import mpHtml from 'mp-html/dist/uni-app/components/mp-html/mp-html'
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {loginRequired} from "@/lib/login";
import {getAppName} from "@/lib/context";

export default {
    data() {
        return {
            sid: '',
            content: '',
            question: {},
            bst: {},
            cursor: '',
            scrollId: "",
            defaultAvatar: '',
            answersList: [],
            loading: true,
            loadingPost: false,
            loadingLike: false,
            loadingList: false,
            newList: [],
            skeleton: [
                {
                    type: 'flex',
                    num: 1,
                    style: 'backgroundColor: #fff;borderRadius: 12rpx;marginBottom:30rpx;',
                    children: [
                        {
                            type: 'avatar',
                            num: 1,
                            style: 'marginRight: 10rpx;marginLeft: 30rpx;marginTop: 30rpx;'
                        }, {
                            type: 'line',
                            num: 2,
                            gap: '30rpx',
                            style: ['width: 200rpx;marginTop:30rpx;', 'width:300rpx;']
                        },
                    ]
                },
                {
                    type: 'flex',
                    num: 1,
                    style: 'backgroundColor: #fff;borderRadius: 12rpx   ;',
                    children: [
                        {
                            type: 'line',
                            num: 2,
                            style: ['marginRight: 30rpx;marginLeft: 30rpx;','marginRight: 30rpx;marginLeft: 30rpx;marginBottom:30rpx;',]
                        },
                    ]
                },
            ],
            skeletonList: [
                {
                    type: 'flex',
                    num: 3,
                    style: 'backgroundColor: #fff;borderRadius: 12rpx;marginBottom:30rpx;',
                    children: [
                        {
                            type: 'avatar',
                            num: 1,
                            style: 'marginRight: 10rpx;marginLeft: 10rpx;marginTop: 30rpx;marginBottom:30rpx;'
                        }, {
                            type: 'line',
                            num: 2,
                            gap: '30rpx',
                            style: ['width: 200rpx;marginTop:30rpx;', 'width:300rpx;']
                        },
                    ]
                },
            ],
            appName: getAppName()
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    onLoad(query) {
        if (query.sid) {
            this.sid = query.sid
            this.initData()
            this.getAnswersList()
        }
        uni.$on('qa_index_answers', () => {
            this.cursor = '';
            this.answersList = [];
            this.getAnswersList()
        })
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.cursor) {
            this.getAnswersList();
        }
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.cursor = '';
        this.answersList = [];
        this.initData()
        this.getAnswersList()

    },
    onUnload() {
        uni.$off("qa_index_answers");
    },
    components: {
        mpHtml
    },
    methods: {
        initData() {
            const hideLoading = showDelayLoading("加载中", 200)

            api.get('qa/questions/' + this.sid).then(res => {
                this.question = res

                hideLoading()
            }).catch(err => {
                alert(err.message)
            }).finally(() => {
                uni.stopPullDownRefresh();

            })
        },
        getAnswersList() {
            let data = {
                cursor: this.cursor
            }
            if (this.loadingList) {
                return
            }
            this.loadingList = true
            api.get('qa/questions/' + this.sid + '/answers', data).then(res => {

                this.cursor = res.next_cursor;
                this.answersList.push(...res.data);

            }).catch(err => alert(err.message)).finally(() => {
                this.loadingList = false;
                uni.stopPullDownRefresh();
                this.loading = false
            })
        },
        previewImage(data, src, sid) {
            console.log(data)
            uni.previewImage({
                urls: data,
                current: src,
                success: () => {

                },
                fail: (e) => {
                    console.log(e);
                }
            })
            api.request('qa/questions/' + sid, {
                method: 'HEAD',
            }).catch(err => alert(err.message))
        },
        like(data) {
            loginRequired().then(() => {
                let postData = {
                    attitude: 1
                }
                if (this.loadingLike) {
                    return
                }
                this.loadingLike = true
                if (!data.is_like) {
                    api.post("attitude/answer/" + data.sid, postData).then(res => {
                        data.is_like = true
                        data.dislike = false
                        data.like_count++
                        uni.showToast({
                            title: "点赞成功",
                            icon: "none"
                        })
                    }).catch(err => alert(err.message)).finally(() => this.loadingLike = false)
                } else {
                    api.delete("attitude/answer/" + data.sid + '?attitude=1').then(res => {
                        data.is_like = false
                        data.like_count--
                    }).catch(err => alert(err.message)).finally(() => this.loadingLike = false)
                }
            })
        },
        dislike(data) {
            loginRequired().then(() => {
                let postData = {
                    attitude: 2
                }
                if (this.loadingLike) {
                    return
                }
                this.loadingLike = true
                if (!data.dislike) {
                    api.post("attitude/answer/" + data.sid, postData).then(res => {
                        if (data.is_like) {
                            data.like_count--
                        }
                        data.dislike = true
                        data.is_like = false
                    }).catch(err => alert(err.message)).finally(() => this.loadingLike = false)
                } else {
                    api.delete("attitude/answer/" + data.sid + '?attitude=2').then(res => {
                        data.dislike = false
                    }).catch(err => alert(err.message)).finally(() => this.loadingLike = false)
                }
            })
        },
        comment(){
            uni.createSelectorQuery().select('#ndComment1').boundingClientRect(function(rect) {
                uni.pageScrollTo({
                    scrollTop: rect.height, // 滚动到元素顶部位置
                    duration: 300 // 滚动动画时长
                });

            }).exec();
        },
        goPage(url) {
            loginRequired().then(() => {
                uni.navigateTo({
                    url
                })
            })
        },
        attitude(){
            loginRequired().then(() =>{
                let postData = {
                    attitude: 1
                }

                if (this.loadingLike) {
                    return
                }
                this.loadingLike = true
                if (!this.question.is_like) {
                    api.post("attitude/question/" + this.sid, postData).then(res => {
                        this.question.attitudes_count++
                        this.question.is_like = true
                    }).catch(err => alert(err.message)).finally(() =>{
                        this.loadingLike = false
                    })
                }else {
                    api.delete("attitude/question/" + this.sid + '?attitude=1').then(res => {
                        this.question.is_like = false
                        this.question.attitudes_count--
                    }).catch(err => alert(err.message)).finally(() =>{
                        this.loadingLike = false
                    })
                }
            })
        },
        tagContent(data){
            console.log(data)
            if (data.tags?.length){
                let content = null
                for (let index in data.tags){
                    let searchTerm = '#'+ data.tags[index].name;
                    let replacement = "<a href='/pages/ask/topic-detail?id="+data.tags[index].id+"'>#"+data.tags[index].name+"</a>";
                    // 使用 RegExp 构造函数创建一个正则表达式，其中 'g' 表示全局匹配
                    let regex = new RegExp(searchTerm, 'g');

                    if (!content){
                        content = data.content;
                    }
                    let newStr = content.replace(regex, replacement);
                    content = newStr
                }
                return content;
            }else {
                return data.content;
            }
        },
        postAnswer(){
            loginRequired().then(() =>{
                if (!this.content){
                    alert('评论不能为空')
                    return
                }
                if (  this.content.length > 1000){
                    alert('评论字数限制 1000 个汉字')
                    return
                }
                let data = {
                    content: this.content,
                    anonymous:  0
                }
                const hideLoading = showDelayLoading("提交中", 200)
                if (this.loadingPost){
                    return
                }
                this.loadingPost = true
                api.post("qa/questions/"+ this.sid +"/answers", data).then(res => {
                    uni.showToast({
                        title: "评论成功",
                        icon: "success"
                    })
                    this.answersList.unshift(res)
                    this.question.answer_count ++
                    setTimeout(() =>{
                        uni.$emit('qa_index_change')
                    }, 1000)

                    hideLoading()
                }).catch(err => {
                    hideLoading()
                    alert(err.message)
                }).finally(() => {
                    this.loadingPost = false
                    this.$refs.popup.close()
                })
            })
        },
        openContent(){
            loginRequired().then(() =>{
                this.$refs.popup.open()
            })
        }
    },
    onShareAppMessage() {
        return {
            title: this.appName,
            path: '/pages/ask/n-detail?sid=' + this.sid
        };
    },
}
</script>

<style>
    page {
        background-color: #fff;
    }
    .content{
        height: 100vh;
        display: flex;
        flex-direction: column;
    }
    .acli-top {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 0;
    }
    .aclit-l image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
    }
    .aclit-r {
        padding-left: 20rpx;
    }
    .aclitr-tit {
        font-size: 28rpx;
        font-weight: bold;
    }
    .aclitr-time {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .aclic-txt {
        padding: 30rpx;
        white-space:pre-line;
        font-size: 32rpx;
        line-height: 1.6;
    }
    .aclic-txt navigator {
        color: #390ABC;
        display: inline-block;
    }

    .aclic-img {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 0 0 30rpx 30rpx;
    }
    .aclic-img image {
        width: 209rpx;
        height: 209rpx;
        margin-right: 1px;
        margin-bottom: 1px;
        border-radius: 12rpx;
    }

    .ndc-tit {
        display: flex;
    }

    .ndc-tit {
        position: relative;
        height: 100rpx;
        line-height: 100rpx;
        font-weight: bold;
        margin-bottom: 10rpx;
    }

    .ndc-tit text {
        display: inline-block;
        position: relative;
        padding-left: 10rpx;
        font-size: 24rpx;
        color: #999;
        font-weight: normal;
    }

    .ndc-tit::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 16rpx;
        width: 170rpx;
        height: 10rpx;
        background-color: #390ABC;
    }


    .nd-comment {
        flex: 1;
        padding: 0 30rpx 30rpx;
    }
    .ndc-list {
        padding-top: 30rpx;
    }
    .ndcl-item {
        margin-bottom: 30rpx;
        border-bottom: 1px solid #E7E7E7;
    }
    .ndcl-item:last-child {
        border-bottom: none;
    }
    .ndcli-top {
        display: flex;
        align-items: center;
    }
    .ndclit-l image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
    }
    .ndclit-r {
        padding-left: 20rpx;
    }
    .ndclitr-tit {
        font-size: 28rpx;
        font-weight: bold;
    }
    .ndclitr-time {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .ndcl-txt {
        padding: 30rpx 0 0;
        white-space:pre-line;
        font-size: 32rpx;
        line-height: 1.6;
    }
    .ndcl-txt navigator {
        color: #390ABC;
        display: inline-block;
    }
    .ndcl-foot {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
    .ndclf-item {
        display: flex;
        align-items: center;
        height: 100rpx;
        margin-left: 40rpx;
    }
    .ndclf-item image {
        width: 32rpx;
        height: 32rpx;
    }
    .ndclf-item text {
        font-size: 24rpx;
        padding-left: 10rpx;
    }


    /* 底部按钮 */
    .nd-footer-pane {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 20rpx 20rpx env(safe-area-inset-bottom);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #fff;
        border-top: 1px solid #e7e7e7;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        z-index: 2;
    }
    .empty-block {
        height: 130rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .ndfp-btn {
        flex: 1;
        text-align: center;
        height: 88rpx;
        border-radius: 88rpx;
        background-color: #f3f3ff;
        line-height: 88rpx;
        margin-bottom: 20rpx;
        font-size: 28rpx;
    }
    .ndfp-r {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
    }
    .ndfp-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 110rpx;
    }
    .ndfp-item image {
        width: 38rpx;
        height: 38rpx;
        vertical-align: top;
    }
    .ndfp-item text {
        font-size: 24rpx;
        padding-top: 8rpx;
    }


    /* 弹出框样式 */
    .n-comment-popbox {
        background-color: #fff;
        border-radius: 20rpx 20rpx 0 0;
    }
    .ncpb-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .ncpbt-l,.ncpbt-r {
        height: 100rpx;
        line-height: 100rpx;
        color: #390ABC;
        font-size: 28rpx;
        padding: 0 50rpx;
    }
    .ncpb-input-warp {
        padding: 0 30rpx 30rpx;
    }
    .ncpb-input {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f3ff;
        border-radius: 20rpx;
        margin-bottom: env(safe-area-inset-bottom);
    }
    .ncpb-input textarea {
        flex: 1;
        padding: 30rpx;
        min-height: 100rpx;
        line-height: 1.6;
    }
</style>