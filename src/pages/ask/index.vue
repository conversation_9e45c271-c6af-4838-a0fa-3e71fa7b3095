<template>
    <view class="content">
        <navigator url="/pages/search/qa" class="qa-search">
            <image src="../../images/icon/search-line.png"></image>
            <text>检索问题</text>
        </navigator>
        <view class="qa-recommend" v-if="meQuestionList.length">
            <swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
                :duration="duration" indicator-color="#ccc" indicator-active-color="#090ABC">
                <swiper-item v-for="(item, index) in meQuestionList" :key="index">
                    <navigator :url="'/pages/ask/detail?sid=' +item.sid" class="swiper-item">
                        <view class="qr-tit">问：{{ item.title }}</view>
                        <view class="qr-p">
                            <text v-if="item.answer">
                                答：{{item.answer.content}}
                            </text>
                        </view>
                    </navigator>
                </swiper-item>
            </swiper>
        </view>
        <uv-skeletons  v-if="loading" :skeleton="skeleton"></uv-skeletons>


        <view class="qa-list-box">
            <view class="qalb-tit">等你来答</view>

            <view class="qalb-list">

                <navigator :url="'/pages/ask/detail?sid=' +item.sid" class="qalbl-item" v-for="(item, index) in question" :key="index">
                    <view class="qr-tit">问：{{item.title}}</view>
                    <view class="qr-foot">
                        <view class="qrf-l">来自
                            <template v-if="item.anonymous == 1"><text class="qrfl-txt">匿名</text></template>
                            <template v-else><text class="qrfl-txt">{{ item.user.nickname}}</text></template>
                            的提问&nbsp;&nbsp;·&nbsp;&nbsp;<uni-dateformat :date="item.updated_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></view>
                        <view class="qrf-r">{{ item.answer_count }}回答</view>
                    </view>
                </navigator>
                <uni-load-more v-if="question.length"  :status="statusLoad" />
            </view>
        </view>
        <uv-skeletons  v-if="loading && !question.length" :skeleton="skeletonList"></uv-skeletons>


        <view class="qapb-eb"></view>
        <view class="qa-part-bottom">
            <view  @click="goPage('/pages/ask/send')" class="qapb-btn">
                <image src="../../images/icon/question-line-01.png"></image>
                <text>提问</text>
            </view>
            <view @click="goPage('/pages/me/qa')" class="qapb-txt-link p-rel">
                我的问答
                <text v-if="answerTip" class="nomal-badge"></text>
            </view>
        </view>
    </view>
</template>

<script>
    import api from "../../lib/api";
    import {showDelayLoading, alert} from "@/lib/utils";
    import {mapState} from "pinia";
    import {useUserStore} from "@/store/user";
    import {loginRequired} from "@/lib/login";

    export default {
        data() {
            return {
                background: ['color1', 'color2', 'color3'],
                indicatorDots: true,
                autoplay: true,
                interval: 5000,
                duration: 500,
                question:[],
                statusLoad: 'loading',
                meQuestionList:[],
                next_cursor:'',
                hideLoading:'',
                loading: false,
                answerTip: false,
                skeleton: [{
                    type: 'flex',
                    num: 1,
                    children: [ {
                        type: 'line',
                        num: 3,
                        gap: '30rpx',
                        //style: ['width: 200rpx;', null, 'width:400rpx;']
                    }]
                }],
                skeletonList: [{
                    type: 'flex',
                    num: 1,
                    children: [
                        {
                            type: 'avatar',
                            num: 1,
                            style: 'marginRight: 10rpx;'
                        },
                        {
                        type: 'line',
                        num: 2,
                        gap: '30rpx',
                        style: ['width: 200rpx;' , null]
                    }]
                }]
            }
        },
        computed: {
            ...mapState(useUserStore, ['user', 'loaded']),
        },
        onLoad(query) {
            this.meData()
            this.getQuestionList()
            uni.$on('qa_index_change',() =>{
                this.question = [];
                this.next_cursor = ''
                this.getQuestionList();

            })
            this.hideLoading = showDelayLoading("请稍后", 200)

        },
        onPullDownRefresh() {
            console.log("下拉刷新");
            this.question = [];
            this.next_cursor = ""
            this.getQuestionList()

        },
        onUnload() {
            // 页面关闭去除相关事件
            uni.$off("qa_index_change")
        },
        onReachBottom() {
            console.log("上拉加载");
            if (this.next_cursor ){
                this.getQuestionList();
            }
        },
        methods:{
            meData(){
                api.get("pages/qa").then(res => {
                    this.meQuestionList = res.question_list
                    this.answerTip = res.answer_tip
                })
            },
            getQuestionList(){
                let data = {
                    next_cursor: this.next_cursor
                }
                this.loading = true
                api.get("qa/questions", data).then(res => {
                    this.question.push(...res.data)
                    this.next_cursor = res.next_cursor
                    this.hideLoading()
                    if (!res.data.length ||  res.data.length < 19){
                        this.statusLoad = 'noMore'
                    }
                }).catch(err =>{
                    alert(err.message)
                    this.hideLoading()
                }).finally(() =>{
                    uni.stopPullDownRefresh();
                    this.loading = false;

                })
            },
            goPage(url){
                if (url == "/pages/me/qa"){
                    if (this.answerTip){
                        url = url + "?is_tip=1"
                    }
                    this.answerTip = false
                }
                loginRequired().then(() =>{
                    uni.navigateTo({
                        url
                    })
                })
            }
        }
    }
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .content {
        padding: 30rpx;
    }
    .qa-search {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        line-height: 88rpx;
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .qa-search image {
        width: 32rpx;
        height: 32rpx;
    }
    .qa-search text {
        padding-left: 20rpx;
        font-size: 28rpx;
        color: #999;
    }
    .swiper {
        height: 310rpx;
    }
    .swiper-item {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        padding: 30rpx;
    }
    .qr-tit {
        font-weight: bold;
        height: 100rpx;
        line-height: 50rpx;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    .qr-p {
        font-size: 28rpx;
        color: #999;
        padding-top: 16rpx;
        height: 80rpx;
        line-height: 40rpx;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }


    .qa-list-box {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .qalb-tit {
        padding: 20rpx 30rpx;
        border-bottom: 1px solid #F3F3FF;
        font-size: 28rpx;
        color: #999;
    }
    .qalbl-item {
        padding: 30rpx;
        border-bottom: 1px solid #F3F3FF;
    }
    .qalbl-item:last-child {
        border-bottom: 0;
    }
    .qr-foot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 24rpx;
        color: #999;
        padding-top: 20rpx;
    }
    .qrf-l {
        display: flex;
        align-items: center;
    }
    .qrfl-txt {
        display: inline-block;
        max-width: 120rpx;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        padding: 0 10rpx;
    }

    .qapb-eb {
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .qa-part-bottom {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 140rpx;
        padding-bottom: env(safe-area-inset-bottom);
        padding-left: 30rpx;
        padding-right: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        border-top: 1px solid #F3F3FF;
    }
    .qapb-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390ABC;
        color: #fff;
        height: 90rpx;
        line-height: 90rpx;
        border-radius: 12rpx;
    }
    .qapb-btn image {
        width: 32rpx;
        height: 32rpx;
        vertical-align: top;
        margin-top: 4rpx;
    }
    .qapb-btn text {
        padding-left: 10rpx;
        font-size: 28rpx;
    }
    .qapb-txt-link {
        color: #390ABC;
        padding: 0 60rpx;
        font-size: 28rpx;
        height: 90rpx;
        line-height: 90rpx
    }
</style>