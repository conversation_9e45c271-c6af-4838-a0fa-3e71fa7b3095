<template>
    <view class="content">
        <view class="topic-top-box">
            <view class="top-search-box">
                <view class="tsb-icon">
                    <image src="../../images/icon/search-line.png"></image>
                </view>
                <input class="uni-input" type="text" confirm-type="search" v-model="content" @confirm="search()" :focus="focus" placeholder="请输入关键词" @input="clearInput"/>
                <view class="search-clear-btn" v-if="content" @click="content = ''">
                    <view class="scb-icon">
                        <image src="../../images/icon/close-line-white.png"></image>
                    </view>
                </view>
                <button class="esb-btn" @click="search()" :disabled="disabled">搜索</button>
            </view>
        </view>

        <view class="topic-list-box">
            <view class="tlb-item" @click="tagChoice(item)" v-for="(item,index) in tags" :key="index">
                <view class="tlbi-l">
                    <image src="../../images/icon/chat-thread-fill.png"></image>
                    <text>{{item.name}}</text>
                </view>
<!--                <view class="tlbi-r">1.9万浏览</view>-->
            </view>
        </view>
        <view class="no-data-nomal-box" v-if="!tags.length && !loading">
            <view class="ndnb-icon">
                <image src="../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无数据</text>
        </view>
    </view>
</template>

<script>
    import api from "@/lib/api";
    import {alert} from "@/lib/utils";

    export default {
        data(){
            return{
                type: 1,
                cursor:'',
                content:'',
                searchContent:'',
                tags: [],
                loading: true,
                showClearIcon: false,
            }
        },
        onLoad(e) {
            if (e.type){
                this.type = e.type
            }
            this.initData()
        },
        onReachBottom() {
            console.log("上拉加载");
            if (this.cursor ){
                this.initData();
            }
        },
        methods: {
            initData(){
                let data = {
                    cursor: this.cursor,
                    q: this.searchContent,
                }
                this.loading = true
                api.get('qa/tags',data).then(res => {
                    this.tags.push(...res.data)
                    this.cursor = res.next_cursor
                }).catch(err => alert(err.message)).finally(() =>{
                    this.loading = false
                })
            },
            search(){
                if (!this.content && !this.searchContent){
                    uni.showToast({
                        title:'请输入搜索内容'
                    })
                    return
                }
                this.cursor = '';
                this.tags = [];
                this.searchContent = this.content;
                this.initData()
            },
            tagChoice(data){
                if (this.type == 1){
                    uni.$emit("ask_tag_select", data)
                    uni.navigateBack()
                }else {
                    uni.navigateTo({
                        url:'/pages/ask/topic-detail?id='+ data.id
                    })
                }
            }
        },
    }
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .topic-top-box {
        position: sticky;
        top: 0;
    }
    .top-search-box {
        display: flex;
        align-items: center;
        height: 100rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 10rpx 0 20rpx;
        margin: 30rpx 30rpx 0;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .tsb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
    }
    .tsb-icon image {
        width: 32rpx;
        height: 32rpx;
    }
    .top-search-box .uni-input {
        flex: 1;
        font-size: 28rpx;
        padding-left: 20rpx;
        height: 100rpx;
        line-height: 100rpx;
    }
    .esb-btn {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
        color: #390ABC;
    }
    .clear {
        height: 100rpx;
        line-height: 100rpx;
        padding: 0 30rpx;
        font-size: 28rpx;
    }
    /* 搜索清楚按钮 */
    .search-clear-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100rpx;
        padding: 0 20rpx;
    }
    .scb-icon {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 36rpx;
        height: 36rpx;
        border-radius: 100%;
        background-color: rgba(0, 0, 0, .3);
        margin-right: 10rpx;
    }
    .search-clear-btn image {
        width: 28rpx;
        height: 28rpx;
    }

    .topic-list-box {
        padding: 30rpx;
    }
    .tlb-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 30rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .tlbi-l {
        display: flex;
        align-items: center;
    }
    .tlbi-l image {
        width: 38rpx;
        height: 38rpx;
    }
    .tlbi-l text {
        padding-left: 20rpx;
        color: #FF8200;
        font-weight: bold;
    }
    .tlbi-r {
        font-size: 24rpx;
        color: #999;
    }
</style>