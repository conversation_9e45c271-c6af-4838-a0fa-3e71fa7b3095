<template>
    <view class="content">

            <view class="topic-intro">
                <uv-skeletons :loading="loadingInit"  :skeleton="skeleton">
                    <view class="ti-tit">#{{tag?.name}}</view>
                    <view class="ti-txt"><text>{{tag?.total}}条动态</text> · <text>{{tag?.views}}次浏览</text></view>
                </uv-skeletons>
            </view>

        <view class="ti-new-thread">最新</view>
        <questions-item :loading="loadingInit" :questions="questions"></questions-item>
        <uni-load-more  v-if="questions.length" :status="statusLoad"></uni-load-more>

        <!-- 发表按钮 -->
        <view class="ti-send-btn" @click="goPost">
            <image src="../../images/icon/quill-pen-fill.png"></image>
            <!-- <text>发表</text> -->
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert, showDelayLoading} from "@/lib/utils";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {loginRequired} from "@/lib/login";
import QuestionsItem from "./questionsItem.vue";
import {getAppName} from "@/lib/context";

export default {
    components: {QuestionsItem},
    data(){
        return{
            tags: [],
            questions: [],
            tag: {},
            next_cursor: '',
            hideLoading: '',
            loading: true,
            loadingInit: true,
            answerTip: false,
            statusLoad: 'loading',
            tagId:'',
            skeleton:[
                {
                    type: 'flex',
                    children:[
                        {
                            type: 'custom',
                            style: 'width:120rpx;height:40rpx;'
                        }
                    ]
                },10,{
                    type: 'flex',
                    children:[
                        {
                            type: 'custom',
                            style: 'width:60rpx;height:20rpx;'
                        },{
                            type: 'custom',
                            style: 'width:100rpx;height:20rpx;marginLeft:30rpx;'
                        },
                    ]
                }
            ],
            appName: getAppName()
        }
    },
    onLoad(e) {
        if (e.id){
            this.tagId = e.id
        }
        this.hideLoading = showDelayLoading("请稍后", 200)

        this.getQuestionList()
        this.getTag()
        uni.$on('qa_index_change',() =>{
            this.questions = [];
            this.next_cursor = ''
            this.getQuestionList();

        })
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    onPullDownRefresh() {
        console.log("下拉刷新");
        this.question = [];
        this.next_cursor = ""
        this.getQuestionList()

    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("qa_index_change")
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.next_cursor ){
            this.getQuestionList();
        }
    },
    methods: {
        getQuestionList(){
            let data = {
                next_cursor: this.next_cursor,
                tag_id: this.tagId
            }
            this.loading = true
            api.get("qa/questions", data).then(res => {
                this.questions.push(...res.data)
                console.log(this.questions)
                this.next_cursor = res.next_cursor
                this.hideLoading()
                if (!res.data.length ||  res.data.length < 19){
                    this.statusLoad = 'noMore'
                }
            }).catch(err =>{
                alert(err.message)
                this.hideLoading()
            }).finally(() =>{
                uni.stopPullDownRefresh();
                this.loading = false;
                this.loadingInit = false;

            })
        },
        getTag(){
            api.get('qa/tags/'+ this.tagId).then(res => {
                this.tag = res
                uni.setNavigationBarTitle({
                    title: res.name
                })
            }).catch(err => alert(err.message))
        },
        attitude(data){
            loginRequired().then(() =>{
                let postData = {
                    attitude: 1
                }
                if (this.loading){
                    return
                }
                this.loading = true
                if (!data.is_like) {
                    api.post("attitude/question/" + data.sid, postData).then(res => {
                        data.attitudes_count++
                        data.is_like = true
                    }).catch(err => alert(err.message)).finally(() =>{
                        this.loading = false
                    })
                }else {
                    api.delete("attitude/question/" + data.sid + '?attitude=1').then(res => {
                        data.is_like = false
                        data.attitudes_count--
                    }).catch(err => alert(err.message)).finally(() =>{
                        this.loading = false
                    })
                }
            })
        },
        goPost(){
            loginRequired().then(() =>{
                uni.navigateTo({
                    url: "/pages/ask/post?tag_id=" + this.tagId + "&tag_name=" + this.tag.name
                })
            })
        }
    },
    onShareAppMessage(res) {
        return {
            title: this.appName,
            path: '/pages/ask/n-detail?sid=' + res.target.id
        };
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .content {
        padding-bottom: 200rpx;
    }
    .topic-intro {
        margin: 30rpx 30rpx 0;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ti-tit {
        font-size: 32rpx;
        font-weight: bold;
    }
    .ti-txt {
        font-size: 24rpx;
        color: #999;
        padding-top: 16rpx;
    }
    .ti-new-thread {
        position: sticky;
        top: 0;
        padding: 30rpx;
        background-color: #f3f3ff;
        z-index: 2;
    }
    /* 标题 */
    .dpb-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        padding: 0 20rpx 0 30rpx;
    }
    .dpbt-txt {
        font-size: 28rpx;
        font-weight: bold;
    }
    .dpbt-more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
    }
    .dpbt-more text {
        font-size: 24rpx;
        color: #999;
    }
    .dpbt-more image {
        width: 32rpx;
        height: 32rpx;
    }

    .sub-sview-list {
        white-space: nowrap;
        width: 100%;
        border-bottom: 1px solid #F3F3FF;
    }
    .sub-sview-item {
        display: inline-block;
        height: 70rpx;
        line-height: 70rpx;
        padding: 0 30rpx;
        text-align: center;
        font-size: 36rpx;
        font-size: 24rpx;
        background-color: #fef7ed;
        border: 1px solid #ff731c;
        color: #ff731c;
        border-radius: 70rpx;
        margin-left: 30rpx;
        margin-bottom: 30rpx;
    }
    .sub-sview-item.cur {
        background-color: #390ABC;
        color: #fff;
        border: 1px solid #390ABC;
    }

    /* 内容块 */
    .ask-cnt-list {
        margin: 0 30rpx;
    }
    .acl-item {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        margin-bottom: 30rpx;
    }
    .acli-top {
        display: flex;
        align-items: center;
        padding: 30rpx 30rpx 0;
    }
    .aclit-l image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 100%;
    }
    .aclit-r {
        padding-left: 20rpx;
    }
    .aclitr-tit {
        font-size: 28rpx;
        font-weight: bold;
    }
    .aclitr-time {
        font-size: 24rpx;
        color: #999;
        padding-top: 6rpx;
    }
    .aclic-txt {
        padding: 30rpx;
        white-space: pre-wrap;
        font-size: 28rpx;
        line-height: 1.6;
    }
    .aclic-txt navigator {
        color: #390ABC;
        display: inline-block;
    }

    .aclic-img {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 0 0 30rpx 30rpx;
    }
    .aclic-img image {
        width: 209rpx;
        height: 209rpx;
        margin-right: 1px;
        margin-bottom: 1px;
        border-radius: 12rpx;
    }

    .acli-foot {
        display: flex;
        align-items: center;
        border-top: 1px solid #f3f3ff;
    }
    .acli-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100rpx;
    }
    .acli-item image {
        width: 32rpx;
        height: 32rpx;
    }
    .acli-item text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }

    .ask-foot-box {
        position: sticky;
        bottom: 0;
        padding: 16rpx 30rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .afb-main {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .afb-btn {
        flex: 1;
        height: 90rpx;
        text-align: center;
        line-height: 90rpx;
        background-color: #390ABC;
        color: #fff;
        font-size: 28rpx;
        border-radius: 80rpx;
    }
    .afb-small-btn {
        width: 200rpx;
        margin-left: 30rpx;
        height: 90rpx;
        line-height: 90rpx;
        text-align: center;
        font-size: 28rpx;
        color: #390ABC;
    }


    .ti-send-btn {
        position: fixed;
        right: 30rpx;
        bottom: 80rpx;
        width: 100rpx;
        height: 100rpx;
        border-radius: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #EA712E;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        z-index: 2;
    }
    .ti-send-btn image {
        width: 48rpx;
        height: 48rpx;
    }
    .ti-send-btn text {
        font-size: 24rpx;
        color: #fff;
    }
</style>