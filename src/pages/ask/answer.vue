<template>
    <view class="content">
        <view class="question-detail">
            <view class="qd-tit">{{ question.title }}</view>
            <view class="qd-sub-d">{{ question.content }}</view>
        </view>
        <view class="ask-detail">
            <textarea v-model="content" space="ensp" :maxlength="-1" auto-height placeholder="写回答"/>
        </view>

        <view class="ask-section">
            <view class="asks-option">
                <text>匿名</text>
                <switch @change="isAnonymous" :checked="anonymous" color="#EA712A" style="transform:scale(0.7)"/>
            </view>
        </view>

        <view class="ask-send-btn" @click="postAnswer()">发布回答</view>
    </view>
</template>

<script>
import api from "../../lib/api";
import {showDelayLoading, alert} from "../../lib/utils";

export default {
    data() {
        return{
            title:'',
            sid: '',
            question: {},
            content: "",
            loading: false,
            anonymous:false,

        }
    },
    onLoad(query) {
        this.title = query.title;
        this.sid = query.sid;
        this.initData()
    },
    methods: {
        initData() {
            api.get('qa/questions/' + this.sid).then(res => {
                this.question = res.question
                this.bst = res.bst
            })
        },
        isAnonymous(e){
            this.anonymous = e.detail.value
        },
        postAnswer(){
            this.content = this.content.replace(/↵/g, "</br>");
            console.log(this.content)
            if (!this.content){
                alert('回答不能为空')
                return
            }
            if (  this.content.length > 1000){
                alert('回答字数限制 1000 个汉字')
                return
            }
            let data = {
                content: this.content,
                anonymous: this.anonymous ? 1 : 0
            }
            const hideLoading = showDelayLoading("提交中", 200)
            if (this.loading){
                return
            }
            this.loading = true
            api.post("qa/questions/"+ this.sid +"/answers", data).then(res => {
                uni.showToast({
                    title: "提交成功",
                    icon: "success"
                })
                setTimeout(() =>{
                    uni.$emit('qa_index_answers')
                    uni.$emit('qa_index_change')
                }, 1000)
                setTimeout(() => {
                    uni.navigateBack()
                }, 1500)
                hideLoading()
            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(() => {
                this.loading = false
            })
        }
    },
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .ask-title {
        padding: 20rpx 0;
        font-size: 24rpx;
        color: #999;
    }
    .ask-input {
        display: flex;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
    }
    .ask-input textarea {
        flex: 1;
        padding: 30rpx;
        min-height: 100rpx;
        font-size: 32rpx;
        line-height: 1.8;
    }
    .ask-detail {
        display: flex;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ask-detail textarea {
        flex: 1;
        padding: 30rpx;
        min-height: 160rpx;
        font-size: 32rpx;
        line-height: 1.6;
    }
    .ask-send-btn {
        height: 100rpx;
        background-color: #390ABC;
        color: #FFF;
        font-size: 32rpx;
        font-weight: bold;
        border-radius: 12rpx;
        text-align: center;
        line-height: 100rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ask-section {
        text-align: right;
        margin-bottom: 30rpx;
    }
    .asks-option {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
    .asks-option text {
        color: #999;
        font-size: 28rpx;
    }

    .question-detail {
        padding: 30rpx;
        border-left: 6rpx solid #999;
        margin-bottom: 30rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        border-radius: 0 12rpx 12rpx 0;
    }
    .qd-tit {
        font-size: 32rpx;
        font-weight: bold;
    }
    .qd-sub-d {
        font-size: 28rpx;
        line-height: 1.6;
        padding-top: 10rpx;
    }
</style>