<template>
    <view class="content">
        <view class="ask-title">问题标题</view>
        <view class="ask-input">
            <textarea v-model="title" :maxlength="-1"    auto-height placeholder="写下你的问题,准确的描述问题更容易得到解答"/>
        </view>
        <view class="ask-title">问题描述</view>
        <view class="ask-detail">
            <textarea :maxlength="-1" auto-height v-model="content" placeholder="输入问题背景,条件等详细信息(选填)"/>
        </view>

        <view class="ask-section">
            <view class="asks-option">
                <text>匿名</text>
                <switch @change="isAnonymous" :checked="anonymous" color="#EA712A" style="transform:scale(0.7)"/>
            </view>
        </view>

        <view class="ask-send-btn" @click="postQuestions">发布问题</view>
    </view>
</template>

<script>
import api from "../../lib/api";
import {showDelayLoading, alert} from "../../lib/utils";

export default {
    data() {
        return {
            title: '',
            content: '',
            anonymous: false,
            loading: false
        }
    },
    methods: {
        isAnonymous(e){
            this.anonymous = e.detail.value
        },
        postQuestions(){
            console.log(this.title.length <= 5 )
            if (this.title.length <= 5 ||  this.title.length > 50){
                alert('标题字数限制 6 ~ 50 个')
                return
            }
            if (  this.content.length > 1000){
                alert('描述字数限制 1000 个')
                return
            }

            this.content = this.content.replace(/↵/g, "\n");

            let data={
                title: this.title,
                content: this.content,
                is_anonymous: this.anonymous ? 1 : 0
            }
            if (this.loading){
                return
            }
            this.loading = true
            let hideLoading = showDelayLoading('发布中', 200)
            api.post("qa/questions", data).then(res => {
                hideLoading()
                uni.showToast({
                    title: "发布成功",
                    icon: "success"
                })
                setTimeout(() =>{
                    uni.$emit('qa_index_change')
                }, 1000)
                setTimeout(() => {
                    uni.navigateBack()
                }, 1500)
            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(err => {
                this.loading = false
            })
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .ask-title {
        padding: 20rpx 0;
        font-size: 24rpx;
        color: #999;
    }
    .ask-input {
        display: flex;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ask-input textarea {
        flex: 1;
        padding: 30rpx;
        min-height: 100rpx;
        font-size: 32rpx;
        line-height: 1.6;
    }
    .ask-detail {
        display: flex;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ask-detail textarea {
        flex: 1;
        padding: 30rpx;
        min-height: 160rpx;
        font-size: 32rpx;
        line-height: 1.6;
    }
    .ask-send-btn {
        height: 100rpx;
        background-color: #390ABC;
        color: #FFF;
        font-size: 32rpx;
        font-weight: bold;
        border-radius: 12rpx;
        text-align: center;
        line-height: 100rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ask-section {
        text-align: right;
        margin-bottom: 30rpx;
    }
    .asks-option {
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }
    .asks-option text {
        color: #999;
        font-size: 28rpx;
    }
</style>