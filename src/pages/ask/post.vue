<template>
    <view class="content">
        <view class="ask-main-box">
            <view class="ask-input">
                <textarea v-model="content" :maxlength="-1" auto-height placeholder="请输入内容"/>
            </view>
            <view class="ai-foot">
                <view class="aif-item" @click="addImage">
                    <image src="../../images/icon/image-line.png"></image>
                    <text>图片</text>
                </view>
                <navigator url="/pages/ask/topic-list?ype=1" class="aif-item">
                    <image src="../../images/icon/chat-thread-line.png"></image>
                    <text>话题</text>
                </navigator>
            </view>
        </view>

        <view class="ai-image-warp" v-if="list.length">
            <shmily-drag-image @input="inputImg" keyName="src" :number="9" :list="list" v-model="list"></shmily-drag-image>
        </view>

        <button :disabled="!content || loadingUpdate" class="ask-send-btn" @click="postQuestions">发表</button>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert, debounce, showDelayLoading} from "@/lib/utils";

export default {
    data() {
        return {
            list: [],
            content: '',
            tagName: '',
            tagId: '',
            loadingUpdate: false,
            imageList: [],
        }
    },
    onLoad(e) {
        if (e.tag_name) {
            this.content += "#" + e.tag_name + " "
        }
        uni.$on("ask_tag_select", data => {
            this.content += "#" + data.name + " "
        })
    },
    onUnload() {
        // 页面关闭去除相关事件
        uni.$off("ask_tag_select")
    },
    watch:{
        list:{
            handler(newVal, oldVal) {

            },
            deep: true
        }
    },
    methods: {
        pickerSuccess(e) {
            console.log(e)
        },
        pickerDel(e) {
            console.log(e)
        },
        addImage() {
            if (this.list.length < 9) {
                uni.chooseImage({
                    count: 9, // 默认9
                    sizeType: ['original'], // 可以指定是原图还是压缩图，默认二者都有
                    sourceType: ['album'], // 可以指定来源是相册还是相机，默认二者都有
                    success: (res) => {
                        // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
                        const tempFilePaths = res.tempFilePaths;
                        tempFilePaths.forEach(img => {
                            let data = {
                                src: img,
                                is_update: false
                            }
                            if (this.list.length < 9){
                                this.list.push(data)
                            }
                        })
                        this.uploadImage().then(() => {
                        })
                    }
                });
            }
        },
        postQuestions() {
            if (this.loadingUpdate){
                uni.showToast({
                    title: '图片上传中',
                    icon: 'none'
                })
                return;
            }
            if (this.content.length > 1000) {
                alert('描述字数限制 1000 个')
                return
            }
            if (!this.content.length) {
                alert('内容不能为空')
                return
            }
            let imageList = []
            this.list.forEach(res => {
                imageList.push(res.key)
            })
            let data = {
                content: this.content,
                title: "",
                is_anonymous: 0,
                images: JSON.stringify(imageList)
            }
            console.log(data)
            if (this.loading) {
                return
            }

            this.loading = true
            let hideLoading = showDelayLoading('发布中', 200)
            api.post("qa/questions", data).then(res => {
                hideLoading()
                uni.showToast({
                    title: "发布成功",
                    icon: "success"
                })
                this.content = ""
                setTimeout(() => {
                    uni.$emit('qa_index_change')
                }, 800)
                setTimeout(() => {
                    uni.navigateBack()
                }, 1500)
            }).catch(err => {
                hideLoading()
                alert(err.message)
            }).finally(err => {
                this.loading = false
            })
        },
        inputImg(e){
            console.log('inputImg', e)
            this.list = e
            this.uploadImage().then(() => {
            })
        },
        async uploadImage() {
            this.loadingUpdate = true
            for (let i = 0; i < this.list.length; i++) {
                try {
                    if (!this.list[i].is_update || this.list[i].is_update == undefined){
                        const res = await api.get("qa/questions/upload-form");
                        await new Promise((resolve, reject) => {
                            uni.uploadFile({
                                url: res.url,
                                filePath: this.list[i].src,
                                formData: res.form_params,
                                name: res.name,
                                success: uploadFileRes => {
                                    // 注意：这里返回的uploadFileRes.data 为JSON 需要自己去转换
                                    console.log(uploadFileRes);
                                    let data = JSON.parse(uploadFileRes.data);
                                    if (data?.key) {
                                        this.list[i].is_update = true;
                                        this.list[i].key = data.key;

                                        resolve(); //
                                    } else {
                                        reject(uploadFileRes); // 上传失败，拒绝Promise
                                        alert(data.message);
                                    }
                                },
                                fail: error => {
                                    reject(error); // 上传失败，拒绝Promise
                                    uni.showModal({
                                        content: error.errMsg,
                                        duration: 2000,
                                        showCancel: false
                                    });
                                },
                                complete: () => {
                                }
                            });
                        });
                    }
                } catch (err) {
                    alert(err.message); // 捕获任何错误并显示
                }
            }
            this.loadingUpdate = false
        },
    }
}
</script>

<style>
page {
    background-color: #f3f3ff;
}

.content {
    padding: 30rpx;
    height: 95vh;
}

.ask-main-box {
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    background-color: #fff;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
}

.ask-input {
    display: flex;
}

.ask-input textarea {
    flex: 1;
    padding: 30rpx;
    min-height: 100rpx;
    font-size: 32rpx;
    line-height: 1.6;
}

.ai-foot {
    display: flex;
    align-items: center;
}

.aif-item {
    display: flex;
    align-items: center;
    height: 100rpx;
    padding: 0 40rpx;
    font-size: 28rpx;
}

.aif-item image {
    width: 38rpx;
    height: 38rpx;
}

.aif-item text {
    padding-left: 10rpx;
}

.ask-send-btn {
    position: relative;
    bottom: 0;
    height: 100rpx;
    background-color: #390ABC;
    color: #FFF;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 12rpx;
    text-align: center;
    line-height: 100rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}


.ai-image-warp {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 30rpx;
    width: 100%;
}

.aiw-item {
    position: relative;
    width: 214rpx;
    height: 214rpx;
    margin-right: 6rpx;
    margin-bottom: 6rpx;
}

.aiw-main-img {
    width: 210rpx;
    height: 210rpx;
    border-radius: 12rpx;
}

.aiw-delete {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: rgba(0, 0, 0, .3);
    border-radius: 100%;
}

.aiw-delete image {
    width: 28rpx;
    height: 28rpx;
    vertical-align: top;
}

.aiw-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 214rpx;
    height: 214rpx;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    border-radius: 12rpx;
    margin-right: 6rpx;
    margin-bottom: 6rpx;
}

.aiw-add image {
    width: 48rpx;
    height: 48rpx;
    vertical-align: top;
}

.aiw-add text {
    font-size: 24rpx;
    color: #FFA10A;
    padding-top: 10rpx;
}
</style>