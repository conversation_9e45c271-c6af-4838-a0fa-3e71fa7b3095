<template>
    <view class="content">
        <view class="reslit-page-box" v-if="isSelect == 0">
            <view class="rpb-title">行政处罚自测结果</view>
            <view class="rpb-ht">优秀</view>
            <view class="rpb-img">
                <image src="https://img.shiwusuo100.com/assets/app-static/selftest/cg.png" mode="widthFix"></image>
            </view>
            <view class="rpb-txt">
                合规做的好，省了数百万
            </view>
            <view class="rpb-btn" @click="goDetail">查看自测明细</view>
        </view>
        <view class="reslit-page-box" v-else>
            <view class="rpb-title">行政处罚自测结果</view>
            <view class="rpb-ht">不理想</view>
            <view class="rpb-img">
                <image src="https://img.shiwusuo100.com/assets/app-static/selftest/zg.png" mode="widthFix"></image>
            </view>
            <view class="rpb-txt">
                完啦！最高被罚<text>{{ amountTol / 10000 }}</text>万
            </view>
            <view class="rpb-btn" @click="goDetail">看看分别罚多少</view>
        </view>
    </view>
</template>

<script>
    export default {
        data() {
           return {
               testId: 0,
               amountTol: 0,
               isSelect: 0,
           }
        },
        onLoad(e) {
            this.testId = e.testId
            this.amountTol = e.amountTol
            this.isSelect = e.isSelect
        },
        methods: {
            goDetail() {
                uni.redirectTo({
                    url: "/pages/selftest/result-detail?testId=" + this.testId
                })
            }
        },
    }
</script>

<style>
    page {
        width: 100%;
        height: 100%;
        background-color: #f3f3ff;
    }
    .content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .reslit-page-box {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding-bottom: 60rpx;
    }
    .rpb-txt {
        padding-top: 30rpx;
    }
    .rpb-txt text {
        font-weight: bold;
        font-size: 48rpx;
        padding: 0 10rpx;
        color: red;
    }
    .rpb-btn {
        height: 100rpx;
        line-height: 100rpx;
        border-radius: 100rpx;
        background: linear-gradient(to right,#f65c5a,#d32d2b);
        color: #fffdc2;
        text-align: center;
        font-weight: bold;
        border: 6rpx solid #fff;
        box-shadow: 0 5rpx 60rpx rgba(0, 0, 0, .2);
        margin-top: 100rpx;
        padding: 0 60rpx;
    }


    .rpb-title {
        font-size: 28rpx;
        color: #999;
    }
    .rpb-img image {
        width: 500rpx;
    }
    .rpb-ht {
        font-size: 48rpx;
        font-weight: bold;
        padding: 20rpx 0 60rpx;
    }
</style>