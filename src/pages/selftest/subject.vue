<template>
    <view class="content">
        <swiper class="swiperList" :autoplay="false"
                easing-function="easeOutCubic"
                :duration="200" disable-programmatic-animation
                :current="noProdsDone" @change="eventHandle">
            <swiper-item v-for="(item,index) in subjectsList" :key="index">
                <scroll-view
                    v-if="index == noProdsDone ||  (index  >= noProdsDone  - 1  && index  <= noProdsDone + 1 )"
                    scroll-y="true" style="height: 100%">
                <view class="is-title">
                    <text>{{ index + 1 }}、</text>{{item.intro}}
                </view>
                <view class="isq-list">
                    <view class="isql-item" @click="selectAnswer(index, optionIndex)" :class="{cur: item.answer === optionIndex}" v-for="(option, optionIndex) in item.options" :key="optionIndex">
                        <label class="radio">
                            <radio value="" :checked="item.answer === optionIndex" color="#390ABC" style="transform:scale(0.8)" /><text>{{option.name}}</text>
                        </label>
                    </view>
                </view>
                <view class="sic-foot-eb"></view>
                </scroll-view>
            </swiper-item>

        </swiper>

        <view class="sic-foot" v-if="subjectsList.length">
            <view class="sic-foot-part">
                已答{{answerCount}}题 / 共{{ subjectsList.length }}题
            </view>
            <view class="sicf-box">
                <view v-if="answerCount == subjectsList.length" class="sicf-btn next" @click="goResult">
                    <text>查看我的风险</text>
                </view>
                <view class="sicf-btn next "  v-else :class="{disabled: !answerCount}"  @click="confirmSubmit">
                    <text>提交</text>
                </view>

            </view>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert, getDetailUrl, showToast} from "@/lib/utils";
import {showDelayLoading} from "@/lib/utils";

export default {
    data() {
        return {
            subjectsList: [],
            isSubmit: 0,
            noProdsDone: 0,
            topicId: 0,
            isAutomatic: true,
            testId: 0,
            amountTol: 0,
            loading: false,
        }
    },
    onLoad(e) {
        this.topicId = e.topic_id
        this.isSubmit = e.isSubmit
        this.getSubjectsList()
    },
    computed: {
        answerCount() {
            let answer = this.subjectsList.filter(res => res.answer !== "")
            return answer.length
        },
    },
    onUnload() {
        if (!this.answerCount) {
            return
        }
        if (this.answerCount != this.subjectsList.length && !this.isSubmit) {
            let topicId = this.topicId
            let subjectsList = this.subjectsList
            uni.showModal({
                title: '退出确认',
                content: '自测未完成，确认退出吗？',
                confirmText: '查看细则',
                cancelText: '退出',
                success(res) {
                    if (res.confirm) {
                        let options = [];
                        subjectsList.forEach(res => {
                            if (res.answer !== "") {
                                options.push({answer:res.answer, subject_id: res.id})
                            }
                        })

                        uni.setStorageSync("self_test", options)
                        uni.navigateTo({
                            url: '/pages/selftest/subject?topic_id=' + topicId + "&isSubmit=1"
                        })
                    }
                }
            });
        }

    },
    methods: {
        getSubjectsList() {

            api.get("punish/subject/test/"+ this.topicId).then(res => {
                this.subjectsList = res
                let selfTests = uni.getStorageSync("self_test")
                this.subjectsList.forEach((res, index) => {
                    res.options.push({name: "未涉及，或全部合规",amount: 0,id: 0})
                    let answer = "";
                    if (selfTests) {
                        uni.setStorageSync("self_test", "")
                        answer  = selfTests.find(test => res.id === test.subject_id)
                    }
                    if (answer && selfTests) {
                        res.answer = answer.answer
                    } else {
                        res.answer = ""
                    }
                })

                let answerIndex = this.subjectsList.findIndex(res => res.answer === "")
                if (answerIndex > -1) {
                    this.noProdsDone = answerIndex
                }
                this.$nextTick(() => {
                    if (this.isSubmit) {
                        this.answerEnd()
                    }
                })
            }).catch(err => {
                alert(err.message)
            })
        },
        eventHandle(e) {
            this.noProdsDone = e.detail.current
        },
        selectAnswer(index, optionIndex) {
            this.subjectsList[index].answer = optionIndex
            if (this.isAutomatic && this.noProdsDone < this.subjectsList.length - 1) {
                this.noProdsDone++
            }
            this.$nextTick(() => {
                if (this.answerCount == this.subjectsList.length) {

                    this.answerEnd()
                }
            })
        },
        answerEnd() {
            if (this.loading) {
                return
            }
            this.loading = true
            let amountTol = 0
            let options = []
            this.subjectsList.forEach(res => {
                let id = 0;
                if (res.answer !== "") {
                    amountTol = res.options[res.answer].amount + amountTol
                    id = res.options[res.answer].id;
                    options.push({option_id: id, subject_id: res.id})
                }

            })

            let data = {
                topic_id: this.topicId,
                amount_tol: amountTol,
                options: JSON.stringify(options),
            }
            const hideLoading = showDelayLoading("提交中", 200)

            api.post("punish/subject/test", data).then(res => {
                hideLoading()
                uni.showToast({
                    title: "已经是最后一题了",
                    icon: "none"
                })
                this.amountTol = amountTol;
                this.testId = res.test_id;
                if (this.isSubmit) {
                    this.goResult()
                }
            }).catch(err => {
                this.loading = false
                hideLoading()
                alert(err.message)
            }).finally(() => {

            })
        },
        goResult() {
            let isSelect = 0
            if (this.subjectsList.find(res => res.answer !== "" && res.options[res.answer].id)) {
                isSelect = 1
            }
            uni.redirectTo({
                url: "/pages/selftest/result?testId=" + this.testId + "&amountTol=" + this.amountTol + "&isSelect=" + isSelect
            })
        },
        prev() {
            if (this.noProdsDone) {
                this.noProdsDone--
            }
        },
        back() {
            uni.navigateBack()
        },
        confirmSubmit() {
            if (!this.answerCount) {
                return
            }
            if (this.answerCount != this.subjectsList.length) {
                uni.showModal({
                    title: '提示',
                    content: '您还有题目未完成，确定要提交吗？',
                    confirmText: '确认提交',
                    cancelText: '继续答题',
                    success:res => {
                        if (res.confirm) {
                            this.isSubmit = 1
                            this.answerEnd()
                        } else {
                            let answerIndex = this.subjectsList.findIndex(res => res.answer === "")
                            if (answerIndex > -1) {
                                this.noProdsDone = answerIndex
                            }
                        }
                    }
                })
            } else {
                // this.isSubmit = 1
                // this.answerEnd()
            }

        },
        next() {
            if (this.noProdsDone < this.subjectsList.length - 1) {
                this.noProdsDone++
            }
        }
    },
}
</script>

<style>
page {
    background-color: #f3f3ff;
}
.content {
    display: flex;
    height: 100vh;
}
.is-title {
    margin: 30rpx;
    background-color: #fff;
    padding: 30rpx;
    border-radius: 12rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    font-weight: bold;
    line-height: 1.8;
}
.isq-list {
    padding: 0 30rpx 30rpx;
}
.isql-item {
    margin-bottom: 30rpx;
}
.isql-item label {
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    border: 1px solid transparent;
}
.isql-item text {
    padding-left: 20rpx;
    line-height: 1.8;
    font-size: 28rpx;
}
.isql-item.cur label {
    padding: 30rpx;
    background-color: #f3f3ff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    border: 1px solid #390ABC;
}
.isql-item.cur text {
    color: #390ABC;
    font-weight: bold;
}
.swiperList {
    width: 100%;
    height: 100%;
    flex: 1;
}

/* 底部操作区域 */
.sic-foot {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx;
    background-color: #fff;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: env(safe-area-inset-bottom);
}
.sicf-box {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 20rpx;
}
.sicf-btn {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #390abc;
    color: #fff;
    border-radius: 12rpx;
}
.sicf-btn.disabled{
    background-color: #cccccc;
}
.sicf-btn.next {
    flex: 1;
}
.sicf-btn.prev {
    width: 200rpx;
    background-color: #f3f3ff;
    color: #333;
    margin-right: 20rpx;
}
.sicf-btn.prev.disabled {
    color: #999;
}
.sicf-btn image {
    width: 36rpx;
    height: 36rpx;
}
.sicf-btn text {
    font-weight: bold;
}
.sicf-btn.disabled {
    background-color: #999;
}
.sic-foot-eb {
    height: 260rpx;
    padding-bottom: env(safe-area-inset-bottom);
}

.sic-foot-part {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 28rpx;
    padding: 0 0 20rpx 10rpx;
    width: 400rpx;
    text-align: left;
}
.sicfp-l,
.sicfp-r {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx 0 10rpx;
}
.sicfp-l image,
.sicfp-r image {
    width: 32rpx;
    height: 32rpx;
}
.sicfp-l text,
.sicfp-r text {
    padding-left: 10rpx;
    font-size: 28rpx;
}
</style>