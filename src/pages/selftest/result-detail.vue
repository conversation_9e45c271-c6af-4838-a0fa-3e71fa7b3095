<template>
    <view class="content">
        <template v-if="!share">
            <view class="unitable-box">
                <uni-table border stripe emptyText="暂无更多数据" >
                    <!-- 表头行 -->
                    <uni-tr>
                        <uni-th width="3" align="center">序号</uni-th>
                        <uni-th width="30"  align="center">违法行为</uni-th>
                        <uni-th width="30" align="center">我的情况</uni-th>
                        <uni-th width="30" align="center">行政处罚标准</uni-th>
                    </uni-tr>
                    <!-- 表格数据行 -->
                    <uni-tr v-for="(item, index) in list" :key="index">
                        <uni-td align="center">{{index + 1}}</uni-td>
                        <uni-td>{{item.subject.intro}}</uni-td>
                        <uni-td v-if="item.option_id">{{item.option.name}}</uni-td>
                        <uni-td v-else>未涉及，或全部合规</uni-td>
                        <uni-td v-if="item.option_id">{{item.option.standard}}</uni-td>
                        <uni-td v-else>未涉及，或全部合规</uni-td>
                    </uni-tr>

                </uni-table>
            </view>

            <view class="sic-foot-eb"></view>
            <view class="sic-foot">
                <view class="sicf-box">
                    <view class="sicf-btn" @click="goSearch">
                        <text>查看完整版处罚标准</text>
                    </view>
                    <view class="sicf-btn share" @click="openQrCode">
                        <text>分享好友测一测</text>
                    </view>
                </view>
            </view>
            <uni-popup ref="sharePop" type="center">
                <view class="rdp-share-box">
                    <view class="rdpsb-img" v-if="imgUrl">
                        <image :src="imgUrl" mode="widthFix"></image>
                    </view>
                    <button @click="saveNetworkImage" class="rdpsb-btn" >保存图片</button>
                </view>
            </uni-popup>
        </template>
        <template v-else>
            <view class="content">
                <view class="service-intro-cnt">
                    <image src="https://img.shiwusuo100.com/assets/app-static/selftest/01.png" mode="widthFix"></image>
                </view>
            </view>
        </template>
        <!-- 分享海报 -->

    </view>

</template>
<script>
import api from "@/lib/api";
import {alert, showDelayLoading} from "@/lib/utils";
import {mapState} from "pinia";
import {useUserStore} from "@/store/user";
import {getAppName} from "@/lib/context";

export default {
    data() {
        return {
            testId: 0,
            share: 0,
            imgUrl: "",
            list: [],
            appName: getAppName()
        }
    },
    onLoad(e) {
        if (e.testId) {
            this.testId = e.testId
        } else {
            uni.switchTab({
                url:'/pages/index/index'
            })
        }

        if (this.user) {
            this.initData()
        }
        if (e.share) {
            this.share = e.share
        }
    },
    watch: {
        loaded(newVal, oldVal) {
            if (this.user) {
                this.$nextTick(() => {
                    this.initData()
                })
            }
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    methods: {

        initData() {
            const hideLoading = showDelayLoading("加载中", 200);

            api.get("punish/subject/test/result/" + this.testId).then(res => {
                hideLoading()
                this.list = res
            }).catch(err => {
                hideLoading()
                alert(err.message)
            })
        },
        openQrCode() {
            const hideLoading = showDelayLoading("生成中", 200);

            api.get("wechat/share/qr_code",).then(res => {
                this.imgUrl = res.imgUrl
                hideLoading()
                console.log(this.imgUrl)
                this.$nextTick(() => {
                    this.$refs.sharePop.open('center')
                })
            }).catch(err => {
                console.log(err)
                hideLoading()
                alert(err.message)
            })
        },
        saveBase64ImageToAlbum(base64Image) {
            // 将 base64 转换为临时文件
            const fs = wx.getFileSystemManager();
            const [, base64Data] = base64Image.split(',');

            // 生成随机文件名
            const tempFilePath = `${wx.env.USER_DATA_PATH}/share_${Date.now()}.png`;

            try {
                // 将 base64 写入临时文件
                fs.writeFileSync(
                    tempFilePath,
                    base64Data,
                    'base64'
                );

                // 保存到相册
                wx.saveImageToPhotosAlbum({
                    filePath: tempFilePath,
                    success: () => {
                        wx.showToast({
                            title: '图片已保存到相册',
                            icon: 'success'
                        });

                        // 可选：删除临时文件
                        fs.unlink({
                            filePath: tempFilePath,
                            success: () => {
                                console.log('临时文件已删除');
                            }
                        });
                    },
                    fail: (err) => {
                        console.error('保存图片失败:', err);
                        wx.showToast({
                            title: '保存失败',
                            icon: 'none'
                        });
                    }
                });
            } catch (error) {
                console.error('处理 base64 图片失败:', error);
                wx.showToast({
                    title: '图片处理失败',
                    icon: 'none'
                });
            }
        },
        goSearch() {
            uni.navigateTo({
                url:"/pages/search/doc?q=应急管理行政处罚裁量权基准"
            })
        },
        saveNetworkImage() {
            this.saveBase64ImageToAlbum(this.imgUrl)
        },
    },

    onShareAppMessage() {
        let path = "/pages/index/index?ref=" + this.user.uuid;

        return {
            title: this.appName,
            path,
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        };
    },
    onShareTimeline(res) {
        return {
            title: this.appName,
            query: 'share=1',
            imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
        };
    }
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .unitable-box {
        padding: 30rpx 0;
    }
    .unitable-box .uni-table-th {
        background-color: #390ABC;
        color: #fff;
        white-space: nowrap;
    }
    .sic-foot {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 20rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
        z-index: 10;
    }
    .sicf-box {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .sicf-btn {
        flex: 1;
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #390abc;
        color: #fff;
        border-radius: 100rpx;
        margin-bottom: env(safe-area-inset-bottom);
        font-size: 28rpx;
    }
    .sicf-btn.share {
        margin-left: 20rpx;
        background-color: #f3f3ff;
        border: 1px solid #390ABC;
        color: #390ABC;
    }
    .sicf-btn image {
        width: 36rpx;
        height: 36rpx;
    }
    .sicf-btn text {
        font-weight: bold;
    }
    .sicf-btn.disabled {
        background-color: #999;
    }
    .sic-foot-eb {
        height: 148rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }


    .rdpsb-img {
        border-radius: 20rpx;
    }
    .rdpsb-img image {
        width: 600rpx;
        border-radius: 20rpx;
        vertical-align: top;
    }
    .rdpsb-btn {
        height: 94rpx;
        line-height: 94rpx;
        background-color: #390abc;
        color: #fff;
        width: 220rpx;
        text-align: center;
        margin: 0 auto;
        border-radius: 100rpx;
        font-weight: bold;
        margin-top: 20rpx;
        font-size: 32rpx;
    }
    .service-intro-cnt image {
        width: 100%;
    }
</style>