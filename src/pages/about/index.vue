<template>
    <view class="content">
        <view class="super-topic-logo">
            <view class="stl-image">
                <image src="../../images/logo_white.png"></image>
            </view>
            <view class="stl-txt">
                <view class="stlt-name">{{ appName }}</view>
            </view>
        </view>

        <navigator url="/pages/about/agreement" class="help-btn">
            <view class="hb-left">
                <image src="../../images/icon/receipt-line.png" mode="widthFix"></image>
                <text>用户协议</text>
            </view>
            <view class="hb-right">
                <image src="../../images/icon/arrow-right-s-line.png"></image>
            </view>
        </navigator>

        <navigator url="/pages/about/privacy" class="help-btn">
            <view class="hb-left">
                <image src="../../images/icon/file-lock-line.png" mode="widthFix"></image>
                <text>隐私政策</text>
            </view>
            <view class="hb-right">
                <image src="../../images/icon/arrow-right-s-line.png"></image>
            </view>
        </navigator>
        <view v-if="version" class="about-version">V {{version}}</view>
    </view>
</template>

<script>
import { getAppName } from '@/lib/context.js';

export default {
    data() {
        return {
            appName: getAppName()
        }
    }
}

</script>

<style>
    page{
        background-color: #F3F3FF;
    }
    .super-topic-logo {
      padding: 60rpx 0 80rpx;
      background-color: #fff;
      margin: 30rpx;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }

    .stl-image image {
      width: 120rpx;
      height: 120rpx;
    }
    .stl-txt .stlt-name {
      font-size: 32rpx;
      font-weight: bold;
      padding: 20rpx 0 10rpx;
      color: #390ABC;
    }
    .stl-txt .stlt-slogen {
      font-size: 28rpx;
      color: #999;
    }

    .help-btn {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 30rpx 30rpx;
        padding: 30rpx;
        background-color: #ffffff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .hb-left {
        display: flex;
        align-items: center;
    }
    .hb-left image {
        width: 32rpx;
        height: 32rpx;
    }
    .hb-left text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .hb-right {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .hb-right image {
        width: 36rpx;
        height: 36rpx;
        color: #999;
    }
    .about-version{
        position: fixed;
        left: 50%;
        transform: translateX(-50%);
        height: 60rpx;
        line-height: 60rpx;
        color: #999;
        font-size: 28rpx;
        padding: 0 40rpx;
        bottom: 40rpx;
        border-radius: 12rpx;
        margin-bottom: env(safe-area-inset-bottom);
    }
</style>
