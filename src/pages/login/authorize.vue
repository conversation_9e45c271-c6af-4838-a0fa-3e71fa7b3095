<template>
    <view class="content" v-if="!logged">
        <view class="pc-login-box">
            <view class="pclb-top">
                <image src="/src/images/icon/computer-line.png"></image>
                <view class="pclb-tit">电脑端登录确认</view>
            </view>
            <view class="pclb-foot">
                <button class="pclb-btn pclbf-submit" @click="authorize">授权登录账号</button>
                <button class="pclb-btn pclbf-cancle">取消</button>
            </view>
        </view>
    </view>
    <view class="content" v-else>
        <view class="pc-login-box">
            <view class="pclb-top">
                <image src="/src/images/icon/sucess-200.png"></image>
                <view class="pclb-yes">授权登录成功</view>
            </view>
        </view>
    </view>
</template>
<script>
import api from '@/lib/api.js';
import { alert, getDeviceInfo } from '@/lib/utils.js';
import { useUserStore } from '@/store/user.js';
import { mapActions } from 'pinia';

export default {
    data() {
        return {
            scene: '',
            logged: false
        };
    },
    onLoad(options) {
        this.scene = options.scene;
    },
    methods: {
        ...mapActions(useUserStore, ["login"]),
        authorize() {
            uni.showLoading({
                title: "授权中",
                mask: true
            });

            uni.getProvider({
                service: "oauth",
                success: p => {
                    if (p.provider.includes("weixin")) {
                        //微信小程序授权登录
                        uni.login({
                            provider: "weixin",
                            success: res => {
                                const deviceInfo = getDeviceInfo()
                                let data = {code: res.code, platform: deviceInfo.platform, system: deviceInfo.system}
                                api.request("login/wechat-qrcode/" + this.scene, {method: "POST", data}).then(res => {
                                    this.login(res.data);
                                    uni.hideLoading();
                                    this.logged = true;
                                }, e => {
                                    uni.hideLoading();

                                    if (e.code == 404) {
                                        const bindCode = e.header("X-Bind-Code");
                                        uni.showToast({
                                            title: e.message,
                                            showCancel: false,
                                            success: () => {
                                                uni.navigateTo({
                                                    url: "/pages/login/bind?code=" + bindCode
                                                });
                                            }
                                        });
                                    } else {
                                        uni.showModal({
                                            title: "登录失败",
                                            content: e.message,
                                            showCancel: false
                                        });
                                    }
                                });
                            },
                            fail: e => {
                                uni.hideLoading();
                                alert(e.errMsg);
                            }
                        });
                    } else {
                        //没有可用的快捷登录
                        uni.hideLoading();
                        alert("不支持的登录方式");
                    }
                },
                fail: e => {
                    uni.hideLoading();
                    alert(e.errMsg);
                }
            });
        }
    }
}
</script>
<style scoped>

    .content {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .pc-login-box {
        padding-bottom: 200rpx;
    }
    .pclb-top {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    .pclb-top image {
        width: 160rpx;
        height: 160rpx;
    }
    .pclb-tit {
        padding-top: 20rpx;
        font-size: 32rpx;
    }

    .pclb-foot {
        padding-top: 160rpx;
        width: 500rpx;
        margin: 0 auto;
    }
    .pclb-btn {
        height: 100rpx;
        text-align: center;
        line-height: 100rpx;
        font-size: 32rpx;
        font-weight: bold;
        border-radius: 12rpx;
        margin-bottom: 40rpx;
    }
    .pclbf-submit {
        background-color: #390ABC;
        color: #FFF;
    }
    .pclbf-cancle {
        background-color: #F3F3FF;
    }
    .pclb-yes {
        padding-top: 40rpx;
        color: #12B45F;
    }
</style>