<template>
    <view class="contet">
        <view class="login-main-box">
            <view class="login-top-part">
                <view class="ltp-h3">绑定手机号</view>
                <view class="ltp-p">为保障您的账号安全，请绑定手机号，未注册手机号将自动为您创建账号</view>
            </view>
            <view class="login-input-part">
                <input class="uni-input" placeholder="请输入手机号" v-model="phone" />
                <GetPhoneCode :phone="phone" type="bind" v-model="code" />
                <view class="lip-btn" @click="bind">绑定</view>
            </view>
        </view>
    </view>
</template>

<script>
import GetPhoneCode from '@/components/get-phone-code.vue';
import api from '@/lib/api.js';
import { getReferral } from '@/lib/context.js';
import { loginReturn } from '@/lib/login.js';
import { useUserStore } from '@/store/user.js';
import { mapActions } from 'pinia';
import {getDeviceInfo} from "@/lib/utils";

export default {
    components: {
        GetPhoneCode
    },
    data() {
        return {
            bindCode: "",
            phone: "",
            code: ""
        };
    },
    onLoad(options) {
        if (!options.code) {
            uni.showModal({
                title: "温馨提醒",
                content: "参数错误，请返回重试。",
                showCancel: false,
                success: () => {
                    uni.navigateBack();
                }
            });
            return;
        }

        this.bindCode = options.code;
    },
    methods: {
        ...mapActions(useUserStore, ["login"]),
        bind() {
            uni.showLoading({
                title: "绑定中",
                mask: true
            });

            const deviceInfo = getDeviceInfo()

            let data = {
                bindCode: this.bindCode,
                from: "phoneCode",
                phone: this.phone,
                code: this.code,
                referral: getReferral(),
                platform: deviceInfo.platform,
                system: deviceInfo.system,
            }
            api.post("login/bind", data)
                .then(info => {
                    this.login(info);
                    uni.showToast({
                        title: "绑定成功",
                        showCancel: false,
                        success: () => setTimeout(loginReturn, 1000)
                    });
                }, e => {
                    uni.hideLoading()
                    uni.showModal({
                        title: "绑定失败",
                        content: e.message,
                        showCancel: false
                    });
                });
        }
    }
}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .login-main-box {
        width: 540rpx;
        padding-top: 80rpx;
        margin: 0 auto;
    }
    .login-top-part {
        padding-bottom: 80rpx;
    }
    .ltp-h3 {
        font-weight: bold;
        padding-bottom: 20rpx;
    }
    .ltp-p {
        font-size: 28rpx;
        color: #999;
        line-height: 1.6;
    }
    .login-input-part .uni-input {
        height: 100rpx;
        background-color: #fff;
        border-radius: 12rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .lip-btn {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        background-color: #390ABC;
        color: #fff;
        margin-top: 60rpx;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
</style>