<template>
    <view class="content">
        <view class="org-me-top">
            <view class="orgmt-l">
                <view class="orgmtl-tit">{{userFrom && userFrom.name}}</view>
                <view class="orgmtl-info">
                    <image src="/src/images/icon/building-4-line-grey.png"></image>
                    <text>{{orgData && orgData.name}}</text>
                </view>
            </view>
            <view class="orgmt-r" v-if="userFrom && userFrom.photo_url">
                <image :src="userFrom.photo_url" mode="widthFix"></image>
            </view>
        </view>

        <view class="org-me-middle">
            <navigator url="/packageOrg/organization/me/info" class="orgmm-item">
                <view class="orgmmi-l">
                    <image src="/src/images/icon/file-user-line.png"></image>
                    <text>个人信息</text>
                </view>
                <view class="orgmmi-r">
                    <image src="/src/images/icon/arrow-right-s-line.png"></image>
                </view>
            </navigator>
            <navigator url="/packageOrg/organization/me/learn" class="orgmm-item">
                <view class="orgmmi-l">
                    <image src="/src/images/icon/video-ai-line.png"></image>
                    <text>学习记录</text>
                </view>
                <view class="orgmmi-r">
                    <image src="/src/images/icon/arrow-right-s-line.png"></image>
                </view>
            </navigator>
            <navigator url="/packageOrg/organization/me/exam" class="orgmm-item">
                <view class="orgmmi-l">
                    <image src="/src/images/icon/contract-line.png"></image>
                    <text>考试记录</text>
                </view>
                <view class="orgmmi-r">
                    <image src="/src/images/icon/arrow-right-s-line.png"></image>
                </view>
            </navigator>
            <view class="orgmm-quit" @click="meLogout">
                <image src="/src/images/icon/shut-down-line.png"></image>
                <text>退出登录</text>
            </view>
        </view>

        <!-- 底部导航 -->
        <view class="orgfn-eb"></view>
        <view class="org-foot-nav">
            <navigator open-type="redirect" url="/packageOrg/organization/enroll/index" class="orgfn-item" v-if="userFrom && userFrom.org_enable_enroll">
                <image src="/src/images/icon/account-box-edit-outline.png"></image>
                <text>报名</text>
            </navigator>
            <navigator open-type="redirect" url="/packageOrg/organization/home" class="orgfn-item ">
                <image src="/src/images/icon/graduation-cap-line.png"></image>
                <text>培训</text>
            </navigator>
            <navigator open-type="redirect" url="/packageOrg/organization/me" class="orgfn-item cur">
                <image src="/src/images/icon/user-4-fill.png"></image>
                <text>我的</text>
            </navigator>
        </view>
    </view>
</template>

<script>
import {mapActions, mapState} from "pinia";
import {useUserStore} from "@/store/user";
import api from "@/lib/api";
import {alert} from "@/lib/utils";

export default {
    data() {
        return {
            orgSid:'',
            userFrom: {}
        }
    },
    onLoad() {
        this.orgSid = uni.getStorageSync("org_id")
        this.getEnrollments()
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded', 'orgData']),
    },
    watch: {
        loaded(newData, oldData) {
            if (newData == false) {
                if (!this.user) {
                    uni.reLaunch({
                        url: "/packageOrg/organization/login/index"
                    })
                }
            }
        }
    },
    methods: {
        ...mapActions(useUserStore,['logout']),
        meLogout() {
            uni.showModal({
                content: "确认要退出吗？",
                confirmText: "确定",
                success: (res) => {
                    if (res.confirm) {
                        this.logout();
                        uni.reLaunch({
                            url: "/packageOrg/organization/login/index"
                        })
                    }
                }
            })
        },
        getEnrollments() {
            api.get(`orgs/${this.orgSid}/my-enrollment`).then(res => {
                this.userFrom = res
            }).catch(err => {
                alert(err.message)
            })
        },
    },
}
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .org-me-top {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin: 40rpx 40rpx 60rpx;
    }
    .orgmtl-tit {
        font-size: 38rpx;
        font-weight: bold;
    }
    .orgmtl-info {
        padding-top: 20rpx;
        display: flex;
        align-items: center;
    }
    .orgmtl-info image {
        width: 32rpx;
        height: 32rpx;
    }
    .orgmtl-info text {
        font-size: 24rpx;
        color: #999;
        padding-left: 10rpx;
    }
    .orgmt-r {
        padding: 10rpx;
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .orgmt-r image {
        width: 200rpx;
        vertical-align: top;
    }

    /* 我的 - 导航 */
    .org-me-middle {
        margin: 40rpx 30rpx;
    }
    .orgmm-item {
        background-color: #fff;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20rpx;
        height: 100rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .orgmmi-l {
        display: flex;
        align-items: center;
    }
    .orgmmi-l text {
        padding-left: 10rpx;
        font-size: 28rpx;
        font-weight: bold;
    }
    .orgmmi-l image {
        height: 40rpx;
        width: 40rpx;
    }
    .orgmmi-r image {
        height: 32rpx;
        width: 32rpx;
    }
    .orgmm-quit {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #FFFFFF;
        height: 90rpx;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .orgmm-quit image {
        width: 32rpx;
        height: 32rpx;
    }
    .orgmm-quit text {
        padding-left: 10rpx;
        font-size: 28rpx;
        color: #999;
    }
    /* 底部导航 */
    .org-foot-nav {
        display: flex;
        align-items: center;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding-bottom: env(safe-area-inset-bottom);
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        z-index: 2;
    }
    .orgfn-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20rpx 0;
    }
    .orgfn-item image {
        width: 48rpx;
        height: 48rpx;
    }
    .orgfn-item text {
        padding-top: 10rpx;
        font-size: 24rpx;
        color: #aaa;
    }
    .orgfn-item.cur text {
        color: #390ABC;
        font-weight: bold;
    }
    .orgfn-eb {
        height: 130rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }
</style>