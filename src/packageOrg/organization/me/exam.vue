<template>
    <view class="content">

        <view class="exam-list">
            <navigator :url="`/pages/training/exam/end-test?test_id=${item.sid}`" class="examl-item"  v-for="(item, index) in list" :key="index">
                <view class="examli-l">
                    <view class="examlil-h">{{item.topic.name}}<uni-dateformat :date="item.created_at" format="yyyyMMdd"></uni-dateformat></view>
                    <view class="examlil-p">分数：{{ item.score}}</view>
                    <view class="examlil-p">考试时间：{{item.end_at}}</view>
                </view>
                <view class="examli-r">
                    <image src="/src/images/icon/arrow-right-s-line.png"></image>
                </view>
            </navigator>
        </view>

        <view class="no-data-nomal-box" v-if="!loading && !list.length">
            <view class="ndnb-icon">
                <image src="../../../images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无数据</text>
        </view>
    </view>
</template>

<script>
import api from "@/lib/api";
import {alert} from "@/lib/utils";
export default {
    data() {
        return {
            list: [],
            orgId: '',
            loading: true,
        }
    },
    onLoad(query) {
        if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id")
        }
        this.initList()
    },
    methods: {
        initList() {
            api.get(`orgs/${this.orgId}/train-tests`).then(res => {
                this.list = res;
            }).catch(err => { alert(err.message) }).finally(() => {
                this.loading = false;
            })
        }
    },

}
</script>

<style>
    page {
        background-color: #F3F3FF;
    }
    .content {
        padding: 30rpx;
    }
    .examl-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 30rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .examli-l {
        flex: 1;
    }
    .examli-r image {
        width: 32rpx;
        height: 32rpx;
    }
    .examlil-h {
        font-weight: bold;
        font-size: 28rpx;
    }
    .examlil-p {
        padding-top: 10rpx;
        font-size: 24rpx;
        color: #999;
    }
</style>