<template>
    <view class="content">
        <view class="test-intro">
<!--            <view class="ti-tit">{{ exam.topic?.name }}</view>-->
            <view class="ti-data-list">
                <view class="tidl-item">
                    <view class="tidli-num">{{exam.exam_limit_count == -1 ? '无限次' : exam.exam_limit_count + '次'}}</view>
                    <view class="tidli-txt">考试次数</view>
                </view>
                <view class="tidl-item">
                    <view class="tidli-num">{{exam.exam_time == -1 ? '随时考' : exam.exam_time }}</view>
                    <view class="tidli-txt">考试时间</view>
                </view>
                <view class="tidl-item">
                    <view class="tidli-num" v-if="exam.exam_mode == 'pc_only'">仅电脑端考试</view>
                    <view class="tidli-num" v-if="exam.exam_mode == 'mobile_only'">仅手机端</view>
                    <view class="tidli-num" v-if="exam.exam_mode == 'all'">不限制</view>
                    <view class="tidli-txt">考试方式</view>
                </view>
            </view>
            <view class="ti-data-list">
                <view class="tidl-item">
                    <view class="tidli-num">{{exam.total_score}}<text>分</text></view>
                    <view class="tidli-txt">试卷总分</view>
                </view>
                <view class="tidl-item">
                    <view class="tidli-num">{{exam.limit_time}}<text>分钟</text></view>
                    <view class="tidli-txt">考试用时</view>
                </view>
            </view>

            <view class="start-test-btn" @click="start()">开始考试</view>
        </view>

        <view class="test-history-list">
            <view class="thl-tit">考试记录</view>
            <view class="thl-cnt">
                <uni-table border stripe emptyText="暂无更多数据">
                    <uni-tr>
                        <uni-th align="left" width="140rpx">考试时间</uni-th>
                        <uni-th align="left" width="120rpx">考试得分</uni-th>
                        <uni-th align="left" width="160rpx">操作</uni-th>
                    </uni-tr>
                    <uni-tr v-for="(item, index) in testList" :key="index">
                        <uni-td><uni-dateformat :date="item.created_at" format="yyyy-MM-dd hh:mm"></uni-dateformat></uni-td>
                        <uni-td>{{ item.score }}分</uni-td>
                        <uni-td><text class="tb-link" @click="check(item)">查看</text>
<!--                            <text class="tb-link" @click="delTest(item, index)" >删除</text>-->
                        </uni-td>
                    </uni-tr>
                </uni-table>
            </view>
        </view>
    </view>
</template>

<script>
import {showDelayLoading, alert, showToast} from "@/lib/utils";
import api from "@/lib/api";

export default {
    data() {
        return {
            topicSid: 0,
            judge: 0,
            multi: 0,
            single: 0,
            orgId: 0,
            name: '',
            scroll_id: '',
            exam: {},
            testList: []
        }
    },
    onLoad(query) {
        if (query.data) {
            this.exam = JSON.parse(query.data)
            this.topicSid = this.exam.topic.sid
        }
        if (query.name) {
            this.name = query.name
        }
        if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id")
        }
        this.initData();

        uni.$on('topic_test_complete', () => {
            this.testList = [];
            this.scroll_id = '';
            this.initData()
        })
    },
    onReachBottom() {
        console.log("上拉加载");
        if (this.scroll_id) {
            this.getCourseList();
        }
    },

    methods: {
        initData() {
            console.log(123123123)
            api.get(`orgs/${this.orgId}/train-tests`).then((res) => {
                this.testList = res
            }).catch(err => {
                alert(err.message);
            })
        },

        start() {
            if (this.exam.exam_mode == 'pc_only') {
                uni.showModal({
                    content: "考试不支持手机端，请使用电脑端查看",
                    showCancel: false
                })
                return
            }
            if (!this.exam.can_exam) {
                uni.showModal({
                    content: this.exam.reason,
                    showCancel: false
                })
                return
            }

            let type = 9
            api.get("training/tests/current/" + this.topicSid + "?type=" + type).then(res => {
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.test.sid
                })

            }).catch(err => {
                if (err.code == 404) {
                    this.createTest(type)
                } else {
                    uni.hideLoading();
                    alert(err.message)
                }
            })
        },
        createTest(type) {
            const hideLoading = showDelayLoading("请稍后", 200)

            api.post("training/" + this.topicSid + "/tests", {type: type,org_sid: this.orgId, enroll_id: this.exam.sid}).then(res => {
                hideLoading()
                uni.navigateTo({
                    url: "/pages/training/exam/test?sid=" + res.sid
                })
            }).catch(err => {
                uni.hideLoading();
                alert(err.message)
            })
        },
        check(data) {
            uni.navigateTo({
                url: "/pages/training/exam/end-test?test_id=" + data.sid
            })
        },
        delTest(data, index) {
            uni.showModal({
                title: "删除",
                content: "确定要删除考试记录吗？",
                confirmText: "确定删除",
                success: (res) => {
                    if (res.confirm) {
                        api.delete("training/tests/" + data.sid).then(res => {
                            uni.showToast({
                                title: "删除成功",
                                icon: "none"
                            })
                            this.testList.splice(index, 1)
                        })
                    }
                }
            })
        }
    }
}
</script>

<style>
page {
    background-color: #F3F3FF;
}

.content {
    padding: 30rpx;
}

.test-intro {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.ti-tit {
    text-align: center;
    font-weight: bold;
    padding-bottom: 30rpx;
}

.ti-data-list {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #F3F3FF;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
}

.tidl-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.tidli-num {
    font-size: 36rpx;
    font-weight: bold;
}

.tidli-num text {
    font-size: 24rpx;
    color: #999;
    font-weight: normal;
    padding-left: 10rpx;
}

.tidli-txt {
    font-size: 28rpx;
    color: #999;
    padding-top: 10rpx;
}

.tti-tit {
    position: relative;
    font-weight: bold;
    padding: 10rpx 0 30rpx;
}

.tti-tit::after {
    content: '';
    position: absolute;
    left: 0;
    width: 126rpx;
    height: 8rpx;
    background-color: #390ABC;
    bottom: 24rpx;
}

.tti-h3 {
    font-size: 28rpx;
    font-weight: bold;
    padding-bottom: 10rpx;
}

.tti-p {
    font-size: 28rpx;
    line-height: 1.8;
    padding-bottom: 30rpx;
}

.start-test-btn {
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 12rpx;
    background-color: #390ABC;
    color: #fff;
    text-align: center;
    font-size: 28rpx;
}

.test-history-list {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    box-shadow: 0 5rpx 30rpx rgba(0, 0, 0, .04);
}

.thl-tit {
    position: relative;
    font-weight: bold;
    padding: 10rpx 0 30rpx;
}

.thl-tit::after {
    content: '';
    position: absolute;
    left: 0;
    width: 126rpx;
    height: 8rpx;
    background-color: #390ABC;
    bottom: 24rpx;
}


.tb-link {
    padding: 20rpx 10rpx;
    color: #390ABC;
    text-decoration: underline;
}
</style>