<template>
    <view class="content">
        <view class="category-list">
            <view class="cl-item" :class="{cur: item.sid == sid}" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
                <text>{{ item.name }}</text>
                <image v-if="item.sid == sid" src="@/images/icon/check-line-blue.png"></image>

                <image v-else src="@/images/icon/arrow-right-s-line.png"></image>
            </view>
        </view>
        <view class="no-data-nomal-box" v-if="!loading && !list.length">
            <view class="ndnb-icon">
                <image src="@/images/empty.png" mode="widthFix"></image>
            </view>
            <text class="ndnb-tip">暂无数据</text>
        </view>
    </view>
</template>

<script>
import {alert} from "@/lib/utils";
import api from "@/lib/api";

export default {
    data() {
        return {
            list: [],
            sid:'',
            orgId:'',
            loading: true,
            isBack: false,
        }
    },
    onLoad(query) {
        this.sid = query.topic_id
        if (uni.getStorageSync("org_id")) {
            this.orgId = uni.getStorageSync("org_id")
        }
        if (query.is_back){
            this.isBack = true
        }
        this.initData()
    },
    onPullDownRefresh() {
        this.initData()
    },
    methods:{
        initData(){
            api.get(`orgs/${this.orgId}/topics`).then(res => {
                this.list = res;
            }).catch(err =>  { uni.hideLoading();alert(err.message)}).finally(() => {
                this.loading = false;
            }).finally(() => {
                this.loading = false
                uni.stopPullDownRefresh();
            })
        },
        goDetail(item){
            this.sid = item.sid
            uni.$emit('choose_topic', item.sid)
            if (this.isBack){
                uni.navigateBack()
            }else {
                uni.navigateTo({
                    url:'/pages/training/exam/index?topic_id=' + item.sid + '&org_sid=' + this.orgId
                })
            }

        }
    }

}
</script>

<style>
page {
    background-color: #F3F3FF;
}
.content {
    padding: 30rpx;
}
.cl-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 120rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 0 30rpx;
    margin-bottom: 30rpx;
    font-weight: bold;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    border: 1px solid #fff;
}
.cl-item.cur {
    background-color: #edf5fe;
    color: #065CDF;
}
.content image {
    width: 32rpx;
    height: 32rpx;
}
.no-data-nomal-box {
    position: relative;
    text-align: center;
}

.ndnb-icon image {
    width: 500rpx;
}
</style>