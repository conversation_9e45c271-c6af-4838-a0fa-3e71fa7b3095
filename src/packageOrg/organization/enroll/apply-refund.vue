<template>
    <view class="refund-apply-page">
        <view class="refund-apply-card">
            <view class="refund-apply-title">报名课程</view>
            <view class="refund-apply-course">{{ courseTitle }}</view>
            <view class="refund-apply-amount">金额：{{ originalAmount }}元</view>
        </view>
        <view class="refund-apply-form">
            <view class="refund-apply-label">退款原因<text class="refund-apply-required">※</text></view>
            <textarea v-model="reason" class="refund-apply-textarea" placeholder="请输入" />
<!--            <view class="refund-apply-label">退款金额<text class="refund-apply-required">※</text></view>-->
<!--            <input v-model="amount"  disabled class="refund-apply-input" type="number" />-->
        </view>
        <view class="refund-apply-bottom-bar">
            <view class="refund-apply-bottom-black">退款金额 ￥{{ amount || 0 }}</view>
            <button class="refund-apply-bottom-btn" :class="{ 'loading': loading }" @click="onSubmit" :disabled="loading">
                {{ loading ? '提交中...' : '申请退款' }}
            </button>
        </view>
    </view>
</template>

<script>
import api from '@/lib/api';
import urls from '@/lib/urls';

export default {
    data() {
        return {
            enrollId: '',
            courseTitle: '',
            originalAmount: '',
            reason: '',
            amount: '',
            loading: false
        }
    },
    onLoad(options) {
        console.log('apply-refund onLoad options:', options);

        // 获取传递过来的参数
        if (options.enrollId) {
            this.enrollId = options.enrollId;
        }
        if (options.title) {
            this.courseTitle = decodeURIComponent(options.title);
        }
        if (options.amount) {
            this.originalAmount = parseFloat(options.amount);
            this.amount = parseFloat(options.amount); // 默认退款金额等于原金额
        }
    },
    methods: {
        onSubmit() {
            if (!this.reason.trim()) {
                uni.showToast({
                    title: '请输入退款原因',
                    icon: 'none'
                });
                return;
            }

            if (this.amount > this.originalAmount) {
                uni.showToast({
                    title: '退款金额不能超过原金额',
                    icon: 'none'
                });
                return;
            }

            if (!this.enrollId) {
                uni.showToast({
                    title: '报名记录ID不存在',
                    icon: 'none'
                });
                return;
            }

            this.submitRefund();
        },

        submitRefund() {
            if (this.loading) return;

            this.loading = true;
            uni.showLoading({
                title: '提交中...'
            });

            const data = {
                reason: this.reason.trim(),
            };

            console.log('提交退款申请:', data);

            api.put(urls.enrollApplyRefund(this.enrollId), data).then(res => {
                console.log('退款申请成功:', res);
                uni.hideLoading();

                uni.showToast({
                    title: '退款申请已提交',
                    icon: 'success'
                });

                // 延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);

            }).catch(err => {
                console.error('退款申请失败:', err);
                uni.hideLoading();

                uni.showToast({
                    title: err.message || '提交失败，请重试',
                    icon: 'none'
                });
            }).finally(() => {
                this.loading = false;
            });
        }
    }
}
</script>

<style lang="scss">
.refund-apply-page {
    min-height: 100vh;
    background: #f5f6fa;
    padding-bottom: 120rpx;
}
.refund-apply-card {
    background: #fff;
    border-radius: 16rpx;
    margin: 32rpx 24rpx 0 24rpx;
    padding: 32rpx 32rpx 24rpx 32rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}
.refund-apply-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #222;
    margin-bottom: 16rpx;
}
.refund-apply-course {
    font-size: 28rpx;
    color: #222;
    margin-bottom: 8rpx;
}
.refund-apply-amount {
    font-size: 26rpx;
    color: #222;
}
.refund-apply-form {
    background: #fff;
    border-radius: 16rpx;
    margin: 24rpx 24rpx 0 24rpx;
    padding: 32rpx;
    box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
}
.refund-apply-label {
    font-size: 28rpx;
    color: #222;
    font-weight: bold;
    margin-bottom: 12rpx;
    margin-top: 24rpx;
}
.refund-apply-required {
    color: #f56c6c;
    font-size: 24rpx;
    margin-left: 4rpx;
}
.refund-apply-textarea {
    width: 100%;
    min-height: 120rpx;
    border: 2rpx solid #ccc;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 16rpx;
    background: #fff;
    margin-bottom: 24rpx;
}
.refund-apply-input {
    width: 100%;
    height: 64rpx;
    border: 2rpx solid #ccc;
    border-radius: 8rpx;
    font-size: 28rpx;
    padding: 0 16rpx;
    background: #fff;
    margin-bottom: 24rpx;
}
.refund-apply-bottom-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: row;
    height: 96rpx;
    background: transparent;
    z-index: 10;
    padding: 0 24rpx 32rpx 24rpx;
}
.refund-apply-bottom-black {
    flex: 1;
    background: #000;
    color: #fff;
    font-size: 32rpx;
    border-top-left-radius: 32rpx;
    border-bottom-left-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}
.refund-apply-bottom-btn {
    flex: 1;
    background: #1677ff;
    color: #fff;
    font-size: 32rpx;
    border-top-right-radius: 32rpx;
    border-bottom-right-radius: 32rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;

    &.loading {
        background: #ccc;
        opacity: 0.7;
    }

    &:disabled {
        background: #ccc;
        opacity: 0.7;
    }
}
</style>