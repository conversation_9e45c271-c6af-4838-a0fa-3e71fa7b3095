<template>
    <view class="content" v-if="user">
        <view class="org-intro-warp">
            <view class="org-intro-box">
                <view class="oib-top-tit">
                    <view class="oibtt-l" v-if="orgData">
                        <image src="/src/images/icon/building-4-line.png"></image>
                        <text>{{orgData.name}}</text>
                    </view>
                    <view class="oibtt-r" v-if="orgList.length > 1" @click="this.$refs.popup.open('bottom')">
                        <image src="/src/images/icon/exchange-line.png"></image>
                    </view>
                </view>
                <view class="oib-cnt">
                    <!-- 如果机构没有课程包，不显示此按钮 -->
                    <view class="oibc-item" @click="goPackages()">
                        <image src="/src/images/icon/org_icon_04.png"></image>
                        <text>课程包</text>
                    </view>
                    <view class="oibc-item" @click="goCourse()">
                        <image src="/src/images/icon/org_icon_01.png"></image>
                        <text>课程</text>
                    </view>
                    <view class="oibc-item" @click="goTopics()">
                        <image src="/src/images/icon/org_icon_02.png"></image>
                        <text>题库</text>
                    </view>
                    <view class="oibc-item" @click="this.$refs.contact.open('center')">
                        <image src="/src/images/icon/org_icon_03.png"></image>
                        <text>客服</text>
                    </view>
                </view>
            </view>
        </view>


        <view class="org-class-subject">
            <view class="ocs-item" v-for="(item, index) in trains" :key="index">
                <view class="ocsi-course" v-if="item.course">
                    <template >
                        <view class="ocsic-tit">待学习课程</view>
                        <view class="ocsic-data">
                            <view class="ocscid-h">{{ item.course.study_progress.study_hour }}<text> / {{ item.course.hour }}学时</text></view>
                        </view>
                        <view class="ocsic-main" @click="goVideo(item)">
                            <view class="ocsicm-l">
                                <image :src="item.course.content.cover_src"></image>
                                <view class="ocsicm-l-main">
                                    <view class="ocsicml-tit">{{item.course.course_name}}</view>
                                    <view class="ocsicml-txt">共 {{ item.course.chapters_count }} 章 · {{item.course.sections_count}} 节 · {{item.course.learning_count + item.course.learning_count_add}}人在学</view>
                                    <view class="ocsicml-progress">已学 {{ item.course.study_progress.study_percent}}%</view>
                                </view>
                            </view>
                            <view class="ocsicm-r">
                                <text>去学习</text>
                                <image src="/src/images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                    </template>
                </view>
                <view class="ocsi-course" v-if="item.course_pack">
                    <template >
                        <view class="ocsic-tit">待学习课程</view>
                        <view class="ocsic-data">
                            <view class="ocscid-h">{{ item.course_pack.study_progress.study_hour }}<text> / {{ item.course_pack.hour }}学时</text></view>
                        </view>
                        <view class="ocsic-main" @click="goPackVideo(course, item)" v-for="(course, courseIndex) in item.course_pack.courses" :key="courseIndex">
                            <view class="ocsicm-l">
                                <image :src="course.cover_src"></image>
                                <view class="ocsicm-l-main">
                                    <view class="ocsicml-tit">{{course.title}}</view>
                                    <view class="ocsicml-txt">共  {{course.resource.chapters_count}} 章 · {{course.resource.sections_count}} 节 · {{course.resource.learning_count }}人在学</view>
                                    <view class="ocsicml-progress">已学 {{ course.resource.progress_percent }}%</view>
                                </view>
                            </view>
                            <view class="ocsicm-r">
                                <text>去学习</text>
                                <image src="/src/images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                    </template>
                </view>
                <view class="ocsi-course item-ques"  v-if="item.topic">
                    <view class="ocsic-tit">待练习题库</view>
                    <view class="ocsic-main" @click="goPage(item, 'topic')">
                        <view class="ocsicm-l">
                            <view class="ocsicml-box">
                                <view class="ocsicml-tit">{{ item.topic.name }}</view>
                                <view class="ocsicml-txt">共{{item.topic.subject_count}}题</view>
                            </view>
                        </view>
                        <view class="ocsicm-r">
                            <view class="ocsicm-r">
                                <text>去练习</text>
                                <image src="/src/images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="ocsi-course" v-if="item.exam">
                    <view class="ocsic-tit">待参加考试</view>
                        <view class="ocsic-data" v-if="item.exam.exam_taken">
                            <view class="ocscid-h">{{  item.exam.score }}<text>分 · {{item.exam.score >= item.exam.pass_score ? '及格' : '不及格'}}</text></view>
                        </view>
                        <view class="ocsic-main" @click="goExam(item.exam)">
                            <view class="ocsicm-l">
                                <view class="ocsicm-l-main">
                                    <view class="ocsicml-tit">{{item.exam.topic.name}}</view>
                                    <view class="ocsicml-txt">
                                        <text>{{item.exam.exam_limit_count == -1 ? '无限次' : item.exam.exam_limit_count + '次'}}</text> ·
                                        <text>{{item.exam.exam_time == -1 ? '随时考' : item.exam.exam_time }}</text> ·
                                        <text v-if="item.exam.exam_mode == 'pc_only'">仅电脑端考试</text>
                                        <text v-if="item.exam.exam_mode == 'mobile_only'">仅手机端</text>
                                        <text v-if="item.exam.exam_mode == 'all'">不限制</text>
                                    </view>
                                </view>
                            </view>
                            <view class="ocsicm-r">
                                <text>去考试</text>
                                <image src="/src/images/icon/arrow-right-s-line.png"></image>
                            </view>
                        </view>
                </view>
            </view>
        </view>
        <view class="no-data-nomal-box ndnb-cal" v-if="!loading && !trains.length">
            <view class="ndnb-icon">
                <image src="@/images/empty.png" mode="widthFix"></image>
            </view>
            <view class="no-work-tit">非常抱歉，暂无进行中的事项</view>
            <view class="nwt-contact" @click="this.$refs.contact.open('center')">咨询老师</view>
        </view>

        <!-- 底部导航 -->
        <view class="orgfn-eb"></view>
        <view class="org-foot-nav">
            <navigator open-type="redirect" url="/packageOrg/organization/enroll/index" class="orgfn-item" v-if="orgData && orgData.enable_enroll">
                <image src="/src/images/icon/account-box-edit-outline.png"></image>
                <text>报名</text>
            </navigator>
            <navigator open-type="redirect" url="/packageOrg/organization/home" class="orgfn-item cur">
                <image src="/src/images/icon/graduation-cap-fill.png"></image>
                <text>培训</text>
            </navigator>
            <navigator open-type="redirect" url="/packageOrg/organization/me" class="orgfn-item">
                <image src="/src/images/icon/user-4-line.png"></image>
                <text>我的</text>
            </navigator>
        </view>


        <!-- 切换机构 -->
        <uni-popup ref="popup"  type="bottom" :safe-area="false">
            <view class="change-org-box">
                <view class="cob-top">切换机构</view>
                <view class="cob-org-list">
                    <view class="cobol-item" v-for="item in orgList" @click="goOrg(item)" :key="item.sid">
                        <view class="coboli-l">
                            <image src="/src/images/icon/building-4-line.png"></image>
                            <text>{{item.name}}</text>
                        </view>
                        <view class="coboli-r">
                            <image src="/src/images/icon/arrow-right-s-line.png"></image>
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>


        <!-- 联系客服 -->
        <uni-popup ref="contact" type="center">
            <view class="contact-box">
                <view class="cb-tit">联系客服</view>
                <view class="cb-content">
                    <view class="cbc-h">长按二维码，添加客服</view>
                    <view class="cbc-img" v-if="orgData">
                        <image :show-menu-by-longpress="true" :src="orgData.service_qrcode_url" />
                    </view>
                </view>
                <view class="cb-foot-close" @click="this.$refs.contact.close()">关闭</view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
    import api from "@/lib/api";
    import {alert, alertOrg} from "@/lib/utils";
    import { getUser } from "@/lib/login.js";

    import {mapActions, mapState} from "pinia";
    import {useUserStore} from "@/store/user";
    import {getAppName} from "@/lib/context";
    import urls from "@/lib/urls";
    export default {
        data() {
            return {
                org: {},
                trains: [],
                orgList: [],
                orgId: '',
                loading: true,
                appName: getAppName(),
            }
        },
        onPullDownRefresh() {
            this.initData()
        },
        onLoad(e) {
            console.log("onLoad", e);
            if (e.org_sid) {
               this.orgId = e.org_sid
               uni.setStorageSync("org_id", e.org_sid)
            } else if (e.q) {
                const sourceUrl = decodeURIComponent(e.q);
                const queryParams = {};
                const queryString = sourceUrl.split('?')[1];
                if (queryString) {
                    queryString.split('&').forEach(param => {
                        const [key, value] = param.split('=');
                        queryParams[key] = decodeURIComponent(value);
                    });
                }
                if (queryParams.org_sid) {
                    this.orgId = queryParams.org_sid
                    uni.setStorageSync("org_id", queryParams.org_sid)
                }
            }

            if (uni.getStorageSync("org_id")) {
               this.orgId = uni.getStorageSync("org_id")
            }
            console.log("最终 orgId:", this.orgId);
            // 先获取用户信息，再检查机构和报名状态
            getUser().then((e) => {
                if (e){
                    // 用户已登录，检查机构报名状态并决定是否跳转
                    this.checkEnrollmentAndRedirect()
                } else {
                    uni.reLaunch({
                        url: '/packageOrg/organization/login/index?org_sid=' + this.orgId
                    })
                }
            }).catch( (e) => {
                uni.reLaunch({
                    url: '/packageOrg/organization/login/index?org_sid=' + this.orgId
                })
            })
            uni.$on('topic_test_complete', ()=>{
                this.initData()
            })
            uni.$on('buy_topic_success', ()=>{
                this.initData()
            })
        },
        onUnload() {
            uni.$off("topic_test_complete")
            uni.$off("buy_topic_success")
        },
        computed: {
            ...mapState(useUserStore, ['user', 'orgData', 'loaded']),
        },
        watch: {
            user() {
                console.log(this.user)
                if (this.user) {
                    this.initData()
                }
            }
        },
        onShareAppMessage() {
            return {
                title: this.appName,
                path: "/packageOrg/organization/home?org_sid=" + this.orgId,
                imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
            };
        },
        methods: {
            ...mapActions(useUserStore,['logout', 'reload', 'getOrgData']),
            initData() {
                api.get(`orgs/${this.orgId}/my-train`).then(res => {
                    this.trains = res.trains
                    this.org = res.org
                    uni.setNavigationBarTitle({
                        title: res.org.name
                    })
                }).catch(err => {
                    alertOrg(err.message)
                }).finally(() => {
                    this.loading = false
                    uni.stopPullDownRefresh();
                })
                api.get(`orgs`).then(res => {
                    this.orgList = res
                })

            },

            // 检查报名状态并决定是否跳转
            checkEnrollmentAndRedirect() {
                console.log('检查机构报名状态并决定是否跳转...');

                // 获取机构数据
                this.getOrgData().then(orgData => {
                    console.log('机构数据:', orgData);

                    // 设置页面标题
                    uni.setNavigationBarTitle({
                        title: orgData.name
                    });

                    // 检查机构是否开启了报名模块
                    if (orgData.enable_enroll) {
                        console.log('机构已开启报名模块，检查用户报名状态...');
                        this.checkUserEnrollmentAndRedirect();
                    } else {
                        console.log('机构未开启报名模块，直接进入培训页面');
                        this.initData();
                    }
                }).catch(err => {
                    console.error('获取机构数据失败:', err);
                    // 如果获取机构数据失败，默认进入培训页面
                    this.initData();
                });
            },

            // 检查用户报名状态并决定是否跳转
            checkUserEnrollmentAndRedirect() {
                console.log('检查用户报名状态并决定是否跳转...');

                // 调用报名状态检查接口
                api.get(urls.enrollmentStatus(this.orgId)).then(res => {
                    console.log('用户报名状态:', res);

                    if (res.is_enrolled) {
                        console.log('用户已完成报名，进入培训页面');
                        this.initData();
                    } else {
                        console.log('用户未完成报名，直接跳转到报名页面');
                        // 使用 reLaunch 确保完全跳转，不保留当前页面
                        uni.reLaunch({
                            url: `/packageOrg/organization/enroll/index?orgId=${this.orgId}&isEnrolled=${res.is_enrolled}`
                        });
                    }
                }).catch(err => {
                    console.error('检查报名状态失败:', err);

                    // 如果接口调用失败，直接跳转到报名页面
                    console.log('接口调用失败，直接跳转到报名页面');
                    uni.reLaunch({
                        url: `/packageOrg/organization/enroll/index?orgId=${this.orgId}`
                    });
                });
            },

            goVideo(data) {
                uni.navigateTo({
                    url: `/pages/training/video/detail?sid=${data.course.content.sid}&org_sid=${this.orgId}&enroll_sid=${data.enroll_sid}&need_photo=${this.org.need_photo}`
                })
            },
            goPackVideo(data, item) {
                uni.navigateTo({
                    url: `/pages/training/video/detail?sid=${data.sid}&org_sid=${this.orgId}&enroll_sid=${item.enroll_sid}&need_photo=${this.org.need_photo}`
                })
            },
            goPage(data, type) {
                    let url = `/pages/training/exam/index?topic_id=${data.topic.sid}&org_sid=${this.orgId}&enroll_sid=${data.enroll_sid}`
                    console.log(url)
                    uni.navigateTo({
                        url: url
                    })

            },
            goExam(data) {
                uni.navigateTo({
                    url: "/packageOrg/organization/start-test?data=" + JSON.stringify(data)
                })
            },
            goOrg(data) {
                uni.setStorageSync("org_id", data.sid)
                this.orgId = data.sid
                // 切换机构后，需要检查新机构的报名状态并决定是否跳转
                this.checkEnrollmentAndRedirect()
                this.$refs.popup.close()
            },
            goTopics() {
                uni.navigateTo({
                    url: "/packageOrg/organization/topics"
                })
            },
            goCourse() {
                uni.navigateTo({
                    url: `/packageOrg/organization/course`
                })
            },
            goPackages() {
                uni.navigateTo({
                    url: `/packageOrg/organization/package?org_sid=${this.orgId}`
                })
            },
        },
    }
</script>

<style>
    page {
        background-color: #f3f3ff;
    }
    .org-intro-warp {
        padding: 12rpx;
        background-color: #e5e4fb;
        margin: 20rpx;
        border-radius: 12rpx;
    }
    .org-intro-box {
        background-color: #fff;
        border-radius: 12rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .oib-top-tit {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 90rpx;
        border-bottom: 1px solid #f3f3f3;
        padding: 0 20rpx;
    }
    .oib-top-tit image {
        width: 32rpx;
        height: 32rpx;
    }
    .oibtt-l {
        display: flex;
        align-items: center;
    }
    .oibtt-l text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }
    .ocsicml-progress {
        font-size: 24rpx;
        padding-top: 10rpx;
        color: rgb(248, 80, 80);
    }

    .oib-cnt {
        display: flex;
        align-items: center;
    }
    .oibc-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30rpx 0;
    }
    .oibc-item image {
        width: 80rpx;
        height: 80rpx;
    }
    .oibc-item text {
        padding-top: 16rpx;
        font-size: 24rpx;
    }
    .oibtt-r {
        width: 60rpx;
        height: 90rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
    .oibtt-r image {
        width: 38rpx;
        height: 38rpx;
    }


    /* 待办事项 */
    .org-class-subject {
        background-color: #fff;
        border-radius: 12rpx;
        margin: 0 20rpx 20rpx;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
    }
    .ocs-item {
        padding: 12rpx;
        background-color: #e5e4fb;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
    }
    .ocsi-course {
        background-color: #fff;
        border-radius: 12rpx;
        margin-bottom: 12rpx;
    }
    .ocsi-course:last-child {
        margin-bottom: 0;
    }
    .ocsic-tit {
        display: flex;
        align-items: center;
        padding: 20rpx 20rpx 10rpx;
        font-size: 24rpx;
    }
    .ocsic-data {
        padding: 20rpx;
    }
    .ocscid-h {
        font-weight: bold;
        font-size: 48rpx;
    }
    .ocscid-h text {
        padding-left: 10rpx;
        font-size: 24rpx;
        color: #999;
        font-weight: normal;
    }
    .ocscid-p {
        font-size: 24rpx;
        color: #999;
        padding-top: 8rpx;
    }

    .ocsic-main {
        padding: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #f3f3f3;
    }
    .ocsic-main:hover {
        background-color: #f3f3ff;
    }
    .ocsicm-l {
        flex: 1;
        display: flex;
        align-items: center;
    }
    .ocsicm-l image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 12rpx;
        vertical-align: top;
        margin-right: 20rpx;
    }
    .ocsicm-l-main {
        flex: 1;
    }
    .ocsicm-r {
        display: flex;
        align-items: center;
        padding-left: 20rpx;
    }
    .ocsicm-r image {
        width: 32rpx;
        height: 32rpx;
    }
    .ocsicm-r text {
        font-size: 24rpx;
        padding-right: 10rpx;
        color: #ea712e;
    }
    .ocsicmr-btn {
        font-size: 28rpx;
        height: 40rpx;
        line-height: 40rpx;
        background-color: #390ABC;
        color: #fff;
        padding: 6rpx 10rpx;
        border-radius: 12rpx;
    }
    .ocsicml-tit {
        font-size: 28rpx;
        font-weight: bold;
        line-height: 1.4;
    }
    .ocsicml-txt {
        font-size: 24rpx;
        padding-top: 10rpx;
        color: #999;
    }
    .item-ques .ocsicm-l {
        flex: 1;
        display: flex;
        align-items: flex-start;
    }
    .item-ques .ocsicm-l image {
        width: 64rpx;
        height: 64rpx;
    }
    .item-ques .ocsicm-l .ocsicml-box {
        flex: 1;
    }


    /* 底部导航 */
    .org-foot-nav {
        display: flex;
        align-items: center;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding-bottom: env(safe-area-inset-bottom);
        background-color: #fff;
        box-shadow: 0 5rpx 30rpx rgba(0,0,0,.04);
        z-index: 2;
    }
    .orgfn-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 20rpx 0;
    }
    .orgfn-item image {
        width: 48rpx;
        height: 48rpx;
    }
    .orgfn-item text {
        padding-top: 10rpx;
        font-size: 24rpx;
        color: #aaa;
    }
    .orgfn-item.cur text {
        color: #390ABC;
        font-weight: bold;
    }
    .orgfn-eb {
        height: 130rpx;
        padding-bottom: env(safe-area-inset-bottom);
    }


    /* 切换机构 */
    .change-org-box {
        background-color: #fff;
        border-radius: 12rpx 12rpx 0 0;
        padding-bottom: env(safe-area-inset-bottom);
    }
    .cob-top {
        height: 100rpx;
        text-align: center;
        line-height: 100rpx;
    }

    .cobol-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100rpx;
        padding: 0 30rpx;
        border-top: 1px solid #f3f3f3;
    }
    .coboli-l {
        flex: 1;
        display: flex;
        align-items: center;
    }
    .cobol-item image {
        width: 32rpx;
        height: 32rpx;
    }
    .cobol-item text {
        padding-left: 20rpx;
        font-size: 28rpx;
    }


    /* 联系客服弹窗 */
    .contact-box {
        background-color: #fff;
        border-radius: 20rpx;
        width: 600rpx;
    }
    .cb-tit {
        height: 100rpx;
        line-height: 100rpx;
        text-align: center;
        font-weight: bold;
    }
    .cbc-h {
        text-align: center;
        height: 60rpx;
        line-height: 60rpx;
    }
    .cbc-img {
        padding: 30rpx;
        width: 540rpx;
    }
    .cbc-img image {
        width: 540rpx;
    }
    .cb-foot-close {
        text-align: center;
        height: 100rpx;
        background-color: #f3f3f3;
        color: #999;
        font-size: 28rpx;
        line-height: 100rpx;
        border-radius: 0 0 20rpx 20rpx;
    }


    /* 无数据情况 */
    .ndnb-cal {
        padding-bottom: 40rpx;
    }
    .no-work-tit {
        margin-top: -140rpx;
        padding-bottom: 40rpx;
        font-size: 28rpx;
    }
    .nwt-contact {
        width: 200rpx;
        height: 80rpx;
        text-align: center;
        line-height: 80rpx;
        border-radius: 90rpx;
        margin: 0 auto;
        font-size: 28rpx;
        font-weight: bold;
        background-color: #fff;
        border: 6rpx solid #e5e4fb;
    }
</style>