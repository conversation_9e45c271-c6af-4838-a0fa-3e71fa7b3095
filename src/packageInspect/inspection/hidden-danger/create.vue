<template>
    <view class="container">
      <view class="form-container">
        <view class="form-item">
          <view class="label">
            <text>现场照片</text>
            <text class="required">*</text>
            <text class="photo-tip">(最多9张)</text>
          </view>
          <view class="photo-upload-area">
            <view class="photo-item" v-for="(image, index) in formData.images" :key="index">
              <image :src="image" mode="aspectFill" @click="previewImage(index)"></image>
              <view class="delete-icon" @click.stop="deleteImage(index)">×</view>
            </view>
            <view class="upload-btn" @click="chooseImage" v-if="formData.images.length < 9">
              <view class="plus-icon">+</view>
              <text class="upload-text">上传图片</text>
            </view>
          </view>
        </view>

        <view class="form-item">
          <view class="label">
            <text>隐患描述</text>
            <text class="required">*</text>
          </view>
          <textarea class="textarea" type="text" placeholder="请输入隐患描述" v-model="formData.question" />
        </view>

        <view class="form-item">
          <view class="label">处理建议</view>
          <textarea class="textarea" type="text" placeholder="请输入处理建议" v-model="formData.suggestion" />
        </view>
      </view>

      <view class="action-buttons" v-if="!isSubmit">
        <button class="action-btn submit-btn" @click="submitToLobby">发布到大厅</button>
        <button class="action-btn transfer-btn" @click="transferToLeader">转发给领导</button>
      </view>

      <view class="action-buttons" v-else>
        <button class="action-btn submit-btn" @click="goBack">返回</button>
      </view>

    </view>
    <canvas
      canvas-id="watermarkCanvas"
      :width="canvasWidth"
      :height="canvasHeight"
      :style="`position:absolute;left:-9999px;width:${canvasWidth}px;height:${canvasHeight}px;`"
    ></canvas>

    <uni-popup ref="popup" type="dialog" :mask-click="false">
      <view class="custom-modal">
        <view class="modal-header">
          <view class="close-btn" @click="closeModal">×</view>
          <text class="modal-title">分享给领导</text>
          <text class="modal-subtitle">请点击下方按钮将隐患信息分享给领导审批</text>
        </view>
        <view class="button-group">
          <button v-if="needShare" class="share-btn" open-type="share" @click="closeModal">
            <text>立即分享</text>
          </button>
        </view>
      </view>
    </uni-popup>

  </template>

  <script>
  import urls from '@/lib/urls.js';
  import api from '@/lib/api.js';
  import { useUserStore } from '@/store/user.js';
  import { mapState } from 'pinia';
  import { BMapWX } from '@/lib/map/bmap-wx.js';
  export default {
    data() {
      return {
        formData: {
          question: '',
          suggestion: '',
          images: [],
          is_public: 0,
          longitude: '',
          latitude: ''
        },
        uploading: false,
        canvasWidth: 300,
        canvasHeight: 300,
        needShare: false, // 是否需要分享
        shareId: null,    // 分享用id
        isSubmit: false, // 是否提交
        locationAccuracy: '', // 新增：定位精度
        locationAddress: '', // 新增：详细地址
        bmap: null, // 新增：百度地图实例
        baiduMapConfig: null
      }
    },

    computed: {
        ...mapState(useUserStore, ["user"]),
    },

    onLoad() {
      // 初始化空数组
      this.formData.images = [];
      // 初始化百度地图实例
        api.get(urls.baiduMapKey).then(res => {
            console.log("获取地图配置成功", res)
            this.baiduMapConfig = res;
            this.bmap = new BMapWX(this.baiduMapConfig);
            // 页面加载即定位
            this.initLocation();
        }).catch(err => {
            console.log("获取地图配置失败", err)
        })
    },
    onShareAppMessage() {
      let id = this.shareId;
      if (this.needShare && this.shareId) {
        return {
          title: this.user.nickname + ' 提交的审批',
          path: `/packageInspect/inspection/hidden-danger/detail?id=${id}&type=mine`,
          imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png",
        }
      }
    },
    methods: {
      showShareModal() {
        this.$refs.popup.open();
      },

      closeModal() {
        this.$refs.popup.close();
      },

      goBack() {
        uni.navigateBack();
      },

      async initLocation() {
        try {
          await this.getLocation();
          // 获取详细地址
          if (this.formData.longitude !== '未知' && this.formData.latitude !== '未知') {
            try {
              this.locationAddress = await this.bmap.getAddressByLocation(this.formData.latitude, this.formData.longitude);
            } catch (e) {
              this.locationAddress = '';
            }
          }
        } catch (e) {
          this.locationAddress = '';
        }
      },
      async getLocation() {
        return new Promise((resolve, reject) => {
          uni.authorize({
            scope: 'scope.userLocation',
            success: () => {
              uni.getLocation({
                type: 'gcj02',
                altitude: true,
                isHighAccuracy: true,
                highAccuracyExpireTime: 3500,
                success: res => {
                  console.log("定位", res)
                  this.formData.longitude = res.longitude;
                  this.formData.latitude = res.latitude;
                  this.locationAccuracy = res.accuracy ? res.accuracy : '';
                  resolve(res);
                },
                fail: err => {
                  console.log(err)
                  uni.showToast({ title: '获取定位失败,请打开手机定位', icon: 'none' });
                  reject(err);
                }
              });
            },
            fail: () => {
              uni.showModal({
                title: '提示',
                content: '需要获取您的地理位置，请在设置中授权',
                showCancel: false,
                success: () => {
                  uni.openSetting();
                }
              });
              reject();
            }
          });
        });
      },
      async chooseImage() {
        if (this.formData.images.length >= 9) {
          uni.showToast({ title: '最多上传9张图片', icon: 'none' });
          return;
        }
        this.takePhoto();
      },
      takePhoto() {
        uni.chooseImage({
          count: 1,
          sourceType: ['camera'],
          success: res => {
            const filePath = res.tempFilePaths[0];
            this.addWatermark(filePath);
          }
        });
      },
      deleteImage(index) {
        this.formData.images.splice(index, 1);
      },
      previewImage(index) {
        uni.previewImage({
          urls: this.formData.images,
          current: index
        });
      },
      async addWatermark(filePath) {
        uni.showLoading({ title: '图片处理中...' });
        uni.getImageInfo({
          src: filePath,
          success: imgInfo => {
            // 中心等比压缩
            const maxWidth = 1284;
            const maxHeight = 2778;
            const scale = Math.min(maxWidth / imgInfo.width, maxHeight / imgInfo.height, 1);
            const drawWidth = Math.round(imgInfo.width * scale);
            const drawHeight = Math.round(imgInfo.height * scale);
            this.canvasWidth = drawWidth;
            this.canvasHeight = drawHeight;
            setTimeout(() => {
              const ctx = uni.createCanvasContext('watermarkCanvas', this);
              // 居中绘制
              const dx = 0;
              const dy = 0;
              ctx.drawImage(
                filePath,
                0, 0, imgInfo.width, imgInfo.height, // 源图全图
                dx, dy, drawWidth, drawHeight        // 目标canvas区域
              );
              // 水印内容
              const time = this.formatTime(new Date());
              const text1 = `时间: ${time}`;
              const text2 = `经度: ${this.formData.longitude}`;
              const text3 = `纬度: ${this.formData.latitude}`;
              const text4 = this.locationAddress ? `地址: ${this.locationAddress}` : '';
              ctx.setFontSize(20);
              const padding = 16;
              const lineHeight = 28;
              const texts = [text1, text2, text3];
              if (text4) texts.push(text4);
              const widths = texts.map(t => ctx.measureText(t).width);
              const bgWidth = Math.max(...widths) + padding * 2;
              const bgHeight = lineHeight * texts.length + padding * 2;
              const bgX = drawWidth - bgWidth - 20;
              const bgY = drawHeight - bgHeight - 20;
              ctx.setFillStyle('rgba(0,0,0,0.4)');
              ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
              ctx.setFillStyle('#fff');
              texts.forEach((t, i) => {
                ctx.fillText(t, bgX + padding, bgY + padding + lineHeight * (i + 0.8));
              });
              ctx.draw(false, () => {
                setTimeout(() => {
                  uni.canvasToTempFilePath({
                    canvasId: 'watermarkCanvas',
                    width: drawWidth,
                    height: drawHeight,
                    destWidth: drawWidth,
                    destHeight: drawHeight,
                    success: async res2 => {
                      try {
                        // 新增：加水印后如图片体积超4MB则自动压缩
                        uni.compressImage({
                          src: res2.tempFilePath,
                          quality: 40, // 可根据实际情况调整
                          success: async compressRes => {
                            uni.getImageInfo({
                              src: compressRes.tempFilePath,
                              success: async info2 => {
                                // 新增：打印压缩后图片大小
                                console.log('压缩后图片大小:', info2.size, '字节，约', (info2.size / 1024 / 1024).toFixed(2), 'MB');
                                if (info2.size && info2.size > 4 * 1024 * 1024) {
                                  uni.hideLoading();
                                  uni.showToast({ title: '图片过大，请重新拍摄', icon: 'none' });
                                  return;
                                }
                                uni.hideLoading();
                                uni.showLoading({ title: '图片上传中...' });
                                const url = await this.uploadImage(compressRes.tempFilePath);
                                this.formData.images.push(url);
                                uni.hideLoading();
                              },
                              fail: () => {
                                uni.hideLoading();
                                uni.showToast({ title: '图片信息获取失败', icon: 'none' });
                              }
                            });
                          },
                          fail: () => {
                            uni.hideLoading();
                            uni.showToast({ title: '图片压缩失败', icon: 'none' });
                          }
                        });
                      } catch (err) {
                        uni.hideLoading();
                        uni.showToast({ title: err.message || '图片上传失败', icon: 'none' });
                      }
                    },
                    fail: err => {
                      console.log(err);
                      uni.hideLoading();
                      uni.showToast({ title: '水印处理失败', icon: 'none' });
                    }
                  }, this);
                }, 500);
              });
            }, 100);
          },
          fail: () => {
            uni.hideLoading();
            uni.showToast({ title: '图片信息获取失败', icon: 'none' });
          }
        });
      },
      async uploadImage(filePath, retryCount = 0) {
        try {
          const res = await api.get(urls.getUploadConfig);
          return await new Promise((resolve, reject) => {
            const doUpload = (retryCount) => {
              uni.uploadFile({
                url: res.url,
                filePath: filePath,
                formData: res.form_params,
                name: res.name,
                success: uploadFileRes => {
                  let data = JSON.parse(uploadFileRes.data);
                  if (data?.url) {
                    resolve(data.url); // 只保存url
                  } else {
                    reject(new Error(data.message || '上传失败'));
                  }
                },
                fail: error => {
                  if (retryCount < 2) { // 最多重试3次
                    setTimeout(() => {
                      doUpload(retryCount + 1);
                    }, 1000 * (retryCount + 1)); // 递增延迟
                  } else {
                    reject(new Error(error.errMsg || '上传失败'));
                  }
                }
              });
            };
            doUpload(retryCount);
          });
        } catch (err) {
          throw new Error(err.message || '上传失败');
        }
      },
      submitToLobby() {
        this.formData.is_public = 1;
        this.validateAndSubmit();
      },
      transferToLeader() {
        this.formData.is_public = 0;
        this.validateAndSubmit();
      },
      validateAndSubmit() {
        if (!this.formData.question) {
          uni.showToast({ title: '请输入隐患描述', icon: 'none' });
          return;
        }
        if (this.formData.images.length === 0) {
          uni.showToast({ title: '请上传现场照片', icon: 'none' });
          return;
        }
        uni.showLoading({ title: '提交中...' });
        api.post(urls.createHiddenDanger, this.formData)
            .then(res => {
                uni.hideLoading();
                this.isSubmit = true;
                if (this.formData.is_public === 0) {
                    this.needShare = true;
                    this.shareId = res.id;
                    this.showShareModal();

                } else {
                    uni.showToast({ title: '提交成功', icon: 'success' });
                    setTimeout(() => {
                        uni.navigateTo({
                          url: "index"
                        })
                    }, 1500);
                }
            })
            .catch( err => {
                uni.hideLoading();
                this.isSubmit = false;
                console.log(err)
                uni.showToast({ title: err.message || '提交失败', icon: 'none', duration: 2500 });
            })
      },
      formatTime(date) {
        const y = date.getFullYear();
        const m = ('0' + (date.getMonth() + 1)).slice(-2);
        const d = ('0' + date.getDate()).slice(-2);
        const h = ('0' + date.getHours()).slice(-2);
        const min = ('0' + date.getMinutes()).slice(-2);
        const s = ('0' + date.getSeconds()).slice(-2);
        return `${y}-${m}-${d} ${h}:${min}:${s}`;
      }
    }
  }
  </script>

  <style>
  .container {
    padding: 0;
    font-family: PingFang SC, Helvetica Neue, Helvetica, Arial, sans-serif;
    background-color: #ffffff;
    min-height: 100vh;
    box-sizing: border-box;
    position: relative;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .form-container {
    padding: 16px;
    position: relative;
  }

  .form-item {
    margin-bottom: 24px;
  }

  .label {
    font-size: 15px;
    color: #333;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
  }

  .required {
    color: #ff4d4f;
    margin-left: 4px;
  }

  .input {
    width: 100%;
    height: 40px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 0 12px;
    font-size: 14px;
    box-sizing: border-box;
  }

  .textarea {
    width: 100%;
    height: 120px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
    box-sizing: border-box;
  }

  .photo-tip {
    font-size: 12px;
    color: #999;
    margin-left: 8px;
  }

  .photo-upload-area {
    display: flex;
    flex-wrap: wrap;
    margin: -6px;
  }

  .photo-item {
    width: 90px;
    height: 90px;
    margin: 6px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
  }

  .photo-item image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .delete-icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    border-bottom-left-radius: 4px;
  }

  .upload-btn {
    width: 90px;
    height: 90px;
    background-color: #f7f7f7;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 6px;
  }

  .btn-warning {
    background-color: #fada3d;
    color: white;
    height: 80rpx;
    line-height: 80rpx;
    font-size: 32rpx;
    border-radius: 8rpx;
  }

  /* Modal styles */
  .custom-modal {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    max-width: 80%;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    margin-bottom: 20px;
    text-align: center;
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #007aff;
    display: block;
    margin-bottom: 8px;
  }

  .modal-subtitle {
    font-size: 14px;
    color: #666;
    display: block;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    font-size: 20px;
    color: #999;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .close-btn:hover {
    color: #333;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    font-size: 20px;
    color: #999;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .close-btn:hover {
    color: #333;
  }

  .share-btn {
    background-color: #007aff;
    color: white;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 24px;
    margin: 0 auto;
    transition: all 0.3s;
  }

  .share-btn:active {
    background-color: #0062cc;
    transform: scale(0.98);
  }

  .share-btn .iconfont {
    color: white;
    font-size: 18px;
    margin-right: 8px;
  }

  .plus-icon {
    font-size: 28px;
    color: #bbb;
    margin-bottom: 8px;
    line-height: 1;
  }

  .upload-text {
    font-size: 12px;
    color: #999;
  }

  .action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  }

  .action-btn {
    flex: 1;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 4px;
    color: #fff;
    font-size: 15px;
    margin: 0 8px;
  }

  .submit-btn {
    background-color: #007aff;
  }

  .transfer-btn {
    background-color: #007aff;
  }

  /* For the icons */
  @font-face {
    font-family: "iconfont";
    src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAMMAAsAAAAABwQAAAK+AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgqBVIFGATYCJAMMCwgABCAFhG0HNhvhBciemjwJCCIUAODw4CRABNGatfd292YWQUUjkmgSzRNJNEKnkUwhFErHQ/X+13PXBZC3XQSKxhWOHfHlQ3HuMPcWV0C6PL889/+c4bLNBchvs7nEoog6oKOeQAOKKLIDeYd4C+MWLuNpAtpMCSOxVtLQBg0KcVgg7opHOTQMpYICjUJdsLCIJ1CpacoeAo/998t/GgwakmqGtdZdF6uB4m/it/3QnGlmQRDgdjbYHjKmAYW4LLS9RgeG09DW5j9iZxHQaBSUb9u3/f+fYZeK1v7lEZJMRAOsuA1McnS5rBKg4DedZJBR1bYWpQ97PwTQtDO8D2XlVVSUKMoYcCxLFMFYsUhgQhUagiWL+Iar1XiqeA88Rt8vv8URJTWZ1tLeeX0bFL+Fbzeui/Wc0N0Zyq4jYxooiB2F1h16QTuNpq0sZEsaVZL41nZK3a6o1xf/8JCIGtB6HCiQb8JRN4lvW0QkBtQhDvuALpwDOtQlHSf4wlJMIVJ70eVK1Py8LQsLlqwsOjGbtU8iWN/Ocy4uOjE9bZ9CuLkd51xYcGxqyj4pQptc7w8e4HDyQ6drFvZlN2/2AgMn0xbCbtsegb+00nLnnFtvUNcQsV0bvV3bfgQIdxcfDO3ulC1sDAfXh2k3JNz2S7kTEG7LgN/jvldqx3+NHwH8f3GV+Tx+rZrJAJ+qvgF8V9lQgN+2iUJl/dSgclJtqLaoTkyjrdZRd9+x2AZtOmTg0/4nXnoNSY1hZGpMyIozDVXaDKDQaB5tpnS3b9OJokCLScCUVwBCl9eQdHiPrMtnKMT5gkpDfkOjq5ho8x5Hi23GlCxKDMUZzFvoGboTk46VlD6i9F5H6ZxNgT+iLOyhoqKwcEoOGOMlGpXMoQgRKjhAL+A6DENJDwucUg7zlndS84wUxZlnqYQiDCocg3QLtA6yR1jy+SnS5yPk9LocarU7lviIZMLeA6VKteBprYrrSvHK9ZTkWByEIAQpUICeIHYwKEm2vAUUJ4mDLcxeT6FYX7oovr490B6/twfa4JLIUcKKn7H1ux4WAAAAAA==') format('woff2');
  }

  .iconfont {
    font-family: "iconfont" !important;
    font-size: 24px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #333;
  }
  </style>