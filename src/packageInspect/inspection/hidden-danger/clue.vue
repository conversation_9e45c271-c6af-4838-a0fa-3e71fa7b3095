<template>
    <view class="container">
<!--      <view class="back-icon">-->
<!--        <text class="iconfont">&#xe601;</text>-->
<!--      </view>-->

      <view class="title">信息填写</view>

      <view class="description">
        留下您的联系方式，我们会在5个工作日内联系您
      </view>

      <view class="form-group">
        <view class="form-item">
          <view class="label">姓名</view>
          <input class="input" type="text" placeholder="请输入" v-model="formData.name" />
        </view>

        <view class="form-item">
          <view class="label">手机号</view>
          <input class="input" type="number" placeholder="请输入" v-model="formData.phone" />
        </view>

        <view class="form-item">
          <view class="label">单位名称</view>
          <input class="input" type="text" placeholder="请输入" v-model="formData.company" />
        </view>
      </view>

      <view class="submit-btn" @click="submitForm">提交</view>
    </view>
  </template>

  <script>
  import api from "@/lib/api";
  import urls from "@/lib/urls";

  export default {
    data() {
      return {
        id: 0,
        formData: {
          name: '',
          phone: '',
          company: ''
        }
      }
    },
    onLoad(option) {
      this.id = option.id;
    },
    methods: {
      submitForm() {
        // Validation
        if (!this.formData.name) {
          uni.showToast({
            title: '请输入姓名',
            icon: 'none'
          });
          return;
        }

        if (!this.formData.phone) {
          uni.showToast({
            title: '请输入手机号',
            icon: 'none'
          });
          return;
        }

        if (!/^1\d{10}$/.test(this.formData.phone)) {
          uni.showToast({
            title: '请输入正确的手机号',
            icon: 'none'
          });
          return;
        }

        if (!this.formData.company) {
          uni.showToast({
            title: '请输入单位名称',
            icon: 'none'
          });
          return;
        }
        uni.showLoading({
          title: '提交中'
        });
        api.post(urls.addClue(this.id), this.formData)
            .then(res => {
                uni.showToast({
                    title: '提交成功',
                    icon: 'success',
                    success: () => {
                        setTimeout(() => {
                            uni.switchTab({url: '/pages/index/index'});
                        }, 2000);
                    }
                });
                this.formData = {
                    name: '',
                    phone: '',
                    company: ''
                };
            })
            .catch(res => {
                uni.showToast({
                  title: '提交失败',
                  icon: 'none'
                });
            })
            .finally(res =>  {
                uni.hideLoading();
            })
      }
    }
  }
  </script>

  <style>
  .container {
    padding: 20px;
    font-family: PingFang SC, Helvetica Neue, Helvetica, Arial, sans-serif;
    background-color: #ffffff;
    min-height: 100vh;
    box-sizing: border-box;
  }

  .back-icon {
    font-size: 24px;
    margin-bottom: 20px;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
    text-align: center;
    margin-bottom: 30px;
  }

  .description {
    font-size: 14px;
    color: #333;
    margin-bottom: 30px;
    line-height: 1.5;
  }

  .form-group {
    margin-bottom: 40px;
  }

  .form-item {
    margin-bottom: 20px;
  }

  .label {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
  }

  .input {
    width: 100%;
    height: 45px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 14px;
    box-sizing: border-box;
  }

  .submit-btn {
    background-color: #0086f6;
    color: white;
    height: 45px;
    line-height: 45px;
    text-align: center;
    border-radius: 4px;
    font-size: 16px;
  }

  .submit-btn:active {
    background-color: #0073d8;
  }

  /* For the back icon */
  @font-face {
    font-family: "iconfont";
    src: url('data:font/woff2;charset=utf-8;base64,d09GMgABAAAAAAKcAAsAAAAABnQAAAJPAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCCcApsgQ0BNgIkAwgLBgAEIAWEbQcwG7kFyJ6aPAFRVMkQyf7IYG4Mj0ANpVKtDm9Pz0QEX2Nf3vc8eWEckUQznU40SSzRDJFIJEKIJrPZvCB5mt7+BrKNwmxlG2eXZ2FUhFKEjumZLuJq1Da9KWwlm0LXKdxJH/Qo/BxS7H+OmS7tBPKDbFcX0XjVRr0A44ACHXtQZAVSIG8Yu8AlPCbQNG+E2MXT1w+iCnu1QJw7kYAoRVYoctyoUDfMLfE0UW+epA/AE/95/ENG1CSVsvaeg8uBHNr+JFo2bU/3EUwgAHqXQcYWUIiTxsyBKMJIUdO+KBgEX3XwJ2GL+aG/PiKugmwPLGHlJ1FJmgzxbCbmGsV5a6lrC1+4a5dpSxWHtgH4vM8rdwft6jcf+bB3Hy4GhwZ7lZWMtc/7X1yvQPPi/bPrq5wZeP+G6Gh79b0bIcz+WG9NiJLrYxZ11nj/j6UdgN/bVfSwGWrHXwsHIGrHJNRVYopQp9wBDU2KKmioO4emTdXVnW0sRKTGhk0QuvaSdL2T0bVvikL9S1Iz8E9D1yaHpi3o2bKpGxujOmEL2IdgGzJVCEcZovbENDNCXUWh5YkQPUckWihmcxkU3RiTbItQXxMjkjGm2g0d5zizTogiQpesqLrKsuILPK1KZQ/FsmmUALYD4WxQRiVCZueisff5DHGaCdJpUaHLJ1ToVt4gkdaQrMN8WsjXWmEj22Xr6dSoRaLOZJikZgw5XJwxK9UJQSGCXOTlVKoVM8svxLNSSso6KVYUjyWq28vtY/Nt3QBNutezSJGjRL0YLaXQbQBRFLlIkyy7AAA=') format('woff2');
  }

  .iconfont {
    font-family: "iconfont" !important;
    font-size: 24px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #333;
  }
  </style>