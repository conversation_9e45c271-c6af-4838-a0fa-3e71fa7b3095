<template>
  <view class="nav-bar">
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    <view class="nav-content">
      <view class="left">
        <view class="back" v-if="showBack" @click="goBack">
          <image src="@/static/images/icon/arrow-left-s-line.png"></image>
        </view>
      </view>
      <view class="center">
        <view class="tabs">
          <view 
            class="tab-item" 
            v-for="(item, index) in tabs" 
            :key="index" 
            :class="{ active: currentTab === index }"
            @click="changeTab(index)"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <view class="right">
        
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomNavbar',
  props: {
    tabs: {
      type: Array,
      default: () => ['我的', '大厅']
    },
    currentTab: {
      type: Number,
      default: 0
    },
    showBack: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      statusBarHeight: 20
    }
  },
  created() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  methods: {
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    changeTab(index) {
      this.$emit('change', index)
    }
  }
}
</script>

<style>
.nav-bar {
  width: 100%;
  background-color: #F3F3FF;
  position: fixed;
  top: 0;
  z-index: 999;
}

.fallback-icon {
  display: inline-block;
  margin-left: 5px;
  font-size: 20px;
}
.nav-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}
.left {
  width: 76rpx;
}
.back {
  font-size: 20px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back image {
	width: 64rpx;
	height: 64rpx;
}

.center {
  flex: 1;
  text-align: center;
}
.tabs {
  display: flex;
  justify-content: center;
}
.tab-item {
  padding: 0 15px;
  position: relative;
  font-size: 16px;
  color: #333;
}
.tab-item.active {
  color: #000;
  font-weight: bold;
}
.tab-item.active:after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #000;
}
.right {
  width: 60px;
  display: flex;
  justify-content: space-between;
}
.dot, .refresh {
  font-size: 20px;
}
</style>