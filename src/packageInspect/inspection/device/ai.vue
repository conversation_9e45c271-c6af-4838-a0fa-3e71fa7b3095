<template>
  <view class="container">
    <view class="camera-container">
      <view @click="takePhoto">
        <view class="camera-icon">
            <uni-icons type="camera" size="60" color="#2b9939"></uni-icons>
        </view>
        <view class="hint-text">
            对准设备拍照后，AI会自动生成巡检项
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api";
import urls  from "@/lib/urls";

export default {
  data() {
    return {}
  },
  methods: {
    async takePhoto() {
        console.log(111);
        uni.chooseMedia({
            count: 1,
            sourceType: ['camera'],
            mediaType: ['image'],
            success: async res => {
                console.log(1+res);
                await this.uploadAllFiles(res.tempFiles);
            }
        })
    },
    initData(){
        return new Promise((resolve, reject) => {
            api.get(urls.getUploadConfig).then(res => {
                this.uploadForm = res
                resolve()
            }).catch(err => {
                alert(err.message)
                reject()
            })
        })
    },

    judgeSize(size) {
        const maxSizeMB = 10 // 最大5MB
        size = size / 1024 / 1024 // 转换为MB
        if (size >= maxSizeMB) {
            return `图片大小不能超过${maxSizeMB}M`
        }
        return false
    },

    async uploadAllFiles(res) {
        console.log(22222);
        await this.initData();

        const file = res[0];
        const filePath = file?.path || file?.tempFilePath;
        if (filePath) {
            this.uploadFile(filePath);
        }
    },

    async uploadFile(path) {
        uni.showLoading({
            title: '上传中...',
            mask: true
        });
        
        try {
            const uploadFileRes = await uni.uploadFile({
                url: this.uploadForm.url,
                filePath: path,
                formData: this.uploadForm.form_params,
                name: this.uploadForm.name
            });

            let data = JSON.parse(uploadFileRes.data);
            if (data?.url) {
                uni.hideLoading(); // 先关闭上传loading
                uni.showToast({
                    title: '上传成功',
                    icon: 'success',
                    duration: 1500
                });

                // 短暂延迟后显示AI识别loading
                setTimeout(() => {
                    uni.showLoading({
                        title: 'AI识别中...',
                        mask: true
                    });
                    
                    // 调用ai识别图片
                    api.get(urls.aiIdentify, { image_url: data.url })
                    .then((res) => {
                        uni.hideLoading();
                        uni.showToast({
                            title: '识别完成',
                            icon: 'success',
                            duration: 1500
                        });
                        
                        // 短暂延迟后显示跳转loading
                        setTimeout(() => {
                            uni.showLoading({
                                title: '正在跳转...',
                                mask: true
                            });
                            
                            uni.navigateTo({
                                url: `/packageInspect/inspection/device/ai-create?data=${JSON.stringify(res)}&imageUrl=${data.url}`,
                                complete: () => {
                                    uni.hideLoading();
                                }
                            });
                        }, 300);
                    }).catch(err => {
                        uni.hideLoading();
                        uni.showToast({
                            title: err.message || 'AI识别失败',
                            icon: 'none'
                        });
                    });
                }, 300);
            } else {
                uni.hideLoading();
                uni.showToast({
                    title: '上传失败: 无效的响应数据',
                    icon: 'none'
                });
            }
        } catch (error) {
            uni.hideLoading();
            uni.showToast({
                title: '上传失败: ' + (error.message || '网络错误'),
                icon: 'none'
            });
        }
    },
  }
}
</script>

<style>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #ffffff;
}

.camera-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.camera-icon {
  margin-bottom: 20px;
}

.hint-text {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
}
</style>