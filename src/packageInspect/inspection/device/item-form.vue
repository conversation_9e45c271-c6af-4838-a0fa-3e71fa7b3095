<template>
  <view class="container">
    <view class="content">
      <view class="section-title">请添加巡检项</view>
      
      <view class="search-box">
        <input 
          type="text" 
          v-model="searchText" 
          placeholder="请输入巡检项名称" 
          class="search-input" 
          @confirm="addItem"
        />
        <text v-if="searchText" class="clear-icon" @tap="clearSearch">×</text>
      </view>
      
      <view class="add-item-btn" @tap="addItem">
        <text class="add-icon">+</text>
        <text class="add-text">添加巡检项</text>
      </view>
      
      <view class="item-list">
        <view 
          v-for="item in items" 
          :key="item.id" 
          class="item"
          @tap="removeItem(item.id)"
        >
          <text class="item-name">{{item.name}}</text>
          <text class="delete-icon">×</text>
        </view>
        <view v-if="items.length === 0" class="empty-tip">
          暂无巡检项，请添加
        </view>
      </view>
    </view>
    
    <view class="footer">
      <button 
        class="save-btn" 
        @tap="saveData"
        :disabled="items.length === 0"
      >
        保存
      </button>
    </view>
  </view>
</template>

<script>
import api from "@/lib/api";
import urls from "@/lib/urls";

export default {
  data() {
    return {
      mode: '',
      searchText: '',
      items: [],
      deviceId: null
    }
  },
  onLoad(options) {
    console.log(options)
    if (options.device_id) {
      this.deviceId = options.device_id;
      this.getItems();
    }
  },
  
  methods: {
    getItems() {
      api.get(urls.deviceItemListByDeviceID(this.deviceId)).then(res => {
        if (res && res.length > 0) {
          uni.setNavigationBarTitle({
            title: '编辑巡检项'
          });

          this.items = res.map(item => ({
            id: item.id || Date.now(),
            name: item.name
          }));
        }
      });
    },
    clearSearch() {
      this.searchText = '';
    },
    addItem() {
      if (this.searchText && this.searchText.trim() !== '') {
        const itemName = this.searchText.trim();
        const isDuplicate = this.items.some(item => item.name === itemName);
        
        if (isDuplicate) {
          uni.showToast({
            title: '已存在同名巡检项',
            icon: 'none'
          });
          return;
        }
        
        this.items.push({
          id: Date.now(),
          name: itemName
        });
        this.searchText = '';
      } else {
        uni.showToast({
          title: '请输入巡检项名称',
          icon: 'none'
        });
      }
    },
    removeItem(id) {
      if (this.items.length <= 1) {
        uni.showToast({
          title: '至少需要保留一个巡检项',
          icon: 'none'
        });
        return;
      }
      this.items = this.items.filter(item => item.id !== id);
    },
    async saveData() {
      if (!this.deviceId) {
        uni.showToast({
          title: '设备ID缺失',
          icon: 'none'
        });
        return;
      }
      
      if (this.items.length === 0) {
        uni.showToast({
          title: '至少需要添加一个巡检项',
          icon: 'none'
        });
        return;
      }
      
      try {
        uni.showLoading({
          title: '保存中',
          mask: true
        });
        
        // 调用API保存数据
        const data = {
          device_id: this.deviceId,
          names: this.items.map(item => item.name)
        };
        
        api.post(urls.deviceItemList, data).then(res => {
          uni.hideLoading();

          uni.showToast({
            title: '保存成功',
            icon: 'success'
          });

          uni.$emit('refreshDeviceList');
          setTimeout(() => {
            const pages = getCurrentPages();
            // 检查页面栈中是否有设备详情页
            const hasDetailPage = pages.some(page => 
              page.route.includes('packageInspect/inspection/device/detail')
            );
            // 如果有详情页则返回3步，否则返回2步
            const delta = hasDetailPage ? 3 : 2;
            uni.navigateBack({ delta });
          }, 1000);

        }).catch(err => {
          uni.hideLoading();
          uni.showToast({
            title: err.message || '保存失败',
            icon: 'none'
          });
        })

      } catch (error) {
        uni.hideLoading();
        // 这里捕获的是api.post抛出的错误
        uni.showToast({
          title: error.message || '保存过程中发生错误',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
}

.title {
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  flex: 1;
}

.content {
  flex: 1;
  padding: 16px;
}

.section-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
}

.search-box {
  position: relative;
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
  height: 44px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  padding: 0 40px 0 16px;
  font-size: 14px;
  box-sizing: border-box;
  transition: all 0.3s;
  background-color: #fafafa;
}

.search-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  outline: none;
  background-color: #fff;
}

.clear-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  color: #999;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  transition: color 0.3s;
}

.clear-icon:active {
  color: #666;
}

.add-item-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  border: 1px solid #1890ff;
  border-radius: 8px;
  margin-bottom: 16px;
  color: #1890ff;
  background-color: #f0f9ff;
  transition: all 0.3s;
}

.add-item-btn:active {
  background-color: #e6f7ff;
  transform: translateY(1px);
}

.add-icon {
  font-size: 16px;
  margin-right: 4px;
}

.add-text {
  font-size: 14px;
}

.item-list {
  flex: 1;
  margin-top: 12px;
}

.item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #f0f9ff;
  border: 1px solid #d0e8ff;
  border-radius: 6px;
  margin-bottom: 10px;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.item:active {
  background-color: #e0f0ff;
  transform: translateY(1px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item-name {
  font-size: 14px;
  color: #333;
}

.delete-icon {
  color: #999;
  font-size: 18px;
  padding: 4px;
}

.empty-tip {
  text-align: center;
  color: #999;
  font-size: 14px;
  margin-top: 40px;
}

.footer {
  padding: 16px;
  padding-bottom: 34px; /* Add safe area for notch phones */
}

.save-btn {
  width: 100%;
  height: 44px;
  background-color: #1890ff;
  color: white;
  border-radius: 4px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.save-btn[disabled] {
  background-color: #ccc;
  opacity: 0.7;
}
</style>