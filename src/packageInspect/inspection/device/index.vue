<template>
  <view class="device-list-container">
    <!-- Search bar -->
    <view class="search-bar">
      <view class="search-input">
        <input type="text" placeholder="请输入" v-model="searchText" @confirm="searchDevices" @input="filterEquipment"/>
      </view>
      <view class="search-button" @click="searchDevices">
        <text>搜索</text>
      </view>
      <view class="add-button" @click="navigateTo('device/form')">
        <text>+</text>
      </view>
    </view>
    
    <!-- Device list -->
    <view class="device-list">
      <view class="device-item" v-for="(item, index) in deviceList" :key="index" @click="navigateTo('device/detail?id='+item.id)">
        <view class="device-image">
          <image v-if="item.image_url" :src="item.image_url" mode="aspectFit" @error="handleImageError"></image>
          <image v-else src="@/images/empty.png" mode="aspectFit" style="max-width:100%;max-height:100%" @error="handleImageError"></image>
        </view>
        <view class="device-info">
          <view class="device-name">{{ item.name }}</view>
          <view class="device-details">
            <text>巡检项: {{ item.inspection_item_count }}</text>
          </view>
          <view class="device-details">
            <text>备注: {{ item.remark}}</text>
          </view>
        </view>
      </view>

      <view v-if="deviceList.length === 0" class="empty-tasks">
        <text class="empty-text">没找到设备 ~</text>
        <button class="create-task-btn" @click="navigateTo('device/form')">去添加</button>
      </view>

    </view>
    
    <!-- AI assistant message -->
    <movable-area class="movable-area">
      <movable-view class="ai-assistant" direction="all" :x="dragX" :y="dragY" @change="onDragChange" @touchend="saveDragPosition">
        <view class="ai-message" @click="navigateTo('device/ai')">
          <text class="ai-label">AI</text>
          <view class="ai-info">
            <text>对设备不熟悉？</text><br>
            <text>试试AI创建</text>
          </view>
        </view>
      </movable-view>
    </movable-area>
  </view>
</template>

<script>
import api from "@/lib/api";
import urls from "@/lib/urls";

export default {
  data() {
    return {
      deviceList: [],
      searchText: '',
      debounceTimer: null,
      dragX: 0,
      dragY: 0,
      dragTimer: null // 新增拖拽防抖计时器
    }
  },

  onLoad() {
      // 如果没有保存的位置，初始化在右下角
      const windowInfo = uni.getWindowInfo();
      this.dragX = windowInfo.windowWidth - 120; // 120px from right
      this.dragY = windowInfo.windowHeight - 220; // 220px from bottom
      
      this.getDevices();
      uni.$on('refreshDeviceList', this.getDevices);
  },
  onUnload() {
    uni.$off('refreshDeviceList', this.getDevices);
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
  },
  onPullDownRefresh() {
    this.getDevices().finally(() => {
      uni.stopPullDownRefresh();

			setTimeout(() => {
        uni.showToast({
          title: '刷新成功',
          icon: 'success'
        });
      }, 300);
    });
  },

  methods: {
    navigateTo(page) {
      uni.navigateTo({
        url: '/packageInspect/inspection/' + page
      });
    },

    filterEquipment() {
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer);
      }
      this.debounceTimer = setTimeout(async () => {
        this.getDevices();
      }, 300);
    },

    getDevices() {
      const keyword  = this.searchText.trim();
      const params = keyword  ? {keyword} : {};
      return api.get(urls.deviceList, params).then(res => {
        this.deviceList = res.data;
      });
    },

    searchDevices() {
      this.getDevices();
    },
    addDevice() {
    },
    goBack() {
      uni.navigateBack();
    },
    handleImageError(e) {
      console.error('图片加载失败:', e);
      // 可以在这里添加更多错误处理逻辑
    },
    onDragChange(e) {
      // 清除之前的计时器
      if (this.dragTimer) {
        clearTimeout(this.dragTimer);
      }
      
      // 使用防抖优化拖拽体验
      this.dragTimer = setTimeout(() => {
        uni.nextTick(() => {
          this.dragX = e.detail.x;
          this.dragY = e.detail.y;
        });
      }, 50); // 50ms防抖延迟
    },
    
    saveDragPosition() {
      // 清除防抖计时器
      if (this.dragTimer) {
        clearTimeout(this.dragTimer);
        this.dragTimer = null;
      }
    }
  }
}
</script>

<style>
.device-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #ffffff;
  position: relative;
}

.header {
  display: flex;
  height: 44px;
  align-items: center;
  padding: 0 15px;
  position: relative;
}

.left {
  width: 30px;
}

.center {
  flex: 1;
  text-align: center;
}

.right {
  display: flex;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: 500;
}

.more-icon, .plus-icon {
  font-size: 20px;
  margin-left: 15px;
}

.search-bar {
  display: flex;
  padding: 10px 15px;
  align-items: center;
}

.search-input {
  flex: 1;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 15px;
  margin-right: 10px;
  border: none;
  font-size: 14px;
}

.search-input input {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
  outline: none;
}

.search-button {
  background-color: #007AFF;
  color: white;
  padding: 5px 15px;
  border-radius: 5px;
  margin-right: 10px;
}

.add-button {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: white;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.device-list {
  flex: 1;
  overflow-y: auto;
}

.device-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.device-image {
  width: 60px;
  height: 60px;
  background-color: #f5f5f5;
  margin-right: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.device-image image {
  max-width: 100%;
  max-height: 100%;
}

.device-icon {
  font-size: 24px;
}

.device-info {
  flex: 1;
}

.device-name {
  font-size: 16px;
  margin-bottom: 5px;
}

.device-details {
  font-size: 14px;
  color: #666;
  margin-bottom: 3px;
}

.movable-area {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ai-assistant {
  pointer-events: auto;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  width: 100px; /* 固定宽度 */
  height: 120px; /* 固定高度 */
}

.ai-label {
  padding: 8px 20px;
  font-size: 24px;
  text-align: center;
  border-radius: 8px;
  color: #4CAF50;
  background-color: rgba(224, 247, 224, 0.8);
  margin-bottom: 5px;
}

.ai-info {
  background-color: rgba(224, 247, 224, 0.5);
  border: 1px solid #e0f7e0;
  border-radius: 8px;
  padding: 8px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  text-align: center;
}

.empty-tasks {
  margin-top: 50px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300rpx;
  padding: 0;
}

.create-task-btn {
  margin-top: 20px;
  background-color: #3388ff;
  color: #ffffff;
  padding: 12rpx 32rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin-bottom: 18rpx;
  border: none;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  color: #999;
}
</style>