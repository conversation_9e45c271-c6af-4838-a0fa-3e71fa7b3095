<template>
  <view class="container">
    <!-- Hidden canvas for watermark -->
    <canvas
      canvas-id="watermarkCanvas"
      :style="{width: canvasWidth + 'px', height: canvasHeight + 'px', position: 'fixed', top: '-9999px', left: '-9999px'}"
    ></canvas>
    <!-- Inspection form -->
    <view class="inspection-form">

      <!-- Current photo section -->
      <view class="photo-section">
        <view class="section-header">
          <text class="header-title">现场照片：</text>
          <text class="required-mark" v-if="!isInspected">*</text>
        </view>
        <view class="photo-container">
          <view v-for="(img, idx) in imageList" :key="img" class="photo-item" style="position:relative;">
            <image class="photo-image" :src="img" mode="aspectFill" @click="previewImage(idx)"></image>
            <view style="position:absolute;top:0;right:0;z-index:2;" @click.stop="removePhoto(idx)" v-if="!isInspected">
              <text style="background:#fff;color:#f00;border-radius:50%;padding:0 10rpx;">×</text>
            </view>
          </view>
          <view class="photo-upload" @click="uploadPhoto" v-if="imageList.length < 9 && !isInspected">
            <text class="upload-icon">+</text>
            <text class="upload-text">上传图片</text>
          </view>
        </view>
      </view>

      <!-- Header -->
      <view class="form-header">
        <text class="header-title">巡检项：</text>
      </view>

      <!-- Inspection items -->
      <view v-for="(item, idx) in deviceItems" :key="item.id" class="inspection-item">
        <text class="item-label">{{ item.name }}</text>
        <view class="item-options">
          <button
            class="btn-status btn-normal"
            :class="{ selected: item.status === 1 }"
            @click="changeStatus(idx, 1)"
          >正常</button>
          <button
            class="btn-status btn-abnormal"
            :class="{ selected: item.status === 0 }"
            @click="changeStatus(idx, 0)"
          >异常</button>
        </view>
      </view>

      <!-- Problem description -->
      <view class="problem-section" v-if="(!isInspected && hasAbnormal) || (isInspected && question)">
        <view class="section-header">
          <text class="header-title">问题描述：</text>
          <text class="required-mark" v-if="!isInspected">*</text>
        </view>
        <view class="input-placeholder" :class="{ 'readonly': isInspected }">
          <textarea
            class="placeholder-text"
            v-model="question"
            placeholder="请输入问题描述"
            :disabled="isInspected"
            auto-height
          />
        </view>
      </view>

      <!-- Processing suggestion -->
      <view class="suggestion-section" v-if="(!isInspected && hasAbnormal) || (isInspected && suggestion)">
        <view class="section-header">
          <text class="header-title">处理建议：</text>
          <text class="required-mark" v-if="!isInspected">*</text>
        </view>
        <view class="input-placeholder" :class="{ 'readonly': isInspected }">
          <textarea
            class="placeholder-text"
            v-model="suggestion"
            placeholder="请输入处理建议"
            :disabled="isInspected"
            auto-height
          />
        </view>
      </view>

      <!-- debug 定位精度展示 -->
<!--      <view v-if="!isInspected && locationAccuracy" style="color:#888;font-size:24rpx;margin-bottom:10rpx;">-->
<!--        定位精度：{{ locationAccuracy }}米-->
<!--      </view>-->

      <!-- Submit button -->
      <view class="submit-section" v-if="!isInspected">
        <button v-if="needShare" class="btn-submit" @click="goBack">返回</button>
        <button v-else class="btn-submit" @click="submitForm">提交</button>
      </view>
    </view>
  </view>

  <uni-popup ref="popup" type="dialog" :mask-click="false">
    <view class="custom-modal">
      <view class="modal-header">
        <view class="close-btn" @click="closeModal">×</view>
        <text class="modal-title">分享给领导</text>
        <text class="modal-subtitle">请点击下方按钮将隐患信息分享给领导审批</text>
      </view>
      <view class="button-group">
        <button v-if="needShare" class="share-btn" open-type="share" @click="closeModal">
          <text>立即分享</text>
        </button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import api from '@/lib/api.js';
import urls from '@/lib/urls.js';
import { useUserStore } from '@/store/user.js';
import { mapState } from 'pinia';
import { alert } from "@/lib/utils";
import { BMapWX } from '@/lib/map/bmap-wx.js';
export default {
  data() {
    return {
      deviceRecordId: 0,
      isInspected: false, // 是否已巡检
      deviceItems: [], // 巡检项
      imageList: [],  // 现场照片（多图）
      question: '',   // 问题描述
      suggestion: '',  // 处理建议
      needShare: false, // 是否需要分享
      shareId: null,    // 分享用id
      longitude: '',
      latitude: '',
      locationAccuracy: '', // 新增：定位精度
      locationAddress: '', // 新增：定位详细地址
      canvasWidth: 300,
      canvasHeight: 300,
      isUploading: false, // 是否正在上传
      baiduMapConfig: null,
      bmap: null,
    }
  },
  computed: {
    ...mapState(useUserStore, ["user"]),
    hasAbnormal() {
      // 只要有一项为0即为异常
      return this.deviceItems.some(item => item.status === 0);
    }
  },
    onShow() {
      api.get(urls.baiduMapKey).then(res => {
          this.baiduMapConfig = res;
      }).catch(err => {
          console.log("获取地图配置失败", err)
      })
    },
  onLoad(options) {
    this.deviceRecordId = options.id;
    uni.setNavigationBarTitle({
      title: options.deviceName || '设备巡检详情'
    });

    try {
        api.get(urls.baiduMapKey).then(res => {
            console.log("获取地图配置成功", res)
            this.baiduMapConfig = res;
            this.bmap = new BMapWX(this.baiduMapConfig);
            // 页面加载即定位
            this.initLocation();
        }).catch(err => {
            console.log("获取地图配置失败", err)
        })
    } catch (e) {
      console.log(e)
    }
    this.loadDeviceItem();
  },
  methods: {
    showShareModal() {
      this.$refs.popup.open();
    },

    closeModal() {
      this.$refs.popup.close();
    },

    goBack() {
      uni.navigateBack();
    },

    loadDeviceItem() {
      api.get(urls.taskDeviceRecord(this.deviceRecordId)).then(res => {
        this.isInspected = res.isInspected || false;

        this.deviceItems = (res.deviceItems || []).map(item => {
          const newItem = {...item};
          if (newItem.status !== undefined && newItem.status !== null && newItem.status !== '') {
            newItem.status = Number(newItem.status);
          } else {
            newItem.status = ''; // 未选择状态
          }
          return newItem;
        });

        // 兼容 imageUrl 可能为 null 或字符串或数组
        if (Array.isArray(res.imageUrl)) {
          this.imageList = res.imageUrl;
        } else if (typeof res.imageUrl === 'string' && res.imageUrl) {
          this.imageList = [res.imageUrl];
        } else {
          this.imageList = [];
        }
        this.question = res.question || '';
        this.suggestion = res.suggestion || '';
      });
    },
    async initLocation() {
      try {
        await this.getLocation();
        // 获取详细地址
        if (this.longitude !== '未知' && this.latitude !== '未知') {
          try {
            this.locationAddress = await this.bmap.getAddressByLocation(this.latitude, this.longitude);
          } catch (e) {
            this.locationAddress = '';
          }
        }
      } catch (e) {
        // 定位失败
        this.locationAddress = '';
      }
    },
    async getLocation() {
      return new Promise((resolve, reject) => {
        uni.authorize({
          scope: 'scope.userLocation',
          success: () => {
            uni.getLocation({
              type: 'gcj02',
              altitude: true,
              isHighAccuracy: true,
              highAccuracyExpireTime: 3500,
              success: res => {
                console.log("定位成功", res)
                this.longitude = res.longitude;
                this.latitude = res.latitude;
                this.locationAccuracy = res.accuracy ? res.accuracy : '';
                resolve(res);
              },
              fail: err => {
                console.log("定位失败", err)
                uni.showToast({ title: '获取定位失败,请打开手机定位', icon: 'none' });
                // 定位失败时设置默认值，不影响上传
                this.longitude = '未知';
                this.latitude = '未知';
                resolve(null);
              }
            });
          },
          fail: () => {
            // 用户拒绝授权时，询问是否继续
            uni.showModal({
              title: '提示',
              content: '需要获取您的地理位置来添加水印，是否继续？',
              confirmText: '继续',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  // 用户选择继续，设置默认值
                  this.longitude = '未知';
                  this.latitude = '未知';
                  resolve(null);
                } else {
                  reject(new Error('用户取消操作'));
                }
              }
            });
          }
        });
      });
    },
    async uploadPhoto() {
      if (this.isUploading) {
        uni.showToast({ title: '正在上传中，请稍候', icon: 'none' });
        return;
      }
      if (this.isInspected) {
        console.log('已巡检，无法上传图片');
        return;
      }
      if (this.imageList.length >= 9) {
        uni.showToast({ title: '最多上传9张图片', icon: 'none' });
        return;
      }
      this.isUploading = true;
      uni.chooseImage({
        count: 1,
        sourceType: ['camera'],
        success: async (res) => {
          const filePath = res.tempFilePaths[0];
          try {
            await this.addWatermarkAndUpload(filePath);
            uni.showToast({ title: '上传成功', icon: 'success' });
          } catch (err) {
            uni.showToast({ title: err.message || '上传失败', icon: 'none' });
          } finally {
            this.isUploading = false;
            uni.hideLoading();
          }
        },
        fail: (err) => {
          uni.showToast({ title: '选择图片失败', icon: 'none' });
          this.isUploading = false;
        }
      });
    },
    async addWatermarkAndUpload(filePath) {
      uni.showLoading({ title: '图片处理中...' });
      return new Promise((resolve, reject) => {
        uni.getImageInfo({
          src: filePath,
          success: imgInfo => {
            // 中心等比压缩
            const maxWidth = 1284;
            const maxHeight = 2778;
            const scale = Math.min(maxWidth / imgInfo.width, maxHeight / imgInfo.height, 1);
            const drawWidth = Math.round(imgInfo.width * scale);
            const drawHeight = Math.round(imgInfo.height * scale);
            this.canvasWidth = drawWidth;
            this.canvasHeight = drawHeight;
            setTimeout(() => {
              const ctx = uni.createCanvasContext('watermarkCanvas', this);
              // 居中绘制
              const dx = 0;
              const dy = 0;
              ctx.drawImage(
                filePath,
                0, 0, imgInfo.width, imgInfo.height, // 源图全图
                dx, dy, drawWidth, drawHeight        // 目标canvas区域
              );
              // 水印内容
              const time = this.formatTime(new Date());
              const text1 = `时间: ${time}`;
              const text2 = `经度: ${this.longitude}`;
              const text3 = `纬度: ${this.latitude}`;
              const text4 = this.locationAddress ? `地址: ${this.locationAddress}` : '';
              ctx.setFontSize(20);
              const padding = 16;
              const lineHeight = 28;
              const texts = [text1, text2, text3];
              if (text4) texts.push(text4);
              const widths = texts.map(t => ctx.measureText(t).width);
              const bgWidth = Math.max(...widths) + padding * 2;
              const bgHeight = lineHeight * texts.length + padding * 2;
              const bgX = drawWidth - bgWidth - 20;
              const bgY = drawHeight - bgHeight - 20;
              ctx.setFillStyle('rgba(0,0,0,0.4)');
              ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
              ctx.setFillStyle('#fff');
              texts.forEach((t, i) => {
                ctx.fillText(t, bgX + padding, bgY + padding + lineHeight * (i + 0.8));
              });
              ctx.draw(false, () => {
                setTimeout(() => {
                  uni.canvasToTempFilePath({
                    canvasId: 'watermarkCanvas',
                    width: drawWidth,
                    height: drawHeight,
                    destWidth: drawWidth,
                    destHeight: drawHeight,
                    success: async res2 => {
                      try {
                        // 加水印后如图片体积超4MB则自动压缩
                        uni.getImageInfo({
                          src: res2.tempFilePath,
                          success: info2 => {
                            if (info2.size && info2.size > 4 * 1024 * 1024) {
                              // 压缩
                              uni.compressImage({
                                src: res2.tempFilePath,
                                quality: 40,
                                success: async compressRes => {
                                  uni.getImageInfo({
                                    src: compressRes.tempFilePath,
                                    success: async info3 => {
                                      if (info3.size && info3.size > 4 * 1024 * 1024) {
                                        uni.hideLoading();
                                        uni.showToast({ title: '图片过大，请重新拍摄', icon: 'none' });
                                        reject(new Error('图片过大'));
                                        return;
                                      }
                                      uni.hideLoading();
                                      uni.showLoading({ title: '图片上传中...' });
                                      const url = await this.uploadImageWithRetry(compressRes.tempFilePath);
                                      this.imageList.push(url);
                                      resolve(url);
                                    },
                                    fail: () => {
                                      uni.hideLoading();
                                      uni.showToast({ title: '图片压缩失败', icon: 'none' });
                                      reject(new Error('图片压缩失败'));
                                    }
                                  });
                                },
                                fail: () => {
                                  uni.hideLoading();
                                  uni.showToast({ title: '图片压缩失败', icon: 'none' });
                                  reject(new Error('图片压缩失败'));
                                }
                              });
                            } else {
                              uni.hideLoading();
                              uni.showLoading({ title: '图片上传中...' });
                              this.uploadImageWithRetry(res2.tempFilePath).then(url => {
                                this.imageList.push(url);
                                resolve(url);
                              }).catch(err => {
                                reject(err);
                              });
                            }
                          },
                          fail: () => {
                            uni.hideLoading();
                            uni.showToast({ title: '图片信息获取失败', icon: 'none' });
                            reject(new Error('图片信息获取失败'));
                          }
                        });
                      } catch (err) {
                        uni.hideLoading();
                        uni.showToast({ title: err.message || '图片上传失败', icon: 'none' });
                        reject(err);
                      }
                    },
                    fail: err => {
                      uni.hideLoading();
                      uni.showToast({ title: '水印处理失败', icon: 'none' });
                      reject(new Error('水印处理失败'));
                    }
                  }, this);
                }, 500);
              });
            }, 100);
          },
          fail: () => {
            uni.hideLoading();
            uni.showToast({ title: '图片信息获取失败', icon: 'none' });
            reject(new Error('图片信息获取失败'));
          }
        });
      });
    },
    async uploadImageWithRetry(filePath, retryCount = 0) {
      try {
        const res = await api.get(urls.getUploadConfig);
        return await new Promise((resolve, reject) => {
          const doUpload = (retryCount) => {
            uni.uploadFile({
              url: res.url,
              filePath: filePath,
              formData: res.form_params,
              name: res.name,
              success: uploadFileRes => {
                let data = JSON.parse(uploadFileRes.data);
                if (data?.url) {
                  resolve(data.url);
                } else {
                  reject(new Error(data.message || '上传失败'));
                }
              },
              fail: error => {
                if (retryCount < 2) {
                  setTimeout(() => {
                    doUpload(retryCount + 1);
                  }, 1000 * (retryCount + 1));
                } else {
                  reject(new Error(error.errMsg || '上传失败'));
                }
              }
            });
          };
          doUpload(retryCount);
        });
      } catch (err) {
        throw new Error(err.message || '上传失败');
      }
    },
    formatTime(date) {
      const y = date.getFullYear();
      const m = ('0' + (date.getMonth() + 1)).slice(-2);
      const d = ('0' + date.getDate()).slice(-2);
      const h = ('0' + date.getHours()).slice(-2);
      const min = ('0' + date.getMinutes()).slice(-2);
      const s = ('0' + date.getSeconds()).slice(-2);
      return `${y}-${m}-${d} ${h}:${min}:${s}`;
    },
    removePhoto(idx) {
      if (this.isInspected) return;
      this.imageList.splice(idx, 1);
    },
    changeStatus(index, status) {
      if (this.isInspected) return;
      this.deviceItems[index].status = status;
    },
    submitForm() {
      if (this.isInspected) return;
      // 校验所有巡检项都已选择
      const allSelected = this.deviceItems.every(item => item.status === 0 || item.status === 1);
      if (!allSelected) {
        uni.showToast({ title: '请完成所有巡检项选择', icon: 'none' });
        return;
      }
      // 校验照片
      if (!this.imageList.length) {
        uni.showToast({ title: '请上传现场照片', icon: 'none' });
        return;
      }
      // 如果有异常项，校验问题描述和处理建议
      if (this.hasAbnormal) {
        if (!this.question) {
          uni.showToast({ title: '请输入问题描述', icon: 'none' });
          return;
        }
        if (!this.suggestion) {
          uni.showToast({ title: '请输入处理建议', icon: 'none' });
          return;
        }
      }
      // 组装参数
      const params = {
        device_item_status: this.deviceItems.map(item => ({ id: item.id, status: item.status })),
        img: this.imageList,
        desc: this.question,
        suggest: this.suggestion
      };
      uni.showLoading({ title: '提交中...' });
      api.put(urls.fillInTaskDeviceRecordTable(this.deviceRecordId), params)
        .then((res) => {
          uni.hideLoading();
          if (res.needApprovalStatus) {
            this.needShare = true;
            this.shareId = res.approveId || this.deviceRecordId;
            this.showShareModal();
          } else {
            uni.showToast({
              title: '提交成功',
              icon: 'success',
              duration: 2000,
              success: () => {
                setTimeout(() => {
                  uni.navigateBack();
                }, 2000);
              }
            });
          }
        })
        .catch(() => {
          uni.hideLoading();
          uni.showToast({ title: '提交失败', icon: 'none' });
        });
    },
    previewImage(idx) {
      uni.previewImage({
        current: this.imageList[idx],
        urls: this.imageList
      });
    }
  },
  onShareAppMessage() {
    let id = this.shareId || this.deviceRecordId;
    if ((this.needShare && this.shareId) || this.question || this.suggestion) {
      return {
        title: this.user.nickname + ' 提交的审批',
        path: `/packageInspect/inspection/approve/detail?id=${id}`,
        imageUrl: "https://img.shiwusuo100.com/assets/app-static/share-img.png"
      }
    }
  }
}
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.nav-bar {
  display: flex;
  align-items: center;
  height: 88rpx;
  background-color: #fff;
  padding: 0 20rpx;
  position: relative;
}

.nav-left {
  width: 80rpx;
}

.nav-back {
  font-size: 36rpx;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 34rpx;
  font-weight: 500;
}

.nav-right {
  width: 120rpx;
  display: flex;
  justify-content: flex-end;
}

.nav-more, .nav-add {
  font-size: 40rpx;
  margin-left: 30rpx;
}

.inspection-form {
  flex: 1;
  padding: 20rpx 50rpx;
}

.form-header {
  margin: 20rpx 0;
}

.header-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.required-mark {
  color: #ff0000;
  margin-left: 5rpx;
}

.inspection-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-label {
  width: 160rpx;
  font-size: 30rpx;
  color: #333;
}

.item-options {
  display: flex;
  margin-left: auto; /* 这会将选项推到右侧，无论屏幕宽度如何 */
  justify-content: flex-end; /* 确保按钮从右侧开始排列 */
}

.btn-status {
  margin-right: 20rpx;
  height: 60rpx;
  min-width: 120rpx;
  font-size: 28rpx;
  padding: 0 20rpx;
  line-height: 60rpx;
  border-radius: 6rpx;
  border: none;
  background-color: #f0f0f0;
  color: #666;
}

.btn-status:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-normal.selected {
  background-color: #007aff;
  color: white;
}

.btn-abnormal.selected {
  background-color: #ff9500;
  color: white;
}

.photo-section, .problem-section, .suggestion-section {
  margin-top: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.photo-container {
  display: flex;
  flex-wrap: wrap;
}

.photo-item, .photo-upload {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.photo-image {
  width: 100%;
  height: 100%;
}

.photo-upload {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0;
  border: 1rpx dashed #ccc;
}

.upload-icon {
  font-size: 40rpx;
  color: #ccc;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 22rpx;
  color: #999;
}

.input-placeholder.readonly {
  background-color: #f8f8f8;
}

.placeholder-text {
  color: #333;
  font-size: 28rpx;
  width: 100%;
  min-height: 80rpx;
  padding: 10rpx 0;
  line-height: 1.4;
}

.input-placeholder {
  background-color: #fff;
  border-radius: 8rpx;
  min-height: 80rpx;
  display: flex;
  padding: 10rpx 20rpx;
  width: 100%;
}

.placeholder-text:disabled {
  color: #333;
  opacity: 1;
  background-color: transparent;
}

.submit-section {
  margin-top: 60rpx;
  padding: 0 20rpx;
}

.btn-submit {
  background-color: #007aff;
  color: white;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 8rpx;
}

.btn-warning {
  background-color: #fada3d;
  color: white;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 32rpx;
  border-radius: 8rpx;
}


  /* Modal styles */
  .custom-modal {
    background: #fff;
    border-radius: 12px;
    padding: 24px;
    max-width: 80%;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .modal-header {
    margin-bottom: 20px;
    text-align: center;
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #007aff;
    display: block;
    margin-bottom: 8px;
  }

  .modal-subtitle {
    font-size: 14px;
    color: #666;
    display: block;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    font-size: 20px;
    color: #999;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .close-btn:hover {
    color: #333;
  }

  .close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 24px;
    height: 24px;
    font-size: 20px;
    color: #999;
    text-align: center;
    line-height: 24px;
    cursor: pointer;
    transition: color 0.3s;
  }

  .close-btn:hover {
    color: #333;
  }

  .share-btn {
    background-color: #007aff;
    color: white;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    border-radius: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 24px;
    margin: 0 auto;
    transition: all 0.3s;
  }

  .share-btn:active {
    background-color: #0062cc;
    transform: scale(0.98);
  }

  .share-btn .iconfont {
    color: white;
    font-size: 18px;
    margin-right: 8px;
  }
</style>