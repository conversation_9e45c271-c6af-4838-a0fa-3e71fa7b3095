<template>
	<view class="container">
		<view class="header">
			<view class="title-container">
				<text class="title" :class="{'title-long': taskName.length > 10}">{{ taskName }}</text>
			</view>
			<view class="actions">
				<view class="action-btn compact" @click="toggleAllDates">
					{{ isAllExpanded ? '全部收起' : '全部展开' }}
				</view>
				<view class="action-btn compact" @click="toggleAbnormalFilter">
					{{ showOnlyAbnormal ? '显示全部' : '只看异常' }}
				</view>
			</view>
		</view>
		
		<view class="content">
			<view v-for="(dateItem, index) in filteredDateList" :key="dateItem.date" class="date-section">
				<view class="date-header" @click="toggleDateExpand(index)">
					<view class="date-info">
						<text class="date-text">{{ dateItem.date }}</text>
						<text class="date-count">({{ dateItem.devices.length }}个设备)</text>
					</view>
					<view class="arrow-icon">
						<view v-if="dateItem.expanded" class="arrow arrow-up"></view>
						<view v-else class="arrow arrow-down"></view>
					</view>
				</view>
				
				<view class="devices-list" v-if="dateItem.expanded">
					<view class="device-item" v-for="device in dateItem.devices" :key="device.id" @click="toDetail(device)">
						<view class="device-info">
							<view class="device-icon">
								<image v-if="device.imageUrl" class="device-image" :src="device.imageUrl" mode="aspectFill"></image>
          						<image v-else src="@/images/empty.png" mode="aspectFit"></image>
							</view>
							<view class="device-details">
								<view class="device-name">{{ device.name }}</view>
								<view class="device-count">异常总数: {{ device.abnormalCount }}</view>
							</view>
						</view>
						<view class="device-status-wrapper">
							<view class="device-status" :class="device.status">
								<view class="status-dot"></view>
								<text class="status-text">{{ device.status === 'normal' ? '正常' : '异常' }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<view class="empty-state" v-if="filteredDateList.length === 0">
				<text>没有符合条件的记录</text>
			</view>
		</view>
	</view>
</template>

<script>
import api from "@/lib/api.js";
import urls  from "@/lib/urls";

export default {
	data() {
		return {
			taskId: 0,
			taskName: '',
			dateList: [],
			showOnlyAbnormal: false,
			isAllExpanded: false
		}
	},

	onLoad(options) {
		if (!options.task_id || !options.task_name) {
			uni.showToast({
				title: '缺少任务参数',
				icon: 'error',
				duration: 2000
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 2000);
			return;
		}
		this.taskId = options.task_id;
		this.taskName = options.task_name;
		this.getList();
	},

	onPullDownRefresh() {
        this.getList().finally(() => {
            uni.stopPullDownRefresh();

			setTimeout(() => {
                uni.showToast({
					title: '刷新成功',
					icon: 'success'
				});
            }, 300);
        });
    },

	computed: {
		filteredDateList() {
			if (!this.showOnlyAbnormal) {
				return this.dateList;
			}
			// 只返回包含异常设备的日期
			return this.dateList.filter(dateItem => {
				return dateItem.devices.some(device => device.status === 'abnormal');
			});
		}
	},
	methods: {
		toDetail(device) {
			uni.navigateTo({
				url: '/packageInspect/inspection/approve/detail?id='+device.id
			});
		},
		getList() {
            return api.get(urls.taskDevicesRecordList, {task_id: this.taskId}).then(res => {
                // 转换接口数据为组件需要的格式
                this.dateList = Object.keys(res).map(date => {
                    return {
                        date: date,
                        expanded: false, // 默认不展开
                        devices: res[date].map(record => ({
                            id: record.id,	// 记录id
							deviceId: record.device_id,	// 设备id
                            name: record.device?.deleted_at ? record.device?.name+'(设备已删除)' : record.device?.name ?? '设备已删除',
                            abnormalCount: record.abnormal_count,
                            status: record.status === 1 ? 'normal' : 'abnormal',
                            imageUrl: record.device?.image_url,	// 设备图片
							sceneImage: record.image_url,	// 现场图片
                        }))
                    };
                });
                
                // 按日期降序排序
                this.dateList.sort((a, b) => new Date(b.date) - new Date(a.date));
                
                // 默认展开第一个日期
                if (this.dateList.length > 0) {
                    this.dateList[0].expanded = true;
                    this.isAllExpanded = false;
                }
            }).catch(err => {
                console.error('获取设备记录失败:', err);
            });
		},

		toggleDateExpand(index) {
			// 切换当前点击的日期展开状态
			const dateItem = this.filteredDateList[index];
			const originalIndex = this.dateList.findIndex(item => item.date === dateItem.date);
			this.dateList[originalIndex].expanded = !this.dateList[originalIndex].expanded;
		},
		toggleAllDates() {
			this.isAllExpanded = !this.isAllExpanded;
			this.dateList.forEach(item => {
				item.expanded = this.isAllExpanded;
			});
		},
		toggleAbnormalFilter() {
			this.showOnlyAbnormal = !this.showOnlyAbnormal;
		}
	},
	mounted() {
		// 初始化时只展开第一个日期
		this.dateList.forEach((item, index) => {
			item.expanded = index === 0;
		});
	}
}
</script>

<style>
.container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #fff;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 16px;
	background-color: #fff;
	border-bottom: 1px solid #f0f0f0;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	position: sticky;
	top: 0;
	z-index: 100;
	flex-wrap: nowrap;
}

.title-container {
	flex: 1;
	min-width: 0; /* 允许flex项目收缩到比内容更小 */
	margin-right: 10px;
	overflow: hidden;
}

.title {
	font-size: 18px;
	font-weight: 500;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: block;
}

.title-long {
	font-size: 16px;
}

.actions {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-shrink: 0; /* 防止按钮区域被压缩 */
}

.action-btn {
	padding: 6px 12px;
	border-radius: 4px;
	font-size: 14px;
	color: #666;
	background-color: #f5f5f5;
	border: 1px solid #e8e8e8;
	cursor: pointer;
	transition: all 0.3s ease;
}

.action-btn.compact {
	padding: 4px 8px;
	font-size: 12px;
	border-radius: 3px;
}

.action-btn:hover {
	background-color: #e8e8e8;
	color: #333;
}

.action-btn:active {
	background-color: #ddd;
}

.empty-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 40px 0;
	color: #999;
	font-size: 14px;
}

.content {
	flex: 1;
	overflow-y: auto;
	padding: 15px;
}

.date-section {
	margin-bottom: 10px;
}

.date-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	background-color: #f8f9fa;
	border-radius: 8px;
	cursor: pointer;
	transition: all 0.3s ease;
	margin-bottom: 2px;
}

.date-header:hover {
	background-color: #f0f2f5;
}

.date-header:active {
	background-color: #e9ecef;
}

.date-info {
	display: flex;
	align-items: baseline;
	gap: 8px;
}

.date-text {
	font-size: 16px;
	font-weight: 500;
	color: #2c3e50;
}

.date-count {
	font-size: 14px;
	color: #6c757d;
}

.arrow-icon {
	width: 20px;
	height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.arrow {
	width: 8px;
	height: 8px;
	border-right: 2px solid #6c757d;
	border-bottom: 2px solid #6c757d;
	transition: transform 0.3s ease;
}

.arrow-up {
	transform: rotate(-135deg);
}

.arrow-down {
	transform: rotate(45deg);
}

.devices-list {
	padding: 10px 15px;
	background-color: #fff;
	border-radius: 8px;
	margin-top: 2px;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.device-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px 0;
	border-bottom: 1px solid #f0f0f0;
}

.device-item:last-child {
	border-bottom: none;
}

.device-info {
	display: flex;
	align-items: center;
	flex: 1;
}

.device-icon {
	width: 60px;
	height: 60px;
	border-radius: 8px;
	overflow: hidden;
	margin-right: 15px;
	background-color: #f5f5f5;
}

.device-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.device-details {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.device-name {
	font-size: 16px;
	font-weight: 500;
	color: #2c3e50;
}

.device-count {
	font-size: 14px;
	color: #6c757d;
}

.device-status-wrapper {
	padding-left: 20px;
}

.device-status {
	display: flex;
	align-items: center;
	padding: 6px 12px;
	border-radius: 20px;
	font-size: 14px;
	font-weight: 500;
	gap: 6px;
}

.status-dot {
	width: 8px;
	height: 8px;
	border-radius: 50%;
}

.abnormal {
	background-color: #fff2f0;
	color: #cf1322;
}

.abnormal .status-dot {
	background-color: #cf1322;
}

.normal {
	background-color: #f0f9ff;
	color: #096dd9;
}

.normal .status-dot {
	background-color: #096dd9;
}

.date-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0;
	border-bottom: 1px solid #f0f0f0;
}

/* 引入字体图标（这里仅为示例，实际使用时需要引入正确的图标库） */
@font-face {
	font-family: 'iconfont';
	src: url('data:application/x-font-woff2;charset=utf-8;base64,...') format('woff2');
}

.iconfont {
	font-family: "iconfont" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-left:before {
	content: "\e697";
}

.icon-more:before {
	content: "\e6a7";
}

.icon-refresh:before {
	content: "\e6a4";
}

.icon-up:before {
	content: "\e6a5";
}

.icon-down:before {
	content: "\e6a6";
}

.icon-device:before {
	content: "\e6a8";
}
</style>