<template>
	<view v-if="navHeight > 0" class="information" :style="'padding-top: ' + barHeight + 'px;'">
		<!-- #ifdef MP-WEIXIN -->
		<view class="navbar custom-class" :style="'padding-top: ' + navHeight + 'px;'">
			<view class="search flex flex-between">
				<view class="atp-l flex flex-center">
					<view class="atpl-back" @click="goBack()">
						<image src="@/static/images/icon/arrow-left-s-line.png"></image>
					</view>
					<view class="atpl-tit">AI安全助手</view>
				</view>
				<view class="atp-r" @click="goHistory()">
					<image src="@/static/images/icon/menu-line.png"></image>
				</view>
			</view>
		</view>
		<!-- #endif	 -->
	</view>

</template>

<script>
export default {
	data() {
		return {
			barHeight: 0,
            navHeight: 0
		};
	},
	created() {
        //#ifdef MP-WEIXIN
        this.fixLayout();
        //#endif

	},
	methods: {
		goBack() {
			uni.navigateBack({
				delta: 2
			});
		},
		goHistory() {
			uni.navigateTo({
				url:'/packageA/ai/record'
			});
		},
		fixLayout() {
			let menuButtonObject = uni.getMenuButtonBoundingClientRect();

            this.barHeight = menuButtonObject.top;
            this.navHeight = menuButtonObject.top + menuButtonObject.height + 4;
		},
	}
};
</script>

<style>
.flex {
	display: flex;
}
.flex-between {
	justify-content: space-between;
}
.flex-center {
	align-items: center;
}
.information {
	background: #f3f3ff;
	/*padding: 0 30rpx 88rpx;*/
}
.atpl-back,
.atp-r {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.atpl-back image,
.atp-r image {
	width: 32rpx;
	height: 32rpx;
	vertical-align: top;
}
.atpl-back {
    width: 60rpx;
    height: 60rpx;
	border: 1px solid #dadada;
	border-radius: 100%;
	background-color: #fff;
}
.atp-r {
	width: 60rpx;
	height: 60rpx;
	border: 1px solid #dadada;
	border-radius: 100%;
	background-color: #fff;
}
.atpl-tit {
	font-size: 32rpx;
	font-weight: bold;
	padding-left: 20rpx;
}

/* #ifdef MP-WEIXIN */
.navbar {
	background-color: #f3f3ff;
	padding: 24rpx 30rpx;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 10;
}
.search {
	position: absolute;
	bottom: 31rpx;
	left: 30rpx;
	height: 64rpx;
	width: 500rpx;
}
.search .icon-search {
	margin-right: 12rpx;
}
.search text {
	width: 420rpx;
	height: 64rpx;
	line-height: 64rpx;
	font-size: 28rpx;
	color: #888;
}
/* #endif */

/* #ifdef APP-PLUS */
.navbar {
	background-color: #f3f3ff;
	padding: 24rpx 30rpx;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 10;
}
.search {
	height: 64rpx;
	line-height: 64rpx;
	border-radius: 62rpx;
	padding: 20rpx 16rpx;
	margin-top: 50rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.search .icon-search {
	margin-right: 12rpx;
}
.search text {
	width: 420rpx;
	height: 64rpx;
	line-height: 64rpx;
	font-size: 28rpx;
	color: #888;
}
/* #endif */
</style>
