<template>
	<view class="markdown-view">
		<mp-html :key="mpkey" :selectable="selectable" :scroll-table='scrollTable' :markdown="true" :content="html"></mp-html>
	</view>
</template>

<script>
	export default {
		name: 'markdown-view',
		props: {
			markdown: {
				type: String,
				default: ''
			},
			selectable: {
				type: [Boolean, String],
				default: true
			},
			scrollTable: {
				type: Boolean,
				default: true
			},
			themeColor: {
				type: String,
				default: '#007AFF'
			},
			codeBgColor: {
				type: String,
				default: '#2d2d2d'
			},
		},
		data() {
			return {
				html: '',
				tagStyle: '',
				mpkey: 'zero'
			};
		},
		watch: {
			markdown: function(val) {
				this.html = this.markdown
			}
		},
		created() {
			this.html = this.markdown
		},
		methods: {
		}
	};
</script>

<style>

</style>
