<template>
	<view class="content">
		<view class="ai-record-top">
			<view class="art-list" v-if="list.length > 0">
				<view class="artl-item" v-for="(item, index) in list" :key="item.id" :class="{'cur': item.id === selectId}">
					<view class="artl-tit" @click="go(item.id)">{{ item.title }}</view>
					<view class="artl-option">
						<view class="artl-o-btn" @click="show(index)">
							<image src="@/static/images/icon/pencil-line.png"></image>
						</view>
						<view class="artl-o-btn" @click="remove(item.id)">
							<image src="@/static/images/icon/delete-bin-line.png"></image>
						</view>
					</view>
				</view>
			</view>
			<view class="art-list" v-else>
				<uv-empty mode="history"  />
			</view>
		</view>
		<view class="ai-record-foot">
			<view class="arf-box">
				<view class="arf-add" @click="go(0)">
					<image src="@/static/images/icon/add-line.png"></image>
					<text>创建新的对话</text>
				</view>
				<view class="arf-delete" @click="remove(-1)">
					<image src="@/static/images/icon/delete-bin-line.png"></image>
				</view>
			</view>
		</view>


		<!-- 修改名称 -->
		<view class="ai-modelbox" v-if="display">
			<view class="aimb-part">
				<view class="aimb-part-box">
					<input class="uni-input" v-model="title" placeholder="请输入会话名称" />
					<view class="aimb-foot">
						<view class="aimbf-btn cancle" @click="close()">取消</view>
						<view class="aimbf-btn save" @click="update()">保存</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import api from "@/lib/api";
import { useUserStore } from "@/store/user";
import { mapState } from "pinia";

const app = getApp();

export default {
    data() {
        return {
			display: false,
			selectId: 0,
			selectIndex: 0,
			list: [],
			title: ''
        }
    },
    computed: {
        ...mapState(useUserStore, ['user', 'loaded']),
    },
    watch: {
        loaded(newValue) {
            if (newValue && this.user) {
                this.init();
            }
        }
    },
    onLoad() {
        if (this.user) {
            this.init();
        } else {
            uni.switchTab({
                url:'/pages/index/index'
            })
        }
    },
    onPullDownRefresh() {
        uni.stopPullDownRefresh();

        this.init();
    },
    methods: {
		init() {
            api.get('chat/sessions', {}).then(res => {
                this.list = res.data;
            }).catch(() => {});
		},
		show(index) {
			this.selectId = index;
			this.selectId = this.list[index].id;
			this.title = this.list[index].title;
			this.display = true;
		},
		close() {
			this.display = false;
			this.title = '';
		},
		go(id) {
			this.selectId = id;

			uni.navigateTo({
				url: '/packageA/ai/index?id=' + id
			});
		},
		update() {
			uni.showLoading({
				title: '提交中'
			});

			api.put('chat/sessions/' + this.selectId, {
				title: this.title
			}).then(res => {
				const index = this.list.findIndex(item => item.id === this.selectId);

				this.list[index].title = res.title;

				this.close();
			}).catch(e => {
				uni.showModal({
					title: '温馨提示',
					content: e.message,
					showCancel: false,
					confirmText: '我知道了'
				});
			}).finally(() => {
				uni.hideLoading();
			});
		},
		remove(id) {
			let title = '确定要删除该记录吗？';

			if (id < 0) {
				title = '确定要删除所有记录吗？';
			}

			uni.showModal({
				title: title,
				confirmText: '确定',
				cancelText: '取消',
				success: (e) => {
					if (e.confirm) {
						uni.showLoading({
							title: '提交中'
						});

						api.post('chat/sessions/remove', {
							id
						}).then(() => {
							this.init();
						}).catch(e => {
							uni.showModal({
								title: '温馨提示',
								content: e.message,
								showCancel: false,
								confirmText: '我知道了'
							});
						}).finally(() => {
							uni.hideLoading();
						});
					}
				}
			});
		}
    }
}
</script>


<style>
	page,
	.content {
		width: 100%;
		height: 100%;
    background-color: #f3f3ff;
	}
	.content {
		display: flex;
		flex-direction: column;
	}

	.art-list {
		padding: 30rpx;
	}

	.artl-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 12rpx;
	}
	.artl-tit {
		padding: 30rpx 20rpx;
	}

	.artl-option {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}

	.artl-o-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 80rpx;
		height: 80rpx;
	}
	.artl-o-btn image {
		width: 32rpx;
		height: 32rpx;
	}

	.artl-item.cur {
		background-color: #fff;
	}

	.ai-record-top {
		flex: 1;
		overflow-y: scroll;
	}

	.arf-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.ai-record-foot {
		padding-bottom: env(safe-area-inset-bottom);
	}
	.arf-box {
		padding: 0 30rpx;
	}
	.arf-add {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100rpx;
		background-color: #065CDF;
		color: #fff;
		border-radius: 12rpx;
	}
	.arf-add image {
		width: 36rpx;
		height: 36rpx;
		vertical-align: top;
	}
	.arf-add text {
		padding-left: 10rpx;
		padding-bottom: 4rpx;
	}
	.arf-delete {
		display: flex;
		align-items: center;
		flex-direction: column;
		justify-content: center;
		height: 100rpx;
		width: 100rpx;
		border: 1px solid #e7e7e7;
		border-radius: 12rpx;
		background-color: #fff;
		margin-left: 30rpx;
	}
	.arf-delete image {
		width: 36rpx;
		height: 36rpx;
		vertical-align: top;
	}


	.ai-modelbox {
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 20;
		background-color: rgba(0, 0, 0, .5);
	}
	.aimb-part {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		align-items: center;
		justify-content: space-around;
		background-color: #fff;
		padding-left: 30rpx;
		padding-right: 30rpx;
		padding-top: 20rpx;
		padding-bottom: env(safe-area-inset-bottom);
		border-radius: 30rpx 30rpx 0 0;
	}
	.aimbp-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0 20rpx;
	}
	.aimbp-item image {
		width: 64rpx;
		height: 64rpx;
	}
	.aimbp-item text {
		font-size: 28rpx;
		padding-top: 20rpx;
	}
	.aimb-part-box {
		flex: 1;
		padding-top: 16rpx;
	}
	.aimb-part .uni-input {
		height: 100rpx;
		border: 1px solid #e7e7e7;
		border-radius: 20rpx;
		padding-left: 20rpx;
	}
	.aimb-foot {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20rpx;
	}
	.aimbf-btn {
		height: 100rpx;
		line-height: 100rpx;
		border-radius: 20rpx;
		text-align: center;
	}
	.aimbf-btn.cancle {
		background-color: #F3F3F3;
		width: 200rpx;
		margin-right: 20rpx;
	}
	.aimbf-btn.save {
		flex: 1;
		background-color: #065CDF;
		color: #fff;
	}
</style>
