import api from "./api.js";
import {ContentViewLimit as cvl} from "@/lib/enums";

/**
 * @typedef {"wechat"|"alipay"} PaymentPlatform 支付平台
 * @typedef {"mp"} PaymentClient 支付客户端
 */

/**
 * 获取微信小程序的 openid
 * @return {Promise<String>}
 */
async function getWechatMiniAppOpenId() {
    return new Promise((resolve, reject) => {
        uni.login({
            provider: "weixin",
            success: res => {
                const code = res.code;
                api.post("login/wechat-mini-app/openid", {code})
                    .then(data => resolve(data.openid))
                    .catch(reject);
            }
        });
    });
}

/**
 * 将后端给到的 parameters 参数组合成 uni.requestPayment 的结构
 *
 * 有的平台参数是在 requestPayments 一级，有的是在 orderInfo 中，此函数抹平差异
 *
 * @param {PaymentPlatform} platform 支付平台
 * @param {PaymentClient} client 支付客户端
 * @param {Object} parameters 后端给到的支付参数
 */
function extractPaymentParameters(platform, client, parameters) {
    if (platform == "wechat" && client == "mp") {
        return parameters;
    } else {
        return {
            orderInfo: parameters
        };
    }
}

/**
 * 对订单发起支付
 *
 * 创建支付单，发起支付，并轮询最终支付结果
 *
 * @param {String} orderNo 订单编号
 * @param {PaymentPlatform} platform 支付平台
 * @param {PaymentClient} client 客户端
 * @return {Promise}
 */
export async function makePayment(orderNo, platform, client) {
    //我们平台名与 uni.requestPayment 的 provider 名称映射，名称相同不用映射
    const platformProviderMap = {
        wechat: "wxpay"
    };

    //验证支付平台
    const provider = await new Promise((resolve, reject) => {
        uni.getProvider({
            service: "payment",
            success(res) {
                const provider = platformProviderMap[platform] || platform;

                if (res.provider != provider) {
                    uni.hideLoading();
                    reject(new Error("无法发起支持，获取支付平台 " + platform + " 失败。"));
                    return;
                }

                resolve(provider);
            },
            fail(e) {
                reject(new Error(e.errMsg));
            }
        });
    });

    const extra = {};

    //微信小程序需要传入 openid
    if (platform == "wechat" && client == "mp") {
        extra.openid = await getWechatMiniAppOpenId();
    }

    //创建支付单
    const pay = await api.post("payments", {order_no: orderNo, platform, client, extra});

    //发起支付，此时有支付自己的加载状态，关闭页面的加载状态
    uni.hideLoading();

    await new Promise((resolve, reject) => {
        uni.requestPayment({
            provider,
            ...extractPaymentParameters(platform, client, pay.parameters),
            success: () => resolve(),
            fail: e => {
                if (e.errMsg.includes("cancel")) {
                    reject(new Error("支付已取消"));
                } else {
                    reject(new Error(e.errMsg))
                }
            }
        });
    });

    uni.showLoading({
        title: "等待支付结果"
    });

    //轮询订单状态
    const maxPulling = 24; //24 * 2.5 = 60秒
    let countPulling = 0;

    while (true) {
        await delay(2500);

        const payment = await api.get("payments/" + pay.payment.out_trade_no);

        if (payment.status == 1) {
            uni.hideLoading();
            return payment;
        }

        if (++countPulling >= maxPulling) {
            uni.hideLoading();
            throw new Error("查询支付结果超时，请稍候再试。");
        }
    }
}

/**
 * 异步延迟
 * @param {int} timeout
 * @return {Promise}
 */
async function delay(timeout) {
    return new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * 获取支付类型
 *
 * @param viewLimit
 * @returns {string[]|*[]}
 */
export function getAllowTypes(viewLimit) {
    if (viewLimit === cvl.credit) {
        return ['credit']
    } else if (viewLimit === cvl.amount) {
        return ['amount', 'balance']
    } else if (viewLimit === cvl.credit_amount) {
        return ['credit', 'amount', 'balance']
    } else {
        return []
    }
}
