// 统一管理所有接口路径

const urls = {
    // 百度地图key
    baiduMapKey: 'map-config',

    // -----  巡检模块  -----
    // 创建巡检任务
    createTask: 'inspect/task',
    // 巡检任务列表
    taskList: 'inspect/task',
    // 巡检任务关联新设备
    assocDevice: (taskID) => `inspect/assoc-device/${taskID}`,
    // 巡检任务记录表
    taskDevicesRecordTable: (taskID) => `inspect/task-devices-record-table/${taskID}`,
    // 巡检任务设备记录列表
    taskDevicesRecordList: 'inspect/task-devices-records',
    // 巡检任务审批列表
    taskDevicesRecordApproval: (taskID) => `inspect/task-devices-record-approval/${taskID}`,
    // 巡检任务设备巡检记录详情
    taskDeviceRecord: (taskDeviceRecordID) => `inspect/task-device-record/${taskDeviceRecordID}`,
    // 填写巡检任务设备项记录表
    fillInTaskDeviceRecordTable: (taskDeviceRecordID) => `inspect/fill-in-task-device-record-table/${taskDeviceRecordID}`,
    // 设备列表
    deviceList: 'inspect/devices',
    // 获取设备详情
    deviceDetail: (deviceID) => `inspect/devices/${deviceID}`,
    // 编辑设备
    editDevice: (deviceID) => `inspect/devices/${deviceID}`,
    // 删除设备
    deleteDevice: (deviceID) => `inspect/devices/${deviceID}`,
    // 获取设备巡检项列表
    deviceItemListByDeviceID: (deviceID) => `inspect/device-items?device_id=${deviceID}`,
    // 巡检项列表
    deviceItemList: 'inspect/device-items',
    // 获取审批列表
    approvalList: 'inspect/approval-list',
    // 获取审批详情
    approvalDetail: (taskDeviceRecordID) => `inspect/approval-detail/${taskDeviceRecordID}`,
    // 审批
    approval: (taskDeviceRecordID) => `inspect/approval/${taskDeviceRecordID}`,
    // 获取上传配置
    getUploadConfig: 'inspect/devices/upload-config',
    // ai识图
    aiIdentify: 'inspect/devices/ai',

    // 随手拍-我的
    hiddenDanger: 'inspect/hidden-danger',
    // 随手拍-新增
    createHiddenDanger: 'inspect/hidden-danger',
    // 随手拍-隐患大厅, type： top太震惊 down没什么 mine我发布的
    hiddenDangerHall: 'inspect/hidden-danger-hall',
    // 随手拍-隐患详情, type： mine来自我的 hall来自大厅
    hiddenDangerDetail: (id) => `inspect/hidden-danger/${id}`,
    // 随手拍-隐患审批
    hiddenDangerApproval: (id) => `inspect/hidden-danger/${id}`,
    // 随手拍-隐患处理，amazed顶赞, no_amazed踩赞, lock设为私密或公开, delete删除
    handleHiddenDanger: (id) => `inspect/handle-hidden-danger/${id}`,
    // 随手拍-新增客户线索
    addClue: (id) => `inspect/hidden-danger/add-clue/${id}`,

    // 机构报名首页
    enroll: (sid) => `orgs/${sid}/enroll`,
    // 报名支付
    enrollBuyOrder: (id) => `orgs/enroll/${id}/buy-order`,
    // 报名申请退款
    enrollApplyRefund: (id) => `orgs/enroll/${id}/apply-refund`,
    // 报名撤销申请退款
    enrollCancelRefund: (id) => `orgs/enroll/${id}/cancel-refund`,
    // 报名退款详情
    enrollRefundDetail: (id) => `orgs/enroll/${id}/refund`,
    // 用户在机构下的报名状态
    enrollmentStatus: (orgId) => `orgs/enroll/${orgId}/status`,
    // 报名记录开具发票
    enrollInvoice: (id) => `orgs/enroll/${id}/invoice`,

};

export default urls;