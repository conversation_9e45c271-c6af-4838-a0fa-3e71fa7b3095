/**
 * 接口请求模块
 *
 * 此模块不能依赖任何其它模块，否则相互引用可能起冲突
 *
 * @typedef {{method?: "HEAD"|"GET"|"POST"|"PUT"|"DELETE", url?: String, query?: Map<String, String>, data?: Object, headers?: Map<String, String>, throwOnError?: <PERSON><PERSON><PERSON>, jumpLogin: Boolean, waitBoot: Boolean}} RequestOptions 请求选项
 * @typedef {{code: Number, data?: Object, message?: String, headers?: Map<String, String>, successful: Boolean}} Response 请求响应结果
 */
import { apiBaseUrl, loginUrl } from './../config/index.js';
import { getVisitorToken, isBooted, onBooted } from './bootstrap.js';
import qs from 'qs';

let accessToken = uni.getStorageSync('access_token');

// let authorizeTimer = null;

/**
 * 错误异常
 */
class ApiError extends Error {

    /**
     * @param {String} message 错误信息
     * @param {Number} code 状态码
     * @param {Map<String, String>} headers 头信息
     */
    constructor(message, code, headers) {
        super(message);
        this.code = code;
        this.headers = headers;
        // 设置错误堆栈跟踪信息
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, ApiError);
        }
    }

    /**
     * 获取返回的头信息
     *
     * @param {String} name 头信息
     * @return {String|undefined}
     */
    header(name) {
        return fromHeader(this.headers, name);
    }

}

export function getUrl(uri) {
    return apiBaseUrl + uri;
}

export function buildQuery(obj) {
    const arr = [];
    for (let i in obj) {
        if (obj.hasOwnProperty(i)) {
            arr.push(i + '=' + encodeURIComponent(obj[i]));
        }
    }
    return arr.join('&');
}

/**
 * 获取头字段值（不区分来源大小）
 *
 * @param {Object} header 头信息对象
 * @param {String} key 要获取的头，请输入正常的大小写格式，如 User-Agent
 * @return {String|undefined}
 */
function fromHeader(header, key) {
    return header[key] || header[key.toLowerCase()]; //http2 头为小写时需要兼容
}

/**
 * 基础请求封装
 *
 * @param {String} uri
 * @param {RequestOptions} options
 * 微信接口要求 method 必须为大写
 * @return {Promise<Response>}
 * @throws {ApiError}
 */
function request(uri, options={}) {
    //UniApp 请求对象
    const obj = {
        method: options.method || "GET",
        url: options.url || getUrl(uri),
        header: {},
        enableHttp2: true
    };

    if (options.query) {
        obj.url = obj.url + (obj.url.includes("?") ? "&" : "?") + buildQuery(options.query);
    }

    if (accessToken) {
        obj.header['Authorization'] = 'Bearer ' + accessToken;
    }
    obj.header['X-Platform'] = 'wechat applet';

    if ((obj.method == "POST" || obj.method == "PUT") && options.data) {
        obj.header['Content-Type'] = 'application/x-www-form-urlencoded';
        obj.data = options.data instanceof Object ? qs.stringify(options.data) : options.data;
    }

    const makeRequest = () => {
        let visitorToken = getVisitorToken();

        if (visitorToken) {
            obj.header['X-Visitor-Token'] = visitorToken;
        }

        return new Promise((resolve, reject) => {
            obj.success = (res) => {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    resolve({
                        code: res.statusCode,
                        headers: res.header,
                        data: res.data,
                        successful: true
                    });
                } else if (res.statusCode >= 400 && res.statusCode < 600) {
                    //开启 jumpLogin 时，遇到 401 会自动跳转登录地址，默认为 false
                    if (res.statusCode == 401 && options.jumpLogin) {
                        uni.hideLoading();
                        uni.showToast({
                            title: "您需要登录才能继续"
                        });

                        uni.navigateTo({
                            url: loginUrl
                        });
                    }

                    //开启 throwOnError 时，遇难到 400 以上响应将抛出 ApiError，默认为 true
                    const throwOnError = options.throwOnError == undefined ? true : options.throwOnError;

                    if (throwOnError) {
                        reject(new ApiError(res.data.message, res.statusCode, res.header));
                    } else {
                        resolve({
                            code: res.statusCode,
                            headers: res.header,
                            message: res.data.message,
                            successful: false
                        });
                    }

                } else {
                    reject(new ApiError("请求状态异常", res.statusCode, res.header));
                }
            };

            obj.fail = (err) => {
                const message = err ? err.errMsg : "请求失败，请稍候再试。";
                reject(new ApiError(message, 0, {}));
            };

            uni.request(obj);
        });
    };

    //等待全局启动完成才进行请求，默认 true
    const waitBoot = options.waitBoot == undefined ? true : options.waitBoot;

    if (waitBoot && !isBooted()) {
        return new Promise((resolve, reject) => onBooted(() => resolve(makeRequest())));
    }


    return makeRequest();
}

/**
 * 简易请求
 *
 * 与 request 的区别是简易请求直接返回数据，而 request 返回完整的响应对象
 *
 * @param {String} uri
 * @param {RequestOptions} options
 * @return {Promise}
 */
function simpleRequest(uri, options) {
    return request(uri, options).then(res => res.data);
}

export default {
    request,
    get(uri, query) {
        return simpleRequest(uri, {
            query
        });
    },
    post(uri, data) {
        return simpleRequest(uri, {
            method: 'POST',
            data
        });
    },
    put(uri, data) {
        return simpleRequest(uri, {
            method: 'PUT',
            data
        });
    },
    delete(uri, data) {
        return simpleRequest(uri, {
            method: 'DELETE',
            data
        });
    }
};

export function getToken() {
    return accessToken;
}

export function hasToken() {
    return !!accessToken;
}

export function setToken(token) {
    if (token) {
        accessToken = token;
        uni.setStorageSync('access_token', accessToken);
    } else {
        accessToken = null;
        uni.removeStorageSync('access_token');
    }
}

