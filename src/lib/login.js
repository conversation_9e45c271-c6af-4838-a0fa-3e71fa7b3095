import { useUserStore} from "@/store/user";
import { watch } from "vue";

/**
 * 要求登录
 *
 * @param {Boolean} ask 跳转登录页前是否询问用户，默认为 true
 * @returns {Promise} 登录成功后进入 resolved 状态，未成功则进入 rejected 状态
 */
export function loginRequired(ask = true) {
    return new Promise((resolve, reject) => {
        let url;
        const userStore = useUserStore()
        // #ifdef MP-WEIXIN || H5
        url = "/pages/login/index";
        //#endif
        console.log(userStore.user)
        if (userStore.user) {
            resolve()
        } else {
            if (ask) {
                uni.showModal({
                    title: "尚未登录",
                    content: "请登录后体验完整功能~",
                    confirmText: "去登录",
                    success: res =>{
                        if (res.confirm) {
                            uni.navigateTo({ url });
                        }
                    }
                });
            } else {
                uni.navigateTo({ url });
            }
        }
    });
}

/**
 * 从登录页返回
 *
 * 不管登录页跳转了多少层，都会回到登录页之前的页面
 */
export function loginReturn() {
    const pages = getCurrentPages();

    const l = pages.length;

    let delta = 0;

    let from = l - 1;

    while (pages[from] && pages[from].route.includes('/login/')) {
        ++delta;
        --from;
    }

    if (pages[from]) {
        uni.navigateBack({
            delta
        });
    } else {
        uni.reLaunch({
            url: "/pages/index/index"
        });
    }
}

/**
 * 确保初始化完成后获取当前登录的用户信息，未登录返回 null
 *
 * 为什么要有此函数？当前用户是否登录是由 bootstrap 进行初始化时处理的。
 * 而业务的代码想根据当前用户是否登录来决定后续的流程，必须等初始化完成后才能确定，但业务代码的运行时机有时并不确定，某些情况下可能会跑在初始化的前头。
 * 那怎么保证拿到的用户信息一定是初始化后的？此函数就做了这个保证。
 *
 * @return {Promise<import("@/store/user").User|null>}
 */
export async function getUser() {
    const userStore = useUserStore();
    if (userStore.loaded) {
        return userStore.user;
    } else {
        return new Promise((resolve) => {
            const stopWatch = watch(
                () => userStore.loaded,
                (loaded, prev) => {
                    if (loaded) {
                        resolve(userStore.user);
                        stopWatch();
                    }
                }
            );
        });
    }
}
