import api from "./api.js";
import { getUser } from "./login.js";

let firstTabBarPageLoaded = false;

/**
 * 检查 TabBar 红点
 */
function checkTips() {
    getUser().then(u => {
        if (u) {
            api.get("pages/me").then(res => {
                if (res.answer_tip){
                    uni.setTabBarBadge({
                        index: 3, // 人脉页面在底部菜单栏的索引
                        text: "1", // 要显示的文本（必须是字符串类型）
                    });
                }
            })
        }
    }, e => {
        //ignore
    });
}

/**
 * 在 TabBar 的页面初次加载时运行
 *
 * 适用于所有 TabBar 初次加载时共用的业务逻辑，比如设 TabBar 红点，弹窗等。
 */
export function tabBarPageLoad() {
    //只在第一个 TabBar 页面加载时执行
    if (!firstTabBarPageLoaded) {
        firstTabBarPageLoaded = true;
        checkTips();
    }

    //下面的代码在每个 TabBar 页面加载时都执行
}

export function tabBarPageShow() {}
