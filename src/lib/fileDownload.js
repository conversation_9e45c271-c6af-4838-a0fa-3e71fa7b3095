const MAX_STORAGE_SIZE = 200 * 1024 * 1024; // 小程序存储空间上限，默认200MB，根据实际情况调整

// 检查文件是否存在于临时目录
function checkFileExist(filePath) {
    return new Promise((resolve, reject) => {
        const fsm = uni.getFileSystemManager();
        fsm.access({
            path: filePath,
            success: () => resolve(true),
            fail: () => resolve(false)
        });
    });
}

// 获取文件信息，包括大小和时间
function getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
        const fsm = uni.getFileSystemManager();
        fsm.stat({
            path: filePath,
            success: (res) => resolve(res.stats),
            fail: (err) => reject(err)
        });
    });
}

// 获取临时目录下所有文件信息
async function getTempFilesInfo() {
    const fsm = uni.getFileSystemManager();
    const tempDir = uni.env.USER_DATA_PATH;

    return new Promise((resolve, reject) => {
        fsm.readdir({
            dirPath: tempDir,
            success: async (res) => {
                const filesInfo = [];
                for (const fileName of res.files) {
                    const filePath = `${tempDir}/${fileName}`;
                    try {
                        const stats = await getFileInfo(filePath);
                        if (stats.isFile()) {
                            filesInfo.push({
                                path: filePath,
                                name: fileName,
                                size: stats.size,
                                createTime: stats.lastModifiedTime
                            });
                        }
                    } catch (err) {
                        console.error(`获取文件信息失败: ${fileName}`, err);
                    }
                }
                resolve(filesInfo);
            },
            fail: (err) => reject(err)
        });
    });
}

// 删除文件
function deleteFile(filePath) {
    return new Promise((resolve, reject) => {
        const fsm = uni.getFileSystemManager();
        fsm.unlink({
            filePath,
            success: () => resolve(),
            fail: (err) => reject(err)
        });
    });
}

// 主函数：下载并处理文件
async function downloadAndHandleFile(url, filePath) {
    try {
        // 检查文件是否已存在
        const fileExists = await checkFileExist(filePath);
        if (fileExists) {
            console.log('文件已存在，直接打开');
            // 这里添加打开文件的代码，根据文件类型使用不同的打开方式
            openFile(filePath);
            return;
        }

        // 获取要下载的文件大小
        const downloadFileSize = await getDownloadFileSize(url);

        // 获取当前临时目录所有文件信息
        const tempFiles = await getTempFilesInfo();
        const currentTotalSize = tempFiles.reduce((sum, file) => sum + file.size, 0);
        // 检查是否需要清理空间
        if (currentTotalSize + downloadFileSize > MAX_STORAGE_SIZE) {
            console.log('存储空间不足，需要清理旧文件');

            // 按创建时间排序
            tempFiles.sort((a, b) => a.createTime - b.createTime);

            // 删除旧文件直到有足够空间
            let freedSpace = 0;
            for (const file of tempFiles) {
                if (currentTotalSize + downloadFileSize - freedSpace <= MAX_STORAGE_SIZE) {
                    break;
                }
                await deleteFile(file.path);
                freedSpace += file.size;
                console.log(`删除文件: ${file.name}, 大小: ${(file.size/1024/1024).toFixed(2)}MB`);
            }
        }

        // 开始下载文件
        await downloadFile(url, filePath);

        // 下载完成后打开文件
        openFile(filePath);

    } catch (err) {
        console.error('处理文件失败:', err);
        throw err;
    }
}

// 获取下载文件大小
function getDownloadFileSize(url) {
    return new Promise((resolve, reject) => {
        uni.request({
            url,
            method: 'HEAD',
            success: (res) => {
                console.log(res.header['Content-Length'])
                const fileSize = parseInt(res.header['Content-Length'] || 0);
                resolve(fileSize);
            },
            fail: (err) => reject(err)
        });
    });
}

// 下载文件
function downloadFile(url, filePath) {
    return new Promise((resolve, reject) => {
        const downloadTask = uni.downloadFile({
            url,
            filePath,
            success: (res) => {
                if (res.statusCode === 200) {
                    resolve(res.tempFilePath);
                } else {
                    reject(new Error(`下载失败，状态码: ${res.statusCode}`));
                }
            },
            fail: (err) => {
                uni.hideLoading();
                uni.showModal({
                    title: '下载文件失败',
                    content: err.errMsg,
                    showCancel: false
                });
            }
        });

        // 可以添加下载进度监听
        downloadTask.onProgressUpdate((res) => {
            uni.showLoading({
                title: '加载中 ' + res.progress + '%',
                mask: true
            });
        });
    });
}

// 打开文件
function openFile(filePath) {
    uni.openDocument({
        filePath,
        showMenu: true,
        success: () => {},
        fail: err => {
            uni.showModal({
                title: '打开文件失败',
                content: err.errMsg,
                showCancel: false
            });
        }
    });
}

// 使用示例
export async function handleFileDownload(url, filePath) {
    try {
        uni.showLoading({
            title: '处理中...'
        });
        await downloadAndHandleFile(url, filePath);
        uni.hideLoading();
    } catch (err) {
        uni.hideLoading();
        uni.showToast({
            title: '处理失败',
            icon: 'none'
        });
    }
}